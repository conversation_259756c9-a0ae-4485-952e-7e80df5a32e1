const mongoose = require("mongoose");

const MONGODB_URI =
  process.env.MONGODB_URI ||
  "****************************************************************";

async function fixSessionUserId() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB successfully");

    const sessionId = "689e8ac358caf361f78ce110";
    const correctUserId = "687fe3b739da96aef19699d3";

    // Update the session's userId to be a proper ObjectId
    const result = await mongoose.connection.db
      .collection("sessions")
      .updateOne(
        { _id: new mongoose.Types.ObjectId(sessionId) },
        { $set: { userId: new mongoose.Types.ObjectId(correctUserId) } }
      );

    if (result.modifiedCount > 0) {
      console.log("✅ Session userId fixed!");
    } else {
      console.log("❌ No changes made - session not found or already correct");
    }
  } catch (error) {
    console.error("❌ Error fixing session userId:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

fixSessionUserId();

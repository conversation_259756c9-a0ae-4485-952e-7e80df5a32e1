"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { useHydratedAuth } from "@/hooks/useHydratedAuth";
import { useAuthStore } from "@/store/authStore";
import { counselorAPI } from "@/lib/counselor";
import {
  Counselor,
  COUNSELOR_SPECIALIZATIONS,
  SESSION_TYPES,
  LANGUAGES,
} from "@/types/counselor";
import Header from "@/components/layout/Header";

export default function CounselorProfilePage() {
  const params = useParams();
  const counselorId = params.id as string;

  const { checkAuth, isReady, isLoading } = useHydratedAuth();
  const [counselor, setCounselor] = useState<Counselor | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get tokens from auth store if needed
  const { tokens } = useAuthStore();
  const token = tokens?.accessToken;

  useEffect(() => {
    if (isReady) {
      checkAuth();
    }
  }, [checkAuth, isReady]);

  useEffect(() => {
    if (isReady && counselorId) {
      fetchCounselor();
    }
  }, [counselorId, isReady]);

  const fetchCounselor = async () => {
    try {
      setProfileLoading(true);
      setError(null);
      // Pass token only if available, API should work without it for public profiles
      const response = await counselorAPI.getCounselor(counselorId, token);
      setCounselor(response.data.counselor);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load counselor profile"
      );
    } finally {
      setProfileLoading(false);
    }
  };

  const formatRating = (rating: number) => {
    return "★".repeat(Math.floor(rating)) + "☆".repeat(5 - Math.floor(rating));
  };

  const formatPrice = (rate: number, currency: string) => {
    const symbol = currency === "NGN" ? "₦" : "$";
    return `${symbol}${rate.toLocaleString()}`;
  };

  const getSpecializationInfo = (specialization: string) => {
    return COUNSELOR_SPECIALIZATIONS.find((s) => s.value === specialization);
  };

  const getSessionTypeInfo = (sessionType: string) => {
    return SESSION_TYPES.find((s) => s.value === sessionType);
  };

  const getLanguageInfo = (language: string) => {
    return LANGUAGES.find((l) => l.value === language);
  };

  if (isLoading || profileLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <svg
              className="w-12 h-12 text-red-400 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Error</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/counselors"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Counselors
            </Link>
          </div>
        </main>
      </div>
    );
  }

  if (!counselor) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Counselor not found
            </h1>
            <p className="text-gray-600 mb-4">
              This counselor profile may have been removed or you don't have
              access.
            </p>
            <Link
              href="/counselors"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Counselors
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link
                href="/counselors"
                className="text-gray-500 hover:text-gray-700"
              >
                Counselors
              </Link>
            </li>
            <li>
              <svg
                className="flex-shrink-0 h-5 w-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </li>
            <li>
              <span className="text-gray-900 font-medium">
                {counselor.user?.firstName} {counselor.user?.lastName}
              </span>
            </li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Profile Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Profile Header */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <div className="flex items-start space-x-6">
                {/* Profile Picture */}
                <div className="flex-shrink-0">
                  {counselor.profile.profilePicture ||
                  counselor.user?.profilePicture ? (
                    <img
                      src={
                        counselor.profile.profilePicture ||
                        counselor.user?.profilePicture
                      }
                      alt={`${counselor.user?.firstName} ${counselor.user?.lastName}`}
                      className="w-24 h-24 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-purple-100 flex items-center justify-center">
                      <span className="text-2xl font-semibold text-purple-600">
                        {counselor.user?.firstName?.charAt(0)}
                        {counselor.user?.lastName?.charAt(0)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Basic Info */}
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        {counselor.user?.firstName} {counselor.user?.lastName}
                      </h1>

                      {/* Rating and Reviews */}
                      {counselor.statistics.totalReviews > 0 && (
                        <div className="flex items-center space-x-3 mb-3">
                          <span className="text-yellow-400 text-lg">
                            {formatRating(counselor.statistics.averageRating)}
                          </span>
                          <span className="text-gray-600">
                            {counselor.statistics.averageRating.toFixed(1)} (
                            {counselor.statistics.totalReviews} reviews)
                          </span>
                        </div>
                      )}

                      {/* Experience */}
                      <p className="text-gray-600 mb-3">
                        {counselor.experience.years} years of experience
                      </p>

                      {/* Status */}
                      <div className="flex items-center space-x-4">
                        {counselor.settings.acceptingNewClients ? (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg
                              className="w-3 h-3 mr-1"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clipRule="evenodd"
                              />
                            </svg>
                            Accepting New Clients
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            Not Accepting New Clients
                          </span>
                        )}

                        {counselor.verification.status === "approved" && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <svg
                              className="w-3 h-3 mr-1"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clipRule="evenodd"
                              />
                            </svg>
                            Verified Professional
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* About */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                About
              </h2>
              <p className="text-gray-700 leading-relaxed mb-6">
                {counselor.bio}
              </p>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Therapeutic Approach
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {counselor.profile.approachDescription}
                </p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Experience
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {counselor.experience.description}
                </p>
              </div>
            </div>

            {/* Specializations */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Specializations
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {counselor.specializations.map((specialization) => {
                  const specInfo = getSpecializationInfo(specialization);
                  return (
                    <div
                      key={specialization}
                      className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg"
                    >
                      <span className="text-2xl">{specInfo?.icon}</span>
                      <span className="font-medium text-gray-900">
                        {specInfo?.label || specialization}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Qualifications */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Qualifications & Licenses
              </h2>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Education
                </h3>
                <div className="space-y-3">
                  {counselor.qualifications.map((qual, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <svg
                        className="w-5 h-5 text-purple-600 mt-0.5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                      </svg>
                      <div>
                        <p className="font-medium text-gray-900">
                          {qual.degree}
                        </p>
                        <p className="text-gray-600">
                          {qual.institution} • {qual.year}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Professional Licenses
                </h3>
                <div className="space-y-3">
                  {counselor.licenses.map((license, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <svg
                        className="w-5 h-5 text-green-600 mt-0.5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <div>
                        <p className="font-medium text-gray-900">
                          {license.type}
                        </p>
                        <p className="text-gray-600">
                          License #{license.number} • {license.issuingAuthority}
                        </p>
                        <p className="text-sm text-gray-500">
                          Expires:{" "}
                          {new Date(license.expiryDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Pricing Card */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Session Pricing
                </h3>

                <div className="text-center mb-6">
                  <p className="text-3xl font-bold text-gray-900">
                    {formatPrice(
                      counselor.pricing.ratePerMinute,
                      counselor.pricing.currency
                    )}
                    <span className="text-lg font-normal text-gray-500">
                      /min
                    </span>
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Minimum {counselor.pricing.minimumSessionDuration} minutes
                  </p>
                  <p className="text-sm text-gray-500">
                    From{" "}
                    {formatPrice(
                      counselor.pricing.ratePerMinute *
                        counselor.pricing.minimumSessionDuration,
                      counselor.pricing.currency
                    )}{" "}
                    per session
                  </p>
                </div>

                {/* Session Types */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Available Session Types
                  </h4>
                  <div className="space-y-2">
                    {counselor.profile.sessionTypes.map((sessionType) => {
                      const typeInfo = getSessionTypeInfo(sessionType);
                      return (
                        <div
                          key={sessionType}
                          className="flex items-center space-x-2"
                        >
                          <span className="text-lg">{typeInfo?.icon}</span>
                          <span className="text-sm text-gray-700">
                            {typeInfo?.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Languages */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Languages</h4>
                  <div className="flex flex-wrap gap-2">
                    {counselor.profile.languages.map((language) => {
                      const langInfo = getLanguageInfo(language);
                      return (
                        <span
                          key={language}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {langInfo?.label || language}
                        </span>
                      );
                    })}
                  </div>
                </div>

                {/* Book Session Button */}
                {counselor.settings.acceptingNewClients ? (
                  <Link
                    href={`/counselors/${counselor._id}/book`}
                    className="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 transition-colors font-medium text-center block"
                  >
                    Book a Session
                  </Link>
                ) : (
                  <button
                    disabled
                    className="w-full bg-gray-300 text-gray-500 py-3 px-4 rounded-md cursor-not-allowed font-medium"
                  >
                    Not Accepting New Clients
                  </button>
                )}
              </div>

              {/* Statistics */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Statistics
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Sessions</span>
                    <span className="font-medium text-gray-900">
                      {counselor.statistics.totalSessions}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Completion Rate</span>
                    <span className="font-medium text-gray-900">
                      {counselor.statistics.completionRate}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Response Time</span>
                    <span className="font-medium text-gray-900">
                      {counselor.statistics.responseTime < 60
                        ? `${counselor.statistics.responseTime}m`
                        : `${Math.floor(
                            counselor.statistics.responseTime / 60
                          )}h`}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

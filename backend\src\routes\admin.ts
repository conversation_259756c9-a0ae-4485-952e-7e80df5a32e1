import { Router, Request, Response } from "express";
import { paramValidations } from "../utils/validation";
import { authenticate, requireAdmin } from "../middleware/auth";
import { validateRequest } from "../middleware/validateRequest";

const router = Router();

// Placeholder routes - will be implemented in admin panel task
router.get(
  "/counselors/pending",
  authenticate,
  requireAdmin,
  (req: Request, res: Response) => {
    res.json({
      message: "Get pending counselors endpoint - to be implemented",
    });
  }
);

router.put(
  "/counselors/:id/approve",
  authenticate,
  requireAdmin,
  paramValidations.objectId("id"),
  validateRequest,
  (req: Request, res: Response) => {
    res.json({ message: "Approve counselor endpoint - to be implemented" });
  }
);

router.get(
  "/sessions/reports",
  authenticate,
  requireAdmin,
  (req: Request, res: Response) => {
    res.json({ message: "Get session reports endpoint - to be implemented" });
  }
);

export default router;

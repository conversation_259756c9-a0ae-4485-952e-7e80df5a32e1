const mongoose = require("mongoose");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// Import the Resource model (using the same structure as backend)
const resourceSchema = new mongoose.Schema({
  title: String,
  description: String,
  content: String,
  type: {
    type: String,
    enum: ["article", "video", "audio", "tool", "worksheet", "guide"],
  },
  category: String,
  subcategory: String,
  tags: [String],
  difficulty: { type: String, enum: ["beginner", "intermediate", "advanced"] },
  estimatedReadTime: Number,
  estimatedDuration: Number,
  media: {
    thumbnailUrl: String,
    videoUrl: String,
    audioUrl: String,
    downloadUrl: String,
    fileSize: Number,
  },
  author: {
    name: String,
    credentials: String,
    bio: String,
    profilePicture: String,
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
  },
  accessibility: {
    hasTranscript: { type: Boolean, default: false },
    hasClosedCaptions: { type: Boolean, default: false },
    isScreenReaderFriendly: { type: Boolean, default: true },
    alternativeFormats: [String],
  },
  engagement: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
  },
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  publishedAt: Date,
  createdBy: mongoose.Schema.Types.ObjectId,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Resource = mongoose.model("Resource", resourceSchema);

// Sample resources with different types
const sampleResources = [
  {
    title: "Introduction to Mindfulness Meditation Video",
    description:
      "Learn the basics of mindfulness meditation with this comprehensive video guide.",
    content: `<h2>Getting Started with Mindfulness</h2>
    <p>Mindfulness meditation is a powerful practice that can help reduce stress, improve focus, and enhance overall well-being.</p>
    <h3>What You'll Learn:</h3>
    <ul>
      <li>Basic breathing techniques</li>
      <li>Proper meditation posture</li>
      <li>How to handle distracting thoughts</li>
      <li>Building a daily practice</li>
    </ul>`,
    type: "video",
    category: "mindfulness",
    tags: ["meditation", "mindfulness", "stress-relief"],
    difficulty: "beginner",
    estimatedDuration: 15,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",
      videoUrl:
        "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
      fileSize: 1048576,
    },
    author: {
      name: "Dr. Sarah Chen",
      credentials: "PhD in Psychology",
      bio: "Expert in mindfulness and meditation practices.",
    },
    seo: {
      metaTitle: "Mindfulness Meditation Video Guide",
      metaDescription:
        "Learn mindfulness meditation with this beginner-friendly video guide.",
      keywords: ["mindfulness", "meditation", "video", "beginner"],
      slug: "mindfulness-meditation-video",
    },
    engagement: {
      views: 1523,
      likes: 234,
      averageRating: 4.7,
      totalRatings: 156,
    },
    createdBy: new mongoose.Types.ObjectId(),
  },
  {
    title: "Calming Nature Sounds for Sleep",
    description:
      "A 30-minute audio track featuring gentle rain and forest sounds.",
    content: `<h2>Peaceful Sleep with Nature</h2>
    <p>This audio track combines gentle rainfall with forest ambience to create the perfect sleep environment.</p>
    <h3>Features:</h3>
    <ul>
      <li>30 minutes of continuous audio</li>
      <li>High-quality nature recordings</li>
      <li>No sudden loud sounds</li>
    </ul>`,
    type: "audio",
    category: "sleep",
    tags: ["sleep", "relaxation", "nature-sounds"],
    difficulty: "beginner",
    estimatedDuration: 30,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      fileSize: 2097152,
    },
    author: {
      name: "Sound Therapy Institute",
      credentials: "Certified Sound Therapists",
      bio: "Specialists in therapeutic audio experiences.",
    },
    seo: {
      metaTitle: "Nature Sounds for Sleep - 30 Min Audio",
      metaDescription: "Fall asleep naturally with calming nature sounds.",
      keywords: ["nature sounds", "sleep", "audio", "relaxation"],
      slug: "nature-sounds-sleep-audio",
    },
    engagement: {
      views: 892,
      likes: 167,
      averageRating: 4.9,
      totalRatings: 78,
    },
    createdBy: new mongoose.Types.ObjectId(),
  },
  {
    title: "Daily Mood Tracking Worksheet",
    description:
      "A comprehensive worksheet to help you track your daily moods and identify patterns.",
    content: `<h2>Track Your Emotional Journey</h2>
    <p>This mood tracking worksheet helps you become more aware of your emotional patterns.</p>
    <h3>What's Included:</h3>
    <ul>
      <li>30-day mood tracking calendar</li>
      <li>Emotional awareness exercises</li>
      <li>Pattern identification guide</li>
      <li>Reflection prompts</li>
    </ul>`,
    type: "worksheet",
    category: "mental-health",
    tags: ["mood-tracking", "self-awareness", "worksheet"],
    difficulty: "beginner",
    estimatedReadTime: 5,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=600&fit=crop",
      downloadUrl:
        "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      fileSize: 524288,
    },
    author: {
      name: "Dr. Michael Rodriguez",
      credentials: "Licensed Clinical Psychologist",
      bio: "Specialist in cognitive behavioral therapy.",
    },
    seo: {
      metaTitle: "Daily Mood Tracking Worksheet - Free Download",
      metaDescription: "Download this comprehensive mood tracking worksheet.",
      keywords: ["mood tracking", "worksheet", "mental health"],
      slug: "daily-mood-tracking-worksheet",
    },
    engagement: {
      views: 2156,
      likes: 345,
      averageRating: 4.6,
      totalRatings: 203,
    },
    createdBy: new mongoose.Types.ObjectId(),
  },
  {
    title: "Anxiety Assessment Tool",
    description:
      "An interactive self-assessment tool to understand your anxiety levels.",
    content: `<h2>Understanding Your Anxiety</h2>
    <p>This interactive tool uses validated assessments to help understand anxiety patterns.</p>
    <h3>Assessment Features:</h3>
    <ul>
      <li>Comprehensive questionnaire</li>
      <li>Immediate scoring and results</li>
      <li>Personalized recommendations</li>
      <li>Progress tracking</li>
    </ul>`,
    type: "tool",
    category: "mental-health",
    tags: ["anxiety", "assessment", "interactive"],
    difficulty: "intermediate",
    estimatedDuration: 10,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop",
      downloadUrl:
        "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      fileSize: 1048576,
    },
    author: {
      name: "Theramea Clinical Team",
      credentials: "Licensed Mental Health Professionals",
      bio: "Team of psychologists and therapists.",
    },
    seo: {
      metaTitle: "Anxiety Assessment Tool - Interactive Self-Assessment",
      metaDescription: "Take this comprehensive anxiety assessment tool.",
      keywords: ["anxiety assessment", "mental health tool", "self-assessment"],
      slug: "anxiety-assessment-tool",
    },
    engagement: {
      views: 3421,
      likes: 567,
      averageRating: 4.8,
      totalRatings: 432,
    },
    createdBy: new mongoose.Types.ObjectId(),
  },
];

async function createDiverseResources() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Insert resources one by one to avoid conflicts
    for (let i = 0; i < sampleResources.length; i++) {
      const resource = sampleResources[i];
      const createdResource = await Resource.create(resource);
      console.log(
        `✅ Created ${resource.type.toUpperCase()}: "${createdResource.title}"`
      );
      console.log(`   - Slug: ${createdResource.seo.slug}`);
      console.log(`   - ID: ${createdResource._id}`);
      console.log("");
    }

    console.log("🎉 All diverse resources created successfully!");
    console.log("\nYou can now test different resource types:");
    console.log(
      "• Video: http://localhost:3000/resources/mindfulness-meditation-video"
    );
    console.log(
      "• Audio: http://localhost:3000/resources/nature-sounds-sleep-audio"
    );
    console.log(
      "• Worksheet: http://localhost:3000/resources/daily-mood-tracking-worksheet"
    );
    console.log(
      "• Tool: http://localhost:3000/resources/anxiety-assessment-tool"
    );
  } catch (error) {
    console.error("❌ Error creating resources:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

createDiverseResources();

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: {
    code?: string;
    message: string;
    details?: any;
    stack?: string;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  meta?: {
    timestamp: string;
    requestId?: string;
    version?: string;
  };
}

// Pagination types
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter types
export interface BaseFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  isActive?: boolean;
  createdBy?: string;
}

export interface UserFilters extends BaseFilters {
  role?: 'user' | 'counselor' | 'admin';
  isEmailVerified?: boolean;
  location?: string;
}

export interface CounselorFilters extends BaseFilters {
  specialization?: string;
  verificationStatus?: 'pending' | 'verified' | 'rejected';
  rating?: number;
  availability?: boolean;
  experience?: number;
}

export interface SessionFilters extends BaseFilters {
  status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  counselorId?: string;
  clientId?: string;
  sessionType?: 'individual' | 'group' | 'emergency';
}

export interface ChatRoomFilters extends BaseFilters {
  category?: string;
  topic?: string;
  isModerated?: boolean;
  hasAvailableSlots?: boolean;
}

export interface PaymentFilters extends BaseFilters {
  status?: 'pending' | 'completed' | 'failed' | 'refunded';
  method?: 'card' | 'bank' | 'ussd' | 'transfer';
  currency?: 'NGN' | 'USD';
  amountMin?: number;
  amountMax?: number;
}

// Request body types
export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  role?: 'user' | 'counselor';
  areasOfInterest?: string[];
  location?: {
    country?: string;
    city?: string;
  };
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  username?: string;
  email?: string;
  areasOfInterest?: string[];
  location?: {
    country?: string;
    city?: string;
  };
  profilePicture?: string;
  preferences?: {
    notifications?: {
      email?: boolean;
      push?: boolean;
      sessionReminders?: boolean;
      chatMessages?: boolean;
    };
    privacy?: {
      showOnlineStatus?: boolean;
      allowDirectMessages?: boolean;
    };
  };
}

export interface CreateSessionRequest {
  counselorId: string;
  scheduledAt: string;
  duration: number;
  sessionType: 'individual' | 'group' | 'emergency';
  notes?: string;
  isUrgent?: boolean;
  preferredLanguage?: string;
}

export interface UpdateSessionRequest {
  scheduledAt?: string;
  duration?: number;
  notes?: string;
  status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  counselorNotes?: string;
  rating?: number;
  feedback?: string;
}

export interface CreateChatRoomRequest {
  name: string;
  description: string;
  category: string;
  topic: string;
  maxParticipants?: number;
  isModerated?: boolean;
  rules?: string[];
  tags?: string[];
  settings?: {
    allowAnonymous?: boolean;
    requireApproval?: boolean;
    allowFileSharing?: boolean;
    maxMessageLength?: number;
  };
  schedule?: {
    isScheduled: boolean;
    startTime?: string;
    endTime?: string;
    recurringPattern?: 'daily' | 'weekly' | 'monthly';
  };
}

export interface SendMessageRequest {
  content: string;
  messageType?: 'text' | 'image' | 'file';
  replyTo?: string;
  attachments?: {
    type: 'image' | 'file';
    url: string;
    filename: string;
    size: number;
  }[];
}

// Analytics types
export interface AnalyticsRequest {
  period: 'day' | 'week' | 'month' | 'quarter' | 'year';
  startDate?: string;
  endDate?: string;
  metrics?: string[];
  groupBy?: string;
}

export interface DashboardStats {
  users: {
    total: number;
    active: number;
    new: number;
    growth: number;
  };
  counselors: {
    total: number;
    verified: number;
    pending: number;
    averageRating: number;
  };
  sessions: {
    total: number;
    completed: number;
    scheduled: number;
    revenue: number;
  };
  platform: {
    revenue: number;
    growth: number;
    conversionRate: number;
  };
}

// File upload types
export interface FileUploadRequest {
  file: Express.Multer.File;
  folder?: string;
  allowedTypes?: string[];
  maxSize?: number;
}

export interface FileUploadResponse {
  url: string;
  publicId: string;
  filename: string;
  size: number;
  format: string;
  resourceType: string;
}

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Search types
export interface SearchRequest {
  query: string;
  type?: 'users' | 'counselors' | 'sessions' | 'chatrooms' | 'resources';
  filters?: any;
  pagination?: PaginationOptions;
}

export interface SearchResult<T> {
  results: T[];
  total: number;
  query: string;
  suggestions?: string[];
  facets?: {
    [key: string]: {
      value: string;
      count: number;
    }[];
  };
}

// Notification types
export interface NotificationRequest {
  userId: string;
  type: 'email' | 'push' | 'in_app';
  title: string;
  message: string;
  data?: any;
  scheduledAt?: string;
}

export interface BulkNotificationRequest {
  userIds: string[];
  type: 'email' | 'push' | 'in_app';
  title: string;
  message: string;
  data?: any;
  filters?: {
    role?: string;
    location?: string;
    preferences?: any;
  };
}

// Report types
export interface ReportRequest {
  targetType: 'user' | 'counselor' | 'message' | 'session' | 'chatroom';
  targetId: string;
  reason: string;
  description: string;
  evidence?: {
    screenshots?: string[];
    logs?: string[];
    additionalInfo?: any;
  };
}

export interface ReportResponse {
  reportId: string;
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTo?: string;
  estimatedResolution?: string;
}

// Webhook types
export interface WebhookPayload {
  event: string;
  data: any;
  timestamp: string;
  signature?: string;
}

export interface WebhookResponse {
  received: boolean;
  processed: boolean;
  message?: string;
  error?: string;
}

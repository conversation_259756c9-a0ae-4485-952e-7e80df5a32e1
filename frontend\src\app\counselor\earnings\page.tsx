'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { counselorPortalAPI } from '@/lib/counselorPortal';
import CounselorLayout from '@/components/counselor/CounselorLayout';
import EarningsChart from '@/components/counselor/EarningsChart';
import PayoutHistory from '@/components/counselor/PayoutHistory';
import { 
  CurrencyDollarIcon, 
  BanknotesIcon, 
  ChartBarIcon,
  CalendarIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';

interface EarningsData {
  totalEarnings: number;
  monthlyEarnings: number;
  pendingPayouts: number;
  completedPayouts: number;
  currency: string;
  earningsHistory: {
    month: string;
    earnings: number;
    sessions: number;
  }[];
  recentPayouts: {
    id: string;
    amount: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    date: string;
    method: string;
  }[];
}

export default function EarningsPage() {
  const router = useRouter();
  const { isAuthenticated, user, tokens } = useAuthStore();
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'counselor') {
      router.push('/auth/login');
      return;
    }

    fetchEarningsData();
  }, [isAuthenticated, user, router, selectedPeriod]);

  const fetchEarningsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await counselorPortalAPI.getEarnings(selectedPeriod, tokens!.accessToken);
      setEarningsData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load earnings data');
    } finally {
      setLoading(false);
    }
  };

  const handleExportEarnings = async () => {
    try {
      const response = await counselorPortalAPI.exportEarnings(selectedPeriod, tokens!.accessToken);
      
      // Create download link
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `earnings-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export earnings');
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  if (loading) {
    return (
      <CounselorLayout>
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </CounselorLayout>
    );
  }

  if (!earningsData) {
    return (
      <CounselorLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No earnings data available</h3>
          <p className="text-gray-600">Complete some sessions to see your earnings.</p>
        </div>
      </CounselorLayout>
    );
  }

  return (
    <CounselorLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-600 mr-3" />
                Earnings & Payouts
              </h1>
              <p className="mt-2 text-gray-600">
                Track your earnings and manage payout preferences
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as 'week' | 'month' | 'year')}
                className="border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="year">This Year</option>
              </select>
              <button
                onClick={handleExportEarnings}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Earnings Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(earningsData.totalEarnings, earningsData.currency)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(earningsData.monthlyEarnings, earningsData.currency)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BanknotesIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Payouts</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(earningsData.pendingPayouts, earningsData.currency)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed Payouts</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(earningsData.completedPayouts, earningsData.currency)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Earnings Chart */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Earnings Trend
          </h2>
          <EarningsChart 
            data={earningsData.earningsHistory}
            currency={earningsData.currency}
            period={selectedPeriod}
          />
        </div>

        {/* Payout History */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Recent Payouts
            </h2>
            <button
              onClick={() => router.push('/counselor/payouts')}
              className="text-purple-600 hover:text-purple-700 text-sm font-medium"
            >
              View all →
            </button>
          </div>
          <PayoutHistory 
            payouts={earningsData.recentPayouts}
            currency={earningsData.currency}
          />
        </div>

        {/* Payout Settings */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Payout Settings
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">Automatic Payouts</h3>
                <p className="text-sm text-gray-600">
                  Receive payouts automatically every week
                </p>
              </div>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Configure
              </button>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">Bank Account</h3>
                <p className="text-sm text-gray-600">
                  Update your bank account details for payouts
                </p>
              </div>
              <button 
                onClick={() => router.push('/counselor/profile')}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Update
              </button>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">Tax Information</h3>
                <p className="text-sm text-gray-600">
                  Download tax documents and statements
                </p>
              </div>
              <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Download
              </button>
            </div>
          </div>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">💰 Earnings Tips</h3>
          <ul className="text-sm text-blue-800 space-y-2">
            <li>• Maintain consistent availability to maximize booking opportunities</li>
            <li>• Provide excellent service to encourage repeat bookings and referrals</li>
            <li>• Update your profile regularly to attract new clients</li>
            <li>• Consider offering different session types to reach more clients</li>
            <li>• Keep track of your earnings for tax purposes</li>
          </ul>
        </div>
      </div>
    </CounselorLayout>
  );
}

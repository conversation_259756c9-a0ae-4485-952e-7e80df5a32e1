"use client";

import { useAuthStore } from "@/store/authStore";
import Link from "next/link";

export default function UpgradePromptWidget() {
  const { isGuest, isAuthenticated } = useAuthStore();

  if (!isGuest || isAuthenticated) return null;

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            Want to book a session? 📅
          </h3>
          <p className="text-blue-700 text-sm">
            Create a free account to book 1-on-1 sessions, save chat history,
            and get personalized recommendations.
          </p>
        </div>
        <Link
          href="/auth/signup"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
        >
          Create Account
        </Link>
      </div>
    </div>
  );
}

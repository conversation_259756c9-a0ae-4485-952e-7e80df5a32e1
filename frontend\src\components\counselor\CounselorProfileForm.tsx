"use client";

import { useState } from "react";
import { Counselor } from "@/types/counselor";

interface CounselorProfileFormProps {
  counselor: Counselor;
  onSave: (data: Partial<Counselor>) => Promise<void>;
  isLoading?: boolean;
}

export default function CounselorProfileForm({
  counselor,
  onSave,
  isLoading,
}: CounselorProfileFormProps) {
  const [formData, setFormData] = useState({
    bio: counselor.bio || "",
    specializations: counselor.specializations || [],
    profile: {
      languages: counselor.profile?.languages || [],
      approachDescription: counselor.profile?.approachDescription || "",
      sessionTypes: counselor.profile?.sessionTypes || [],
    },
    experience: {
      years: counselor.experience?.years || 0,
      description: counselor.experience?.description || "",
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
  };

  const handleSpecializationChange = (specialization: string) => {
    setFormData((prev) => ({
      ...prev,
      specializations: prev.specializations.includes(specialization)
        ? prev.specializations.filter((s) => s !== specialization)
        : [...prev.specializations, specialization],
    }));
  };

  const commonSpecializations = [
    "Anxiety",
    "Depression",
    "Relationship Issues",
    "Stress Management",
    "Trauma/PTSD",
    "Family Therapy",
    "Addiction",
    "Grief & Loss",
    "Career Counseling",
    "Life Transitions",
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Bio
        </label>
        <textarea
          value={formData.bio}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, bio: e.target.value }))
          }
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="Tell clients about yourself and your approach to therapy..."
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Specializations
        </label>
        <div className="grid grid-cols-2 gap-2">
          {commonSpecializations.map((spec) => (
            <label key={spec} className="flex items-center">
              <input
                type="checkbox"
                checked={formData.specializations.includes(spec)}
                onChange={() => handleSpecializationChange(spec)}
                className="mr-2"
              />
              <span className="text-sm">{spec}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Years of Experience
        </label>
        <input
          type="number"
          value={formData.experience.years}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              experience: {
                ...prev.experience,
                years: parseInt(e.target.value) || 0,
              },
            }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          min="0"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Experience Description
        </label>
        <textarea
          value={formData.experience.description}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              experience: { ...prev.experience, description: e.target.value },
            }))
          }
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="Describe your professional experience..."
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Therapeutic Approach
        </label>
        <textarea
          value={formData.profile.approachDescription}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              profile: { ...prev.profile, approachDescription: e.target.value },
            }))
          }
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="Describe your therapeutic approach and methods..."
        />
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
      >
        {isLoading ? "Saving..." : "Save Profile"}
      </button>
    </form>
  );
}

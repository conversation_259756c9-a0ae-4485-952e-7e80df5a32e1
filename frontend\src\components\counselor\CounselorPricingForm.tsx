"use client";

import { useState } from "react";
import { Counselor } from "@/types/counselor";

interface CounselorPricingFormProps {
  counselor: Counselor;
  onSave: (data: Partial<Counselor>) => Promise<void>;
  isLoading?: boolean;
}

export default function CounselorPricingForm({
  counselor,
  onSave,
  isLoading,
}: CounselorPricingFormProps) {
  const [formData, setFormData] = useState({
    pricing: {
      currency: counselor.pricing?.currency || ("NGN" as "NGN" | "USD"),
      ratePerMinute: counselor.pricing?.ratePerMinute || 0,
      minimumSessionDuration: counselor.pricing?.minimumSessionDuration || 30,
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
  };

  const calculateSessionPrice = (duration: number) => {
    return (formData.pricing.ratePerMinute * duration).toFixed(2);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Currency
        </label>
        <select
          value={formData.pricing.currency}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              pricing: {
                ...prev.pricing,
                currency: e.target.value as "NGN" | "USD",
              },
            }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value="NGN">Nigerian Naira (NGN)</option>
          <option value="USD">US Dollar (USD)</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Rate per Minute ({formData.pricing.currency})
        </label>
        <input
          type="number"
          value={formData.pricing.ratePerMinute}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              pricing: {
                ...prev.pricing,
                ratePerMinute: parseFloat(e.target.value) || 0,
              },
            }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          min="0"
          step="0.01"
          placeholder="0.00"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Minimum Session Duration (minutes)
        </label>
        <select
          value={formData.pricing.minimumSessionDuration}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              pricing: {
                ...prev.pricing,
                minimumSessionDuration: parseInt(e.target.value),
              },
            }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value={15}>15 minutes</option>
          <option value={30}>30 minutes</option>
          <option value={45}>45 minutes</option>
          <option value={60}>60 minutes</option>
        </select>
      </div>

      {formData.pricing.ratePerMinute > 0 && (
        <div className="bg-gray-50 p-4 rounded-md">
          <h4 className="font-medium text-gray-900 mb-2">Pricing Preview</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <div>
              30 min session: {formData.pricing.currency}{" "}
              {calculateSessionPrice(30)}
            </div>
            <div>
              45 min session: {formData.pricing.currency}{" "}
              {calculateSessionPrice(45)}
            </div>
            <div>
              60 min session: {formData.pricing.currency}{" "}
              {calculateSessionPrice(60)}
            </div>
          </div>
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
      >
        {isLoading ? "Saving..." : "Save Pricing"}
      </button>
    </form>
  );
}

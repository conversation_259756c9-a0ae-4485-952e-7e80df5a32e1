"use client";

import { useState } from "react";
import { Session } from "@/types/counselor";
import {
  CalendarIcon,
  ClockIcon,
  UserIcon,
  VideoCameraIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

interface SessionCardProps {
  session: Session;
  onAction: (
    sessionId: string,
    action: "approve" | "reject" | "cancel",
    reason?: string
  ) => void;
}

export default function SessionCard({ session, onAction }: SessionCardProps) {
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [cancellationReason, setCancellationReason] = useState("");

  const getSessionTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <VideoCameraIcon className="h-5 w-5" />;
      case "audio":
        return <PhoneIcon className="h-5 w-5" />;
      case "chat":
        return <ChatBubbleLeftRightIcon className="h-5 w-5" />;
      default:
        return <VideoCameraIcon className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === "NGN" ? "₦" : "$";
    return `${symbol}${amount.toLocaleString()}`;
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString("en-US", {
        weekday: "short",
        year: "numeric",
        month: "short",
        day: "numeric",
      }),
      time: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
      }),
    };
  };

  const handleReject = () => {
    if (rejectionReason.trim()) {
      onAction(session._id, "reject", rejectionReason);
      setShowRejectModal(false);
      setRejectionReason("");
    }
  };

  const handleCancel = () => {
    if (cancellationReason.trim()) {
      onAction(session._id, "cancel", cancellationReason);
      setShowCancelModal(false);
      setCancellationReason("");
    }
  };

  const { date, time } = formatDateTime(session.scheduledAt);

  return (
    <>
      <div className="p-6 hover:bg-gray-50 transition-colors">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {session.user?.firstName} {session.user?.lastName}
                </h3>
                <p className="text-sm text-gray-600">{session.user?.email}</p>
              </div>
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                  session.status
                )}`}
              >
                {session.status}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <CalendarIcon className="h-4 w-4" />
                <span>{date}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <ClockIcon className="h-4 w-4" />
                <span>
                  {time} ({session.duration} min)
                </span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                {getSessionTypeIcon(session.type)}
                <span className="capitalize">{session.type} session</span>
              </div>
            </div>

            {session.notes?.counselorNotes && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-1">
                  Counselor Notes:
                </h4>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  {session.notes.counselorNotes}
                </p>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                <span className="font-medium">Fee: </span>
                {formatCurrency(
                  session.pricing.totalAmount,
                  session.pricing.currency
                )}
              </div>
            </div>
          </div>

          <div className="flex-shrink-0 ml-6">
            {session.status === "scheduled" && (
              <div className="flex space-x-2">
                <button
                  onClick={() =>
                    window.open(`/session/${session._id}`, "_blank")
                  }
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  Join Session
                </button>
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  Cancel
                </button>
              </div>
            )}

            {session.status === "completed" && (
              <div className="text-sm text-gray-500">
                Completed on{" "}
                {new Date(
                  session.videoSession.endedAt || session.updatedAt
                ).toLocaleDateString()}
              </div>
            )}

            {session.status === "cancelled" && (
              <div className="text-sm text-red-600">Cancelled</div>
            )}
          </div>
        </div>
      </div>

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Reject Session Request
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Please provide a reason for rejecting this session request:
              </p>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={4}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter rejection reason..."
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectionReason("");
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReject}
                  disabled={!rejectionReason.trim()}
                  className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Reject Session
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Cancel Session
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Please provide a reason for cancelling this session:
              </p>
              <textarea
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
                rows={4}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter cancellation reason..."
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => {
                    setShowCancelModal(false);
                    setCancellationReason("");
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Keep Session
                </button>
                <button
                  onClick={handleCancel}
                  disabled={!cancellationReason.trim()}
                  className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel Session
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

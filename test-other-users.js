const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

async function testOtherUsers() {
  const usersToTry = [
    {
      email: "<EMAIL>",
      passwords: ["password123", "TestPassword123", "test123"],
    },
    {
      email: "<EMAIL>",
      passwords: ["password123", "John123", "john123"],
    },
    {
      email: "<EMAIL>",
      passwords: ["password123", "admin123", "Admin123"],
    },
  ];

  for (const user of usersToTry) {
    for (const password of user.passwords) {
      try {
        console.log(
          `\n=== Testing ${user.email} with password: ${password} ===`
        );

        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: user.email,
          password: password,
        });

        if (loginResponse.data.success) {
          console.log("✅ Login successful!");
          console.log(
            `User: ${loginResponse.data.data.user.firstName} ${loginResponse.data.data.user.lastName}`
          );
          console.log(`User ID: ${loginResponse.data.data.user._id}`);
          console.log(`Role: ${loginResponse.data.data.user.role}`);

          // If this is an admin, they might be able to access any session
          if (
            loginResponse.data.data.user.role === "superadmin" ||
            loginResponse.data.data.user.role === "admin"
          ) {
            console.log(
              "\n🔑 This is an admin user! Testing payment access..."
            );
            const authToken = loginResponse.data.data.tokens.accessToken;
            const sessionId = "689e8ac358caf361f78ce110";

            try {
              const paymentResponse = await axios.post(
                `${API_BASE_URL}/sessions/${sessionId}/payment/initialize`,
                {},
                {
                  headers: {
                    Authorization: `Bearer ${authToken}`,
                    "Content-Type": "application/json",
                  },
                }
              );

              console.log(
                "✅ Payment initialization successful with admin user!"
              );
              console.log(
                "Payment data:",
                JSON.stringify(paymentResponse.data, null, 2)
              );
              return;
            } catch (paymentError) {
              console.log(
                "❌ Admin payment failed:",
                paymentError.response?.data?.error?.message ||
                  paymentError.message
              );
            }
          }

          break; // Found working password for this user
        }
      } catch (error) {
        if (error.response?.status === 401) {
          console.log(`❌ Incorrect password`);
          continue; // Try next password
        } else if (
          error.response?.data?.error?.message?.includes(
            "Too many authentication attempts"
          )
        ) {
          console.log("❌ Rate limited. Waiting...");
          await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait 2 seconds
          continue;
        } else {
          console.error(
            "❌ Unexpected error:",
            error.response?.data || error.message
          );
        }
      }
    }
  }
}

testOtherUsers();

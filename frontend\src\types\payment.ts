// Payment system types and interfaces

export interface PaymentInitialization {
  amount: number;
  email: string;
  currency: "NGN" | "USD";
  reference: string;
  callback_url?: string;
  metadata?: any;
  channels?: string[];
}

export interface PaymentResponse {
  authorization_url: string;
  access_code: string;
  reference: string;
}

export interface PaymentVerification {
  reference: string;
  amount: number;
  currency: string;
  status: string;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  channel: string;
  fees: number;
  authorization: {
    authorization_code: string;
    bin: string;
    last4: string;
    exp_month: string;
    exp_year: string;
    channel: string;
    card_type: string;
    bank: string;
    country_code: string;
    brand: string;
    reusable: boolean;
    signature: string;
  };
  customer: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    customer_code: string;
    phone: string;
    metadata: any;
  };
}

export interface PaymentTransaction {
  id: string;
  reference: string;
  amount: number;
  currency: string;
  status:
    | "pending"
    | "completed"
    | "failed"
    | "refunded"
    | "partially-refunded";
  gateway_response: string;
  paid_at?: string;
  created_at: string;
  channel: string;
  fees: number;
  customer: {
    email: string;
    first_name: string;
    last_name: string;
  };
  metadata?: any;
}

export interface RefundRequest {
  transaction: string;
  amount?: number;
  currency?: string;
  customer_note?: string;
  merchant_note?: string;
}

export interface RefundResponse {
  id: string;
  transaction: string;
  amount: number;
  currency: string;
  status: "pending" | "processed" | "failed";
  customer_note?: string;
  merchant_note?: string;
  created_at: string;
  processed_at?: string;
}

export interface PaymentMethod {
  id: string;
  type: "card" | "bank" | "ussd" | "qr" | "mobile_money" | "bank_transfer";
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  fees?: {
    percentage: number;
    fixed: number;
    cap?: number;
  };
}

export interface PaymentPricing {
  baseAmount: number;
  platformFee: number;
  paymentFees: number;
  totalAmount: number;
  currency: string;
  counselorEarnings: number;
  breakdown: {
    sessionCost: number;
    platformFee: number;
    paymentProcessingFee: number;
    total: number;
  };
}

export interface PaymentSession {
  sessionId: string;
  amount: number;
  currency: string;
  reference: string;
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  paymentUrl?: string;
  expiresAt: string;
  metadata: {
    counselorId: string;
    sessionType: string;
    scheduledAt: string;
    duration: number;
  };
}

export interface PaymentHistory {
  id: string;
  reference: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  counselor?: {
    name: string;
    profilePicture?: string;
  };
  session?: {
    id: string;
    scheduledAt: string;
    duration: number;
    type: string;
  };
  paid_at?: string;
  created_at: string;
  refund?: {
    amount: number;
    status: string;
    processed_at?: string;
  };
}

export interface PaymentStats {
  totalSpent: number;
  totalSessions: number;
  averageSessionCost: number;
  currency: string;
  monthlySpending: {
    month: string;
    amount: number;
  }[];
  paymentMethods: {
    method: string;
    count: number;
    percentage: number;
  }[];
}

export interface PaymentError {
  type: "network" | "validation" | "payment" | "server" | "unknown";
  message: string;
  code?: string;
  details?: any;
}

// Payment status types
export const PAYMENT_STATUS = {
  PENDING: "pending",
  PROCESSING: "processing",
  COMPLETED: "completed",
  FAILED: "failed",
  CANCELLED: "cancelled",
  REFUNDED: "refunded",
  PARTIALLY_REFUNDED: "partially-refunded",
} as const;

// Payment channels
export const PAYMENT_CHANNELS = [
  {
    id: "card",
    name: "Debit/Credit Card",
    icon: "💳",
    description: "Pay with your debit or credit card",
  },
  {
    id: "bank",
    name: "Bank Transfer",
    icon: "🏦",
    description: "Transfer from your bank account",
  },
  { id: "ussd", name: "USSD", icon: "📱", description: "Pay using USSD code" },
  { id: "qr", name: "QR Code", icon: "📱", description: "Scan QR code to pay" },
  {
    id: "mobile_money",
    name: "Mobile Money",
    icon: "📲",
    description: "Pay with mobile money",
  },
  {
    id: "bank_transfer",
    name: "Bank Transfer",
    icon: "🏛️",
    description: "Direct bank transfer",
  },
] as const;

// Currency formatting
export const CURRENCY_SYMBOLS = {
  NGN: "₦",
  USD: "$",
} as const;

// Payment validation rules
export const PAYMENT_LIMITS = {
  NGN: {
    min: 100, // ₦1.00
    max: ********, // ₦100,000.00
  },
  USD: {
    min: 100, // $1.00
    max: ********, // $250,000.00
  },
} as const;

// Webhook event types
export interface WebhookEvent {
  event: string;
  data: any;
  created_at: string;
}

export const WEBHOOK_EVENTS = {
  CHARGE_SUCCESS: "charge.success",
  CHARGE_FAILED: "charge.failed",
  TRANSFER_SUCCESS: "transfer.success",
  TRANSFER_FAILED: "transfer.failed",
  REFUND_PROCESSED: "refund.processed",
  SUBSCRIPTION_CREATE: "subscription.create",
  SUBSCRIPTION_DISABLE: "subscription.disable",
} as const;

// Payment component props
export interface PaymentFormProps {
  sessionId: string;
  counselorId: string;
  amount: number;
  currency: "NGN" | "USD";
  counselorName: string;
  sessionDetails: {
    date: string;
    time: string;
    duration: number;
    type: string;
  };
  onSuccess: (reference: string) => void;
  onError: (error: PaymentError) => void;
  onCancel: () => void;
}

export interface PaymentSummaryProps {
  pricing: PaymentPricing;
  sessionDetails: {
    counselorName: string;
    date: string;
    time: string;
    duration: number;
    type: string;
  };
}

export interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onMethodSelect: (method: string) => void;
  availableMethods: PaymentMethod[];
}

export type PaymentStatus =
  (typeof PAYMENT_STATUS)[keyof typeof PAYMENT_STATUS];
export type PaymentChannel = (typeof PAYMENT_CHANNELS)[number]["id"];
export type Currency = keyof typeof CURRENCY_SYMBOLS;
export type WebhookEventType =
  (typeof WEBHOOK_EVENTS)[keyof typeof WEBHOOK_EVENTS];

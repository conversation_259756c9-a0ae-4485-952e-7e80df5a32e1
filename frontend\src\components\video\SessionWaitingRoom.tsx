'use client';

import { useState, useEffect, useRef } from 'react';
import { Session } from '@/types/counselor';
import { 
  VideoCameraIcon, 
  MicrophoneIcon, 
  SpeakerWaveIcon,
  CogIcon,
  ClockIcon,
  UserIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { 
  VideoCameraSlashIcon, 
  MicrophoneSlashIcon, 
  SpeakerXMarkIcon
} from '@heroicons/react/24/solid';

interface SessionWaitingRoomProps {
  session: Session;
  userRole: 'client' | 'counselor';
  onJoinSession: () => void;
  onLeaveWaitingRoom: () => void;
}

export default function SessionWaitingRoom({ 
  session, 
  userRole, 
  onJoinSession, 
  onLeaveWaitingRoom 
}: SessionWaitingRoomProps) {
  const [devicePermissions, setDevicePermissions] = useState({
    camera: false,
    microphone: false,
  });
  const [deviceSettings, setDeviceSettings] = useState({
    videoEnabled: true,
    audioEnabled: true,
    speakerEnabled: true,
  });
  const [availableDevices, setAvailableDevices] = useState({
    cameras: [] as MediaDeviceInfo[],
    microphones: [] as MediaDeviceInfo[],
    speakers: [] as MediaDeviceInfo[],
  });
  const [selectedDevices, setSelectedDevices] = useState({
    camera: '',
    microphone: '',
    speaker: '',
  });
  const [isTestingDevices, setIsTestingDevices] = useState(false);
  const [timeUntilSession, setTimeUntilSession] = useState<string>('');
  const [canJoinSession, setCanJoinSession] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  useEffect(() => {
    requestDevicePermissions();
    getAvailableDevices();
    updateTimeUntilSession();
    
    const interval = setInterval(updateTimeUntilSession, 1000);
    return () => clearInterval(interval);
  }, []);

  const updateTimeUntilSession = () => {
    const now = new Date();
    const sessionTime = new Date(session.scheduledAt);
    const timeDiff = sessionTime.getTime() - now.getTime();
    
    if (timeDiff <= 0) {
      setTimeUntilSession('Session time has arrived');
      setCanJoinSession(true);
    } else if (timeDiff <= 5 * 60 * 1000) { // 5 minutes before
      setCanJoinSession(true);
      const minutes = Math.floor(timeDiff / 60000);
      const seconds = Math.floor((timeDiff % 60000) / 1000);
      setTimeUntilSession(`${minutes}:${seconds.toString().padStart(2, '0')} until session`);
    } else {
      const hours = Math.floor(timeDiff / 3600000);
      const minutes = Math.floor((timeDiff % 3600000) / 60000);
      if (hours > 0) {
        setTimeUntilSession(`${hours}h ${minutes}m until session`);
      } else {
        setTimeUntilSession(`${minutes}m until session`);
      }
    }
  };

  const requestDevicePermissions = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      });
      
      setDevicePermissions({
        camera: true,
        microphone: true,
      });
      
      // Start video preview
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      
      return stream;
    } catch (error) {
      console.error('Failed to get device permissions:', error);
      setDevicePermissions({
        camera: false,
        microphone: false,
      });
    }
  };

  const getAvailableDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      
      setAvailableDevices({
        cameras: devices.filter(device => device.kind === 'videoinput'),
        microphones: devices.filter(device => device.kind === 'audioinput'),
        speakers: devices.filter(device => device.kind === 'audiooutput'),
      });
      
      // Set default devices
      const defaultCamera = devices.find(device => device.kind === 'videoinput');
      const defaultMicrophone = devices.find(device => device.kind === 'audioinput');
      const defaultSpeaker = devices.find(device => device.kind === 'audiooutput');
      
      setSelectedDevices({
        camera: defaultCamera?.deviceId || '',
        microphone: defaultMicrophone?.deviceId || '',
        speaker: defaultSpeaker?.deviceId || '',
      });
    } catch (error) {
      console.error('Failed to enumerate devices:', error);
    }
  };

  const testMicrophone = async () => {
    if (!devicePermissions.microphone) return;
    
    setIsTestingDevices(true);
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: { deviceId: selectedDevices.microphone }
      });
      
      audioContextRef.current = new AudioContext();
      const analyser = audioContextRef.current.createAnalyser();
      const microphone = audioContextRef.current.createMediaStreamSource(stream);
      microphone.connect(analyser);
      
      // Test for 3 seconds
      setTimeout(() => {
        stream.getTracks().forEach(track => track.stop());
        audioContextRef.current?.close();
        setIsTestingDevices(false);
      }, 3000);
    } catch (error) {
      console.error('Microphone test failed:', error);
      setIsTestingDevices(false);
    }
  };

  const toggleVideo = () => {
    setDeviceSettings(prev => ({ ...prev, videoEnabled: !prev.videoEnabled }));
    
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !deviceSettings.videoEnabled;
      }
    }
  };

  const toggleAudio = () => {
    setDeviceSettings(prev => ({ ...prev, audioEnabled: !prev.audioEnabled }));
    
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      const audioTrack = stream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !deviceSettings.audioEnabled;
      }
    }
  };

  const formatSessionTime = () => {
    return new Date(session.scheduledAt).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Session Waiting Room</h1>
              <p className="text-purple-100">
                {userRole === 'client' 
                  ? `Session with ${session.counselor.user?.firstName} ${session.counselor.user?.lastName}`
                  : `Session with ${session.user.firstName} ${session.user.lastName}`
                }
              </p>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2 text-purple-100">
                <ClockIcon className="h-5 w-5" />
                <span>{timeUntilSession}</span>
              </div>
              <p className="text-sm text-purple-200">
                Scheduled: {formatSessionTime()}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
          {/* Video Preview */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">Camera Preview</h2>
            
            <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
              {devicePermissions.camera ? (
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-white">
                  <div className="text-center">
                    <VideoCameraSlashIcon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <p>Camera access required</p>
                    <button
                      onClick={requestDevicePermissions}
                      className="mt-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm"
                    >
                      Enable Camera
                    </button>
                  </div>
                </div>
              )}
              
              {/* Video Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                <button
                  onClick={toggleVideo}
                  className={`p-3 rounded-full ${
                    deviceSettings.videoEnabled 
                      ? 'bg-gray-800 hover:bg-gray-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  } text-white`}
                >
                  {deviceSettings.videoEnabled ? (
                    <VideoCameraIcon className="h-5 w-5" />
                  ) : (
                    <VideoCameraSlashIcon className="h-5 w-5" />
                  )}
                </button>
                
                <button
                  onClick={toggleAudio}
                  className={`p-3 rounded-full ${
                    deviceSettings.audioEnabled 
                      ? 'bg-gray-800 hover:bg-gray-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  } text-white`}
                >
                  {deviceSettings.audioEnabled ? (
                    <MicrophoneIcon className="h-5 w-5" />
                  ) : (
                    <MicrophoneSlashIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Device Settings */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900">Device Settings</h2>
            
            {/* Camera Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Camera
              </label>
              <select
                value={selectedDevices.camera}
                onChange={(e) => setSelectedDevices(prev => ({ ...prev, camera: e.target.value }))}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
              >
                {availableDevices.cameras.map((device) => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Microphone Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Microphone
              </label>
              <div className="flex space-x-2">
                <select
                  value={selectedDevices.microphone}
                  onChange={(e) => setSelectedDevices(prev => ({ ...prev, microphone: e.target.value }))}
                  className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                >
                  {availableDevices.microphones.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                    </option>
                  ))}
                </select>
                <button
                  onClick={testMicrophone}
                  disabled={isTestingDevices || !devicePermissions.microphone}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md text-sm disabled:opacity-50"
                >
                  {isTestingDevices ? 'Testing...' : 'Test'}
                </button>
              </div>
            </div>

            {/* Speaker Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Speaker
              </label>
              <select
                value={selectedDevices.speaker}
                onChange={(e) => setSelectedDevices(prev => ({ ...prev, speaker: e.target.value }))}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
              >
                {availableDevices.speakers.map((device) => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Speaker ${device.deviceId.slice(0, 8)}`}
                  </option>
                ))}
              </select>
            </div>

            {/* System Check */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">System Check</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  {devicePermissions.camera ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  ) : (
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                  )}
                  <span className="text-sm text-gray-700">Camera Access</span>
                </div>
                <div className="flex items-center space-x-2">
                  {devicePermissions.microphone ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  ) : (
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                  )}
                  <span className="text-sm text-gray-700">Microphone Access</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <span className="text-sm text-gray-700">Internet Connection</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
          <button
            onClick={onLeaveWaitingRoom}
            className="text-gray-600 hover:text-gray-700 font-medium"
          >
            Leave Waiting Room
          </button>
          
          <div className="flex space-x-3">
            {!canJoinSession && (
              <div className="text-sm text-gray-600 flex items-center space-x-2">
                <ClockIcon className="h-4 w-4" />
                <span>You can join 5 minutes before the session</span>
              </div>
            )}
            
            <button
              onClick={onJoinSession}
              disabled={!canJoinSession || (!devicePermissions.camera && !devicePermissions.microphone)}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {canJoinSession ? 'Join Session' : 'Wait to Join'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

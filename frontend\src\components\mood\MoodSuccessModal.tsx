"use client";

import { useEffect, useState } from "react";
import {
  CheckCircleIcon,
  XMarkIcon,
  BookOpenIcon,
  HeartIcon,
} from "@heroicons/react/24/outline";
import { moodAPI, CreateMoodEntryData, MoodAnalytics } from "@/lib/mood";
import { resourcesAPI } from "@/lib/resources";
import { Resource } from "@/types/resources";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface MoodSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  moodEntry: CreateMoodEntryData;
  isGuest?: boolean;
  token?: string;
  onViewAnalytics?: () => void;
}

export default function MoodSuccessModal({
  isOpen,
  onClose,
  moodEntry,
  isGuest = false,
  token,
  onViewAnalytics,
}: MoodSuccessModalProps) {
  const router = useRouter();
  const [suggestedResource, setSuggestedResource] = useState<Resource | null>(
    null
  );
  const [insight, setInsight] = useState<string | null>(null);
  const [loadingData, setLoadingData] = useState(false);

  const handleClose = () => {
    console.log("Modal close triggered");
    try {
      // Reset state when closing
      setSuggestedResource(null);
      setInsight(null);
      setLoadingData(false);
      // Call parent close handler
      onClose();
    } catch (error) {
      console.error("Error closing modal:", error);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
      loadPersonalizedContent();

      // Handle escape key
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          handleClose();
        }
      };

      document.addEventListener("keydown", handleEscape);

      return () => {
        document.removeEventListener("keydown", handleEscape);
        document.body.style.overflow = "unset";
      };
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const loadPersonalizedContent = async () => {
    if (!isOpen) return;

    setLoadingData(true);
    try {
      // For authenticated users, get dynamic feedback and suggestions from backend
      if (!isGuest && token) {
        const [feedbackResponse, suggestionsResponse] =
          await Promise.allSettled([
            moodAPI.getPersonalizedFeedback(moodEntry, token),
            moodAPI.getResourceSuggestions(moodEntry, token),
          ]);

        if (feedbackResponse.status === "fulfilled") {
          setInsight(feedbackResponse.value);
        }

        if (
          suggestionsResponse.status === "fulfilled" &&
          suggestionsResponse.value.length > 0
        ) {
          setSuggestedResource(suggestionsResponse.value[0]);
        } else {
          // Fallback to static resource if backend fails
          const fallbackResource = getFallbackResource();
          setSuggestedResource(fallbackResource);
        }
      } else {
        // For guests, use fallback resource
        const fallbackResource = getFallbackResource();
        setSuggestedResource(fallbackResource);
      }
    } catch (error) {
      console.error("Error loading personalized content:", error);
      // Use fallback resource on error
      const fallbackResource = getFallbackResource();
      setSuggestedResource(fallbackResource);
    } finally {
      setLoadingData(false);
    }
  };

  const getFallbackResource = (): Resource => {
    // Fallback resources based on mood
    const fallbackResources: Record<string, Partial<Resource>> = {
      low: {
        title: "5-Minute Breathing Exercise",
        description:
          "A simple breathing technique to help calm your mind and reduce stress.",
        seo: { slug: "breathing-exercise" },
        type: "tool",
      },
      anxious: {
        title: "Quick Anxiety Relief Techniques",
        description:
          "Immediate strategies to help manage anxiety and find calm.",
        seo: { slug: "anxiety-relief" },
        type: "guide",
      },
      tired: {
        title: "Energy Boosting Self-Care Tips",
        description: "Gentle ways to restore your energy and motivation.",
        seo: { slug: "energy-boost" },
        type: "article",
      },
      default: {
        title: "Daily Mindfulness Practice",
        description: "Simple mindfulness exercises to enhance your wellbeing.",
        seo: { slug: "mindfulness-practice" },
        type: "guide",
      },
    };

    let key = "default";
    if (moodEntry.mood <= 2) key = "low";
    else if (moodEntry.anxiety && moodEntry.anxiety >= 4) key = "anxious";
    else if (moodEntry.energy && moodEntry.energy <= 2) key = "tired";

    return fallbackResources[key] as Resource;
  };

  const getDefaultMessage = (): string => {
    // Fallback message for guests or when backend feedback fails
    const mood = moodEntry.mood;
    const anxiety = moodEntry.anxiety || 3;

    if (mood <= 2 && anxiety >= 4) {
      return "I notice you're feeling down and anxious. That's completely valid, and you're brave for acknowledging these feelings.";
    } else if (mood <= 2) {
      return "Thank you for sharing how you're feeling. It takes courage to acknowledge difficult emotions.";
    } else if (anxiety >= 4) {
      return "I see you're feeling anxious right now. Remember that anxiety is temporary and you have the strength to work through this.";
    } else if (mood >= 4) {
      return "It's wonderful to see you feeling good! These positive moments are important to celebrate and remember.";
    } else {
      return "Thank you for taking time to check in with yourself. Self-awareness is a powerful tool for wellbeing.";
    }
  };

  if (!isOpen) {
    return null;
  }

  const moodOption = moodAPI
    .getMoodOptions()
    .find((option) => option.value === moodEntry.mood);

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4"
      onClick={(e) => {
        console.log("Backdrop clicked");
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
      style={{ zIndex: 9999 }}
    >
      <div
        className="bg-white rounded-lg max-w-lg w-full max-h-[90vh] overflow-y-auto p-6 transform transition-all duration-300 scale-100 shadow-xl relative"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                {isGuest ? "Mood Recorded!" : "Mood Entry Saved!"}
              </h3>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-3 transition-all duration-200 absolute top-4 right-4 z-10"
            aria-label="Close modal"
            type="button"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Mood Display */}
          {moodOption && (
            <div className="text-center">
              <div className="text-5xl mb-3">{moodOption.emoji}</div>
              <p className={`text-xl font-medium ${moodOption.color}`}>
                {moodOption.label}
              </p>
            </div>
          )}

          {/* Personalized Feedback */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-5">
            <div className="flex items-start space-x-4">
              <HeartIcon className="h-6 w-6 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-blue-900 mb-2">Reflection</h4>
                {loadingData ? (
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-blue-200 rounded w-full"></div>
                    <div className="h-4 bg-blue-200 rounded w-3/4"></div>
                  </div>
                ) : (
                  <p className="text-sm text-blue-800 leading-relaxed">
                    {!isGuest && insight ? insight : getDefaultMessage()}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Resource Suggestion */}
          {(suggestedResource || loadingData) && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-5">
              <div className="flex items-start space-x-4">
                <BookOpenIcon className="h-6 w-6 text-green-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="font-semibold text-green-900 mb-2">
                    Suggested Resource
                  </h4>
                  {loadingData ? (
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-green-200 rounded w-3/4"></div>
                      <div className="h-3 bg-green-200 rounded w-full"></div>
                    </div>
                  ) : suggestedResource ? (
                    <>
                      <p className="text-sm font-medium text-green-800 mb-2">
                        {suggestedResource.title}
                      </p>
                      <p className="text-sm text-green-700 mb-4 leading-relaxed">
                        {suggestedResource.description}
                      </p>
                      <button
                        onClick={() => {
                          console.log("View Resource clicked");
                          handleClose();
                          router.push(
                            `/resources/${suggestedResource.seo.slug}`
                          );
                        }}
                        className="inline-flex items-center text-sm bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium"
                      >
                        View Resource →
                      </button>
                    </>
                  ) : null}
                </div>
              </div>
            </div>
          )}

          {/* Guest signup prompt */}
          {isGuest && (
            <div className="bg-purple-50 border border-purple-200 rounded-xl p-5">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-sm">✨</span>
                </div>
                <div>
                  <h4 className="font-semibold text-purple-900 mb-2">
                    Unlock More Features
                  </h4>
                  <p className="text-sm text-purple-800 leading-relaxed">
                    Create an account to unlock mood analytics, history
                    tracking, and personalized insights!
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log("Close button clicked");
              handleClose();
            }}
            className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            type="button"
          >
            Close
          </button>
          {isGuest ? (
            <button
              onClick={() => {
                handleClose();
                window.location.href = "/auth/signup";
              }}
              className="flex-1 bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors"
            >
              Sign Up
            </button>
          ) : (
            <button
              onClick={() => {
                console.log("View Analytics clicked");
                handleClose();
                // Use callback if provided, otherwise fallback to router
                if (onViewAnalytics) {
                  setTimeout(() => {
                    console.log("Calling onViewAnalytics callback...");
                    onViewAnalytics();
                  }, 100);
                } else {
                  setTimeout(() => {
                    console.log("Navigating to analytics...");
                    router.push("/mood-tracker?tab=analytics");
                  }, 100);
                }
              }}
              className="flex-1 bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors"
            >
              View Analytics
            </button>
          )}
        </div>

        {/* Close instructions */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Press ESC or click outside to close
          </p>
        </div>
      </div>
    </div>
  );
}

const mongoose = require("mongoose");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// Counselor Schema
const counselorSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  bio: String,
  specializations: [String],
  experience: {
    years: Number,
    description: String,
  },
  qualifications: [
    {
      degree: String,
      institution: String,
      year: Number,
    },
  ],
  licenses: [
    {
      type: String,
      number: String,
      issuingAuthority: String,
      expiryDate: Date,
    },
  ],
  pricing: {
    currency: { type: String, enum: ["NGN", "USD"], default: "NGN" },
    ratePerMinute: Number,
    minimumSessionDuration: { type: Number, default: 15 },
  },
  availability: {
    timezone: { type: String, default: "Africa/Lagos" },
    schedule: { type: Map, of: mongoose.Schema.Types.Mixed },
    unavailableDates: [Date],
    availabilityExpiresAt: Date,
  },
  verification: {
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "suspended"],
      default: "pending",
    },
    documents: {
      idDocument: {
        url: String,
        verified: { type: Boolean, default: false },
      },
      certificates: [
        {
          url: String,
          verified: { type: Boolean, default: false },
        },
      ],
    },
    submittedAt: Date,
    reviewedAt: Date,
    reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    notes: String,
  },
  profile: {
    title: String,
    profilePicture: String,
    languages: [String],
    approachDescription: String,
    sessionTypes: [String],
    approachStyle: [String],
    isOnline: { type: Boolean, default: false },
  },
  statistics: {
    totalSessions: { type: Number, default: 0 },
    totalEarnings: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalReviews: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0 },
    responseTime: { type: Number, default: 0 },
  },
  settings: {
    acceptingNewClients: { type: Boolean, default: true },
    autoAcceptBookings: { type: Boolean, default: false },
    requiresApproval: { type: Boolean, default: true },
    cancellationPolicy: String,
    reschedulePolicy: String,
  },
  bankDetails: {
    accountName: String,
    accountNumber: String,
    bankName: String,
    bankCode: String,
    currency: { type: String, enum: ["NGN", "USD"], default: "NGN" },
  },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Counselor = mongoose.model("Counselor", counselorSchema);

async function fixCounselorQualifications() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    // Find all counselors with problematic qualifications
    const counselors = await Counselor.find({});
    console.log(`Found ${counselors.length} counselors to check`);

    for (const counselor of counselors) {
      let needsUpdate = false;
      const fixedQualifications = [];

      // Check if qualifications need fixing
      if (counselor.qualifications && Array.isArray(counselor.qualifications)) {
        for (const qualification of counselor.qualifications) {
          // If qualification is a character array object, convert it back to string
          if (typeof qualification === 'object' && !qualification.degree) {
            const qualificationString = Object.values(qualification).join('');
            fixedQualifications.push({
              degree: qualificationString,
              institution: "University/Institution",
              year: new Date().getFullYear() - 5, // Default to 5 years ago
            });
            needsUpdate = true;
            console.log(`Fixed qualification for counselor ${counselor._id}: ${qualificationString}`);
          } else if (typeof qualification === 'string') {
            // If it's a string, convert to proper object
            fixedQualifications.push({
              degree: qualification,
              institution: "University/Institution",
              year: new Date().getFullYear() - 5,
            });
            needsUpdate = true;
            console.log(`Converted string qualification for counselor ${counselor._id}: ${qualification}`);
          } else if (qualification.degree) {
            // Already in correct format
            fixedQualifications.push(qualification);
          }
        }
      }

      // Update the counselor if needed
      if (needsUpdate) {
        await Counselor.findByIdAndUpdate(counselor._id, {
          qualifications: fixedQualifications,
          // Also fix other missing fields
          'profile.title': counselor.profile?.title || 'Licensed Counselor',
          'profile.languages': counselor.profile?.languages?.length ? counselor.profile.languages : ['English'],
          'profile.approachStyle': counselor.profile?.approachStyle || [],
          'statistics.averageRating': counselor.statistics?.averageRating || 4.5,
          'verification.status': counselor.verification?.status || 'approved',
          'verification.submittedAt': counselor.verification?.submittedAt || new Date(),
          'availability.availabilityExpiresAt': counselor.availability?.availabilityExpiresAt || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          'settings.cancellationPolicy': counselor.settings?.cancellationPolicy || 'Cancellations must be made at least 12 hours in advance for a full refund.',
          'settings.reschedulePolicy': counselor.settings?.reschedulePolicy || 'Sessions can be rescheduled up to 12 hours before the scheduled time.',
        });
        console.log(`Updated counselor ${counselor._id}`);
      } else {
        console.log(`Counselor ${counselor._id} already has proper qualifications`);
      }
    }

    console.log("Qualification fixing completed!");

    // Verify the fix by fetching counselors
    const updatedCounselors = await Counselor.find({})
      .populate("userId", "firstName lastName profilePicture")
      .limit(2);

    console.log("\nVerification - Sample counselor data:");
    for (const counselor of updatedCounselors) {
      console.log(`\nCounselor: ${counselor.userId?.firstName} ${counselor.userId?.lastName}`);
      console.log("Qualifications:", counselor.qualifications);
      console.log("Profile:", counselor.profile);
      console.log("Statistics:", counselor.statistics);
    }

  } catch (error) {
    console.error("Error fixing qualifications:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

// Run the fix
fixCounselorQualifications();

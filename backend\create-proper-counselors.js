const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// User Schema
const userSchema = new mongoose.Schema({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  username: { type: String, required: true, unique: true },
  email: { type: String, unique: true, required: true },
  password: String,
  role: { type: String, enum: ["user", "counselor", "admin"], default: "user" },
  isActive: { type: Boolean, default: true },
  isEmailVerified: { type: Boolean, default: true },
  profilePicture: String,
  phone: String,
  dateOfBirth: Date,
  gender: String,
  location: String,
  areasOfInterest: { type: [String], default: [] },
  preferences: {
    notifications: {
      email: { type: <PERSON>olean, default: true },
      push: { type: Boolean, default: true },
      sessionReminders: { type: Boolean, default: true },
      chatMessages: { type: Boolean, default: true },
    },
    privacy: {
      showOnlineStatus: { type: Boolean, default: true },
      allowDirectMessages: { type: Boolean, default: true },
    },
  },
  appSettings: {
    theme: { type: String, enum: ["light", "dark", "system"], default: "system" },
    accentColor: { type: String, default: "#7c3aed" },
    fontSize: { type: String, enum: ["small", "medium", "large"], default: "medium" },
    reducedMotion: { type: Boolean, default: false },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Counselor Schema with proper structure
const counselorSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  bio: String,
  specializations: [String],
  experience: {
    years: Number,
    description: String,
  },
  qualifications: [
    {
      degree: String,
      institution: String,
      year: Number,
    },
  ],
  licenses: [
    {
      type: String,
      number: String,
      issuingAuthority: String,
      expiryDate: Date,
    },
  ],
  pricing: {
    currency: { type: String, enum: ["NGN", "USD"], default: "NGN" },
    ratePerMinute: Number,
    minimumSessionDuration: { type: Number, default: 15 },
  },
  availability: {
    timezone: { type: String, default: "Africa/Lagos" },
    schedule: { type: Map, of: mongoose.Schema.Types.Mixed },
    unavailableDates: [Date],
    availabilityExpiresAt: Date,
  },
  verification: {
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "suspended"],
      default: "approved", // Set to approved for test data
    },
    documents: {
      idDocument: {
        url: String,
        verified: { type: Boolean, default: false },
      },
      certificates: [
        {
          url: String,
          verified: { type: Boolean, default: false },
        },
      ],
    },
    submittedAt: { type: Date, default: Date.now },
    reviewedAt: Date,
    reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    notes: String,
  },
  profile: {
    title: { type: String, default: "Licensed Counselor" },
    profilePicture: String,
    languages: { type: [String], default: ["English"] },
    approachDescription: String,
    sessionTypes: [String],
    approachStyle: [String],
    isOnline: { type: Boolean, default: false },
  },
  statistics: {
    totalSessions: { type: Number, default: 0 },
    totalEarnings: { type: Number, default: 0 },
    averageRating: { type: Number, default: 4.5 }, // Default good rating for test data
    totalReviews: { type: Number, default: 12 }, // Default some reviews
    completionRate: { type: Number, default: 95 },
    responseTime: { type: Number, default: 2 },
  },
  settings: {
    acceptingNewClients: { type: Boolean, default: true },
    autoAcceptBookings: { type: Boolean, default: false },
    requiresApproval: { type: Boolean, default: true },
    cancellationPolicy: {
      type: String,
      default: "Cancellations must be made at least 12 hours in advance for a full refund.",
    },
    reschedulePolicy: {
      type: String,
      default: "Sessions can be rescheduled up to 12 hours before the scheduled time.",
    },
  },
  bankDetails: {
    accountName: String,
    accountNumber: String,
    bankName: String,
    bankCode: String,
    currency: { type: String, enum: ["NGN", "USD"], default: "NGN" },
  },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const User = mongoose.model("User", userSchema);
const Counselor = mongoose.model("Counselor", counselorSchema);

const testUsers = [
  {
    firstName: "Dr. Sarah",
    lastName: "Johnson",
    username: "drsarahjohnson",
    email: "<EMAIL>",
    password: "$2a$10$Hash.Of.Password123",
    role: "counselor",
    phone: "+*************",
    gender: "female",
    location: "Lagos, Nigeria",
    profilePicture: "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face",
  },
  {
    firstName: "Dr. Michael",
    lastName: "Brown",
    username: "drmichaelbrown",
    email: "<EMAIL>",
    password: "$2a$10$Hash.Of.Password123",
    role: "counselor",
    phone: "+*************",
    gender: "male",
    location: "Abuja, Nigeria",
    profilePicture: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face",
  },
  {
    firstName: "Dr. Aisha",
    lastName: "Okonkwo",
    username: "draishaokonkwo",
    email: "<EMAIL>",
    password: "$2a$10$Hash.Of.Password123",
    role: "counselor",
    phone: "+2348034567890",
    gender: "female",
    location: "Port Harcourt, Nigeria",
    profilePicture: "https://images.unsplash.com/photo-1594824694191-18bbeb4d7b8c?w=150&h=150&fit=crop&crop=face",
  },
];

const counselorProfiles = [
  {
    bio: "Experienced clinical psychologist specializing in anxiety and depression treatment with over 8 years of practice. I use evidence-based approaches to help clients overcome mental health challenges.",
    specializations: ["anxiety", "depression", "stress-management", "trauma"],
    experience: {
      years: 8,
      description: "Clinical psychologist with extensive experience in cognitive behavioral therapy and mindfulness-based interventions.",
    },
    qualifications: [
      {
        degree: "Ph.D. in Clinical Psychology",
        institution: "University of Lagos",
        year: 2016,
      },
      {
        degree: "M.Sc. in Psychology",
        institution: "University of Ibadan",
        year: 2013,
      },
    ],
    pricing: {
      currency: "NGN",
      ratePerMinute: 150,
      minimumSessionDuration: 30,
    },
    profile: {
      title: "Clinical Psychologist",
      languages: ["English", "Yoruba"],
      approachDescription: "I use a combination of CBT, mindfulness, and person-centered therapy approaches.",
      sessionTypes: ["individual", "group"],
      approachStyle: ["Cognitive Behavioral Therapy", "Mindfulness-Based", "Person-Centered"],
      isOnline: true,
    },
    statistics: {
      totalSessions: 450,
      totalEarnings: 1350000,
      averageRating: 4.8,
      totalReviews: 89,
      completionRate: 98,
      responseTime: 1,
    },
  },
  {
    bio: "Licensed therapist focusing on relationship counseling and family therapy with a holistic approach. Helping couples and families build stronger connections.",
    specializations: ["relationships", "family-therapy", "couples-counseling", "communication"],
    experience: {
      years: 6,
      description: "Family therapist with specialization in systemic therapy and relationship dynamics.",
    },
    qualifications: [
      {
        degree: "Master of Arts in Marriage and Family Therapy",
        institution: "University of Nigeria, Nsukka",
        year: 2018,
      },
      {
        degree: "Bachelor of Psychology",
        institution: "Ahmadu Bello University",
        year: 2016,
      },
    ],
    pricing: {
      currency: "NGN",
      ratePerMinute: 120,
      minimumSessionDuration: 45,
    },
    profile: {
      title: "Marriage and Family Therapist",
      languages: ["English", "Hausa"],
      approachDescription: "I specialize in systemic family therapy and emotionally focused therapy for couples.",
      sessionTypes: ["couples", "family", "individual"],
      approachStyle: ["Systemic Therapy", "Emotionally Focused Therapy", "Solution-Focused"],
      isOnline: true,
    },
    statistics: {
      totalSessions: 320,
      totalEarnings: 864000,
      averageRating: 4.7,
      totalReviews: 64,
      completionRate: 96,
      responseTime: 2,
    },
  },
  {
    bio: "Compassionate therapist specializing in trauma recovery, PTSD, and women's mental health issues. Creating safe spaces for healing and growth.",
    specializations: ["trauma", "ptsd", "women-health", "grief", "abuse-recovery"],
    experience: {
      years: 5,
      description: "Trauma specialist with expertise in EMDR and trauma-informed care approaches.",
    },
    qualifications: [
      {
        degree: "M.Sc. in Clinical Psychology",
        institution: "University of Port Harcourt",
        year: 2019,
      },
      {
        degree: "EMDR Certification",
        institution: "EMDR International Association",
        year: 2021,
      },
    ],
    pricing: {
      currency: "NGN",
      ratePerMinute: 140,
      minimumSessionDuration: 60,
    },
    profile: {
      title: "Trauma Specialist",
      languages: ["English", "Igbo"],
      approachDescription: "I specialize in trauma-informed care using EMDR and somatic approaches.",
      sessionTypes: ["individual"],
      approachStyle: ["EMDR", "Trauma-Informed Care", "Somatic Therapy"],
      isOnline: false,
    },
    statistics: {
      totalSessions: 280,
      totalEarnings: 784000,
      averageRating: 4.9,
      totalReviews: 56,
      completionRate: 97,
      responseTime: 1,
    },
  },
];

async function createProperTestData() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    // Clear existing data
    console.log("Clearing existing counselor and user data...");
    await Counselor.deleteMany({});
    
    // Clear specific test users
    const testEmails = testUsers.map(u => u.email);
    await User.deleteMany({ email: { $in: testEmails } });

    console.log("Creating new test data...");

    // Create users and counselors
    for (let i = 0; i < testUsers.length; i++) {
      const userData = testUsers[i];
      const counselorData = counselorProfiles[i];

      // Create user
      const user = new User(userData);
      await user.save();
      console.log(`Created user: ${user.firstName} ${user.lastName}`);

      // Create counselor profile
      const counselor = new Counselor({
        userId: user._id,
        ...counselorData,
        availability: {
          timezone: "Africa/Lagos",
          schedule: new Map(),
          unavailableDates: [],
          availabilityExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        },
        verification: {
          status: "approved",
          documents: {
            idDocument: { verified: false },
            certificates: [],
          },
          submittedAt: new Date(),
        },
        settings: {
          acceptingNewClients: true,
          autoAcceptBookings: false,
          requiresApproval: true,
          cancellationPolicy: "Cancellations must be made at least 12 hours in advance for a full refund.",
          reschedulePolicy: "Sessions can be rescheduled up to 12 hours before the scheduled time.",
        },
        bankDetails: {
          currency: "NGN",
        },
      });

      await counselor.save();
      console.log(`Created counselor profile for: ${user.firstName} ${user.lastName}`);
    }

    console.log("Test data creation completed!");

    // Verify the data
    console.log("\nVerifying created data...");
    const counselors = await Counselor.find({})
      .populate("userId", "firstName lastName email profilePicture")
      .exec();

    console.log(`\nCreated ${counselors.length} counselors:`);
    counselors.forEach((counselor, index) => {
      console.log(`\n${index + 1}. ${counselor.userId.firstName} ${counselor.userId.lastName}`);
      console.log(`   Specializations: ${counselor.specializations.join(", ")}`);
      console.log(`   Experience: ${counselor.experience.years} years`);
      console.log(`   Rating: ${counselor.statistics.averageRating}/5 (${counselor.statistics.totalReviews} reviews)`);
      console.log(`   Rate: ₦${counselor.pricing.ratePerMinute}/minute`);
      console.log(`   Title: ${counselor.profile.title}`);
      console.log(`   Languages: ${counselor.profile.languages.join(", ")}`);
      console.log(`   Qualifications: ${counselor.qualifications.length} degrees`);
      counselor.qualifications.forEach((qual, qIndex) => {
        console.log(`     ${qIndex + 1}. ${qual.degree} - ${qual.institution} (${qual.year})`);
      });
    });

  } catch (error) {
    console.error("Error creating test data:", error);
  } finally {
    await mongoose.disconnect();
    console.log("\nDisconnected from MongoDB");
  }
}

// Run the script
createProperTestData();

"use client";

import { useRouter } from "next/navigation";
import { X, Star, Heart, MessageCircle, Calendar } from "lucide-react";

interface GuestUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GuestUpgradeModal({
  isOpen,
  onClose,
}: GuestUpgradeModalProps) {
  const router = useRouter();

  if (!isOpen) return null;

  const features = [
    { icon: Heart, text: "Save your progress & preferences" },
    { icon: MessageCircle, text: "Join community discussions" },
    { icon: Calendar, text: "Book 1-on-1 counseling sessions" },
    { icon: Star, text: "Leave reviews & ratings" },
  ];

  const handleSignUp = () => {
    router.push("/auth/signup");
  };

  const handleSignIn = () => {
    router.push("/auth/login");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            Create a free account
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">✨</span>
          </div>
          <p className="text-gray-600 mb-4">
            Create a free account to save your progress & get personalized tips.
          </p>
        </div>

        {/* Feature List */}
        <div className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className="flex items-center text-sm text-gray-700"
            >
              <feature.icon className="h-4 w-4 text-purple-600 mr-3 flex-shrink-0" />
              <span>{feature.text}</span>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleSignUp}
            className="w-full py-3 px-4 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Create Free Account
          </button>
          <button
            onClick={handleSignIn}
            className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Already have an account? Sign in
          </button>
          <button
            onClick={onClose}
            className="w-full py-2 px-4 text-sm text-gray-500 hover:text-gray-700"
          >
            Continue as guest
          </button>
        </div>
      </div>
    </div>
  );
}

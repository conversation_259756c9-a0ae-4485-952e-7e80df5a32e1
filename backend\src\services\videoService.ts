import axios from "axios";
import { Session } from "@/models/Session";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";

export interface DailyRoomConfig {
  name?: string;
  privacy?: "public" | "private";
  properties?: {
    max_participants?: number;
    enable_screenshare?: boolean;
    enable_chat?: boolean;
    enable_knocking?: boolean;
    enable_prejoin_ui?: boolean;
    enable_network_ui?: boolean;
    enable_people_ui?: boolean;
    start_video_off?: boolean;
    start_audio_off?: boolean;
    owner_only_broadcast?: boolean;
    enable_recording?: "cloud" | "local" | "raw-tracks";
    recording_bucket?: {
      allow_api_access?: boolean;
      allow_streaming_from_bucket?: boolean;
      bucket_name?: string;
      bucket_region?: string;
      assume_role_arn?: string;
    };
    exp?: number; // Room expiration timestamp
    eject_at_room_exp?: boolean;
    lang?: string;
  };
}

export interface DailyRoom {
  id: string;
  name: string;
  api_created: boolean;
  privacy: string;
  url: string;
  created_at: string;
  config: DailyRoomConfig;
}

export interface DailyMeetingToken {
  token: string;
  room_name: string;
  user_name?: string;
  user_id?: string;
  is_owner?: boolean;
  exp?: number;
  enable_screenshare?: boolean;
  start_video_off?: boolean;
  start_audio_off?: boolean;
  enable_recording?: boolean;
}

export interface SessionRecording {
  id: string;
  room_name: string;
  status: "finished" | "in-progress" | "failed";
  max_participants: number;
  duration: number;
  start_ts: number;
  download_link?: string;
  mp4_size?: number;
  mp4_duration?: number;
}

export interface VideoSessionStats {
  sessionId: string;
  roomName: string;
  participants: {
    userId?: string;
    name: string;
    joinTime: Date;
    leaveTime?: Date;
    duration: number;
    quality: {
      video: "good" | "fair" | "poor";
      audio: "good" | "fair" | "poor";
      connection: "stable" | "unstable" | "poor";
    };
  }[];
  totalDuration: number;
  recordingId?: string;
  issues: string[];
}

export class VideoService {
  private static readonly DAILY_API_KEY = process.env.DAILY_API_KEY;
  private static readonly DAILY_BASE_URL = "https://api.daily.co/v1";

  /**
   * Create a Daily.co room for a session
   */
  static async createSessionRoom(sessionId: string): Promise<DailyRoom> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId)
        .populate("clientId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      // Calculate room expiration (session time + 2 hours buffer)
      const sessionStart = new Date(session.scheduledAt);
      const sessionEnd = new Date(
        sessionStart.getTime() + (session.duration + 120) * 60 * 1000
      );
      const expiration = Math.floor(sessionEnd.getTime() / 1000);

      const roomConfig: DailyRoomConfig = {
        name: `session-${sessionId}`,
        privacy: "private",
        properties: {
          max_participants: 2, // Client and counselor only
          enable_screenshare: true,
          enable_chat: true,
          enable_knocking: true,
          enable_prejoin_ui: true,
          enable_network_ui: true,
          enable_people_ui: false, // Hide participant list for privacy
          start_video_off: false,
          start_audio_off: false,
          owner_only_broadcast: false,
          enable_recording: "cloud",
          exp: expiration,
          eject_at_room_exp: true,
          lang: "en",
        },
      };

      const response = await axios.post(
        `${this.DAILY_BASE_URL}/rooms`,
        roomConfig,
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
            "Content-Type": "application/json",
          },
        }
      );

      const room: DailyRoom = response.data;

      // Update session with room details
      session.videoSession = {
        roomId: room.id,
        roomName: room.name,
        roomUrl: room.url,
        provider: "daily.co",
      };

      await session.save();

      logger.info(
        `Daily.co room created for session ${sessionId}: ${room.name}`
      );
      return room;
    } catch (error) {
      logger.error("Create session room error:", error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.error || "Failed to create video room",
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Generate meeting token for participant
   */
  static async generateMeetingToken(
    sessionId: string,
    userId: string,
    role: "client" | "counselor"
  ): Promise<DailyMeetingToken> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId)
        .populate("clientId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (!session.videoSession?.roomName) {
        throw createError("Video room not created for this session", 400);
      }

      // Verify user authorization
      const isClient = session.clientId.toString() === userId;
      const isCounselor = session.counselorId.toString() === userId;

      if (!isClient && !isCounselor) {
        throw createError("Unauthorized to join this session", 403);
      }

      // Get user details
      const user = isClient ? session.clientId : session.counselorId;
      const userName =
        role === "client"
          ? `${(user as any).firstName} ${(user as any).lastName}`
          : `Dr. ${
              (user as any).userId?.firstName || (user as any).firstName
            } ${(user as any).userId?.lastName || (user as any).lastName}`;

      // Token expires 1 hour after session end
      const sessionEnd = new Date(
        session.scheduledAt.getTime() + (session.duration + 60) * 60 * 1000
      );
      const tokenExpiration = Math.floor(sessionEnd.getTime() / 1000);

      const tokenConfig = {
        room_name: session.videoSession.roomName,
        user_name: userName,
        user_id: userId,
        is_owner: role === "counselor", // Counselor has owner privileges
        exp: tokenExpiration,
        enable_screenshare: true,
        start_video_off: false,
        start_audio_off: false,
        enable_recording: role === "counselor", // Only counselor can control recording
      };

      const response = await axios.post(
        `${this.DAILY_BASE_URL}/meeting-tokens`,
        tokenConfig,
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
            "Content-Type": "application/json",
          },
        }
      );

      const token: DailyMeetingToken = response.data;

      logger.info(
        `Meeting token generated for session ${sessionId}, user ${userId} (${role})`
      );
      return token;
    } catch (error) {
      logger.error("Generate meeting token error:", error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.error || "Failed to generate meeting token",
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Start recording for a session
   */
  static async startRecording(
    sessionId: string,
    startedBy: string
  ): Promise<void> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (!session.videoSession?.roomName) {
        throw createError("Video room not found for this session", 400);
      }

      const response = await axios.post(
        `${this.DAILY_BASE_URL}/rooms/${session.videoSession.roomName}/recordings/start`,
        {
          layout: {
            preset: "default",
          },
        },
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Update session with recording info
      session.videoSession.recording = {
        isRecording: true,
        startedAt: new Date(),
      };

      await session.save();

      logger.info(`Recording started for session ${sessionId} by ${startedBy}`);
    } catch (error) {
      logger.error("Start recording error:", error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.error || "Failed to start recording",
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Stop recording for a session
   */
  static async stopRecording(
    sessionId: string,
    stoppedBy: string
  ): Promise<void> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (!session.videoSession?.roomName) {
        throw createError("Video room not found for this session", 400);
      }

      const response = await axios.post(
        `${this.DAILY_BASE_URL}/rooms/${session.videoSession.roomName}/recordings/stop`,
        {},
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Update session with recording info
      if (session.videoSession.recording) {
        session.videoSession.recording.isRecording = false;
        session.videoSession.recording.stoppedAt = new Date();
        session.videoSession.recording.stoppedBy = stoppedBy as any;
      }

      await session.save();

      logger.info(`Recording stopped for session ${sessionId} by ${stoppedBy}`);
    } catch (error) {
      logger.error("Stop recording error:", error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.error || "Failed to stop recording",
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Get session recordings
   */
  static async getSessionRecordings(
    sessionId: string
  ): Promise<SessionRecording[]> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (!session.videoSession?.roomName) {
        throw createError("Video room not found for this session", 400);
      }

      const response = await axios.get(
        `${this.DAILY_BASE_URL}/recordings?room_name=${session.videoSession.roomName}`,
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
          },
        }
      );

      const recordings: SessionRecording[] = response.data.data || [];

      logger.info(
        `Retrieved ${recordings.length} recordings for session ${sessionId}`
      );
      return recordings;
    } catch (error) {
      logger.error("Get session recordings error:", error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.error || "Failed to get recordings",
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Delete a room after session completion
   */
  static async deleteSessionRoom(sessionId: string): Promise<void> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (!session.videoSession?.roomName) {
        logger.warn(`No video room found for session ${sessionId}`);
        return;
      }

      await axios.delete(
        `${this.DAILY_BASE_URL}/rooms/${session.videoSession.roomName}`,
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
          },
        }
      );

      // Update session to mark room as deleted
      session.videoSession.deletedAt = new Date();
      await session.save();

      logger.info(`Daily.co room deleted for session ${sessionId}`);
    } catch (error) {
      logger.error("Delete session room error:", error);
      if (axios.isAxiosError(error) && error.response?.status !== 404) {
        // Don't throw error if room doesn't exist
        throw createError(
          error.response?.data?.error || "Failed to delete video room",
          error.response?.status || 500
        );
      }
    }
  }

  /**
   * Get room analytics and participant data
   */
  static async getRoomAnalytics(sessionId: string): Promise<any> {
    try {
      if (!this.DAILY_API_KEY) {
        throw createError("Daily.co API key not configured", 500);
      }

      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (!session.videoSession?.roomName) {
        throw createError("Video room not found for this session", 400);
      }

      const response = await axios.get(
        `${this.DAILY_BASE_URL}/analytics/meetings?room_name=${session.videoSession.roomName}`,
        {
          headers: {
            Authorization: `Bearer ${this.DAILY_API_KEY}`,
          },
        }
      );

      const analytics = response.data;

      logger.info(`Retrieved analytics for session ${sessionId}`);
      return analytics;
    } catch (error) {
      logger.error("Get room analytics error:", error);
      if (axios.isAxiosError(error)) {
        throw createError(
          error.response?.data?.error || "Failed to get room analytics",
          error.response?.status || 500
        );
      }
      throw error;
    }
  }

  /**
   * Process session completion and cleanup
   */
  static async completeVideoSession(sessionId: string): Promise<void> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      // Stop recording if still active
      if (session.videoSession?.recording?.isRecording) {
        await this.stopRecording(sessionId, "system");
      }

      // Get final analytics
      try {
        const analytics = await this.getRoomAnalytics(sessionId);
        if (session.videoSession) {
          session.videoSession.analytics = analytics;
        }
      } catch (error) {
        logger.warn(`Failed to get analytics for session ${sessionId}:`, error);
      }

      // Mark video session as completed
      if (session.videoSession) {
        session.videoSession.completedAt = new Date();
      }

      await session.save();

      // Schedule room deletion (after 24 hours to allow recording processing)
      setTimeout(async () => {
        try {
          await this.deleteSessionRoom(sessionId);
        } catch (error) {
          logger.error(
            `Failed to delete room for session ${sessionId}:`,
            error
          );
        }
      }, 24 * 60 * 60 * 1000); // 24 hours

      logger.info(`Video session completed: ${sessionId}`);
    } catch (error) {
      logger.error("Complete video session error:", error);
      throw error;
    }
  }

  /**
   * Get Daily.co webhook verification
   */
  static verifyWebhook(payload: string, signature: string): boolean {
    try {
      const crypto = require("crypto");
      const expectedSignature = crypto
        .createHmac("sha256", process.env.DAILY_WEBHOOK_SECRET || "")
        .update(payload, "utf8")
        .digest("hex");

      return signature === `sha256=${expectedSignature}`;
    } catch (error) {
      logger.error("Webhook verification error:", error);
      return false;
    }
  }
}

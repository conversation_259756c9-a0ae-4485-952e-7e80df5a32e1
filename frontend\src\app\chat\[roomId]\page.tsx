"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { chatAPI } from "@/lib/chat";
import { ChatRoom as ChatRoomType } from "@/types/chat";
import Header from "@/components/layout/Header";
import ChatRoom from "@/components/chat/ChatRoom";
import { UserGroupIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function ChatRoomPage() {
  const router = useRouter();
  const params = useParams();
  const roomId = params.roomId as string;

  const { isAuthenticated, isGuest, user, tokens, guestToken, isLoading } =
    useAuthStore();
  const [room, setRoom] = useState<ChatRoomType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showInfo, setShowInfo] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);

  const token = tokens?.accessToken || guestToken;

  useEffect(() => {
    if (!isLoading && roomId) {
      fetchRoomData();
    }
  }, [isLoading, roomId]);

  const fetchRoomData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await chatAPI.getChatRoom(roomId, token || undefined);
      setRoom(response.data.chatRoom);

      // Check if user has already joined
      if (response.data.chatRoom.participants) {
        let guestId = null;
        if (isGuest && guestToken) {
          try {
            const tokenPayload = JSON.parse(atob(guestToken.split(".")[1]));
            guestId = tokenPayload.userId || tokenPayload.guestId;
          } catch (e) {
            console.warn("Failed to decode guest token:", e);
          }
        }

        const userParticipant = response.data.chatRoom.participants.find(
          (p: any) =>
            (user && p.userId === user._id) ||
            (isGuest && guestId && p.anonymousId === guestId)
        );
        setHasJoined(!!userParticipant);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load chat room");
    } finally {
      setLoading(false);
    }
  };

  const handleJoinRoom = async () => {
    try {
      let displayName;

      if (isGuest && token) {
        // Extract guest display name from token
        try {
          const tokenPayload = JSON.parse(atob(token.split(".")[1]));
          displayName =
            tokenPayload.displayName || tokenPayload.guestDisplayName;
        } catch (e) {
          console.warn("Failed to decode guest token for display name:", e);
          displayName = ""; // Fallback to backend generation
        }
      } else if (user) {
        // Authenticated user
        displayName = `${user.firstName} ${user.lastName}`;
      } else {
        displayName = ""; // Fallback
      }

      console.log("DEBUG - Join Room Data:", {
        isGuest,
        isAuthenticated,
        user: user
          ? {
              _id: user._id,
              role: user.role,
              firstName: user.firstName,
              lastName: user.lastName,
            }
          : null,
        token: token ? "present" : "absent",
        displayName,
      });

      await chatAPI.joinChatRoom(roomId, displayName || "", token || undefined);
      setHasJoined(true);
      fetchRoomData(); // Refresh room data
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to join chat room");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (error || !room) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Chat Room Not Found
            </h1>
            <p className="text-gray-600 mb-6">
              {error ||
                "The chat room you're looking for doesn't exist or has been removed."}
            </p>
            <button
              onClick={() => router.push("/chat")}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md font-medium"
            >
              Back to Chat Rooms
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!hasJoined) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          {/* Back Button */}
          <button
            onClick={() => router.push("/chat")}
            className="inline-flex items-center text-purple-600 hover:text-purple-700 mb-6"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Chat Rooms
          </button>

          {/* Room Preview */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {room.name}
                  </h1>
                  <p className="text-gray-600 mb-4">{room.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center space-x-1">
                      <UserGroupIcon className="h-4 w-4" />
                      <span>{room.currentParticipants} participants</span>
                    </span>
                    <span className="capitalize">
                      {room.category.replace("-", " ")}
                    </span>
                    <span className="capitalize">{room.topic}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      room.isActive
                        ? "bg-primary-100 text-primary-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {room.isActive ? "Active" : "Inactive"}
                  </span>
                  {room.isModerated && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Moderated
                    </span>
                  )}
                </div>
              </div>

              {/* Room Rules */}
              {room.rules && room.rules.length > 0 && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="text-sm font-semibold text-blue-900 mb-2">
                    Room Rules
                  </h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    {room.rules.map((rule, index) => (
                      <li key={index}>• {rule}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Join Room */}
              <div className="text-center">
                {room.currentParticipants >= room.maxParticipants ? (
                  <div className="text-center py-4">
                    <p className="text-gray-600 mb-4">
                      This chat room is currently full ({room.maxParticipants}{" "}
                      participants).
                    </p>
                    <button
                      onClick={() => router.push("/chat")}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md font-medium"
                    >
                      Find Other Rooms
                    </button>
                  </div>
                ) : !room.isActive ? (
                  <div className="text-center py-4">
                    <p className="text-gray-600 mb-4">
                      This chat room is currently inactive.
                    </p>
                    <button
                      onClick={() => router.push("/chat")}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md font-medium"
                    >
                      Find Other Rooms
                    </button>
                  </div>
                ) : (
                  <div>
                    <p className="text-gray-600 mb-4">
                      Ready to join the conversation?
                    </p>
                    <button
                      onClick={handleJoinRoom}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-md font-medium text-lg"
                    >
                      Join Chat Room
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        {/* Main Chat Area */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* Chat Messages */}
          <div
            className={`${
              showInfo || showParticipants ? "lg:col-span-3" : "lg:col-span-4"
            }`}
          >
            <ChatRoom roomId={roomId} />
          </div>

          {/* Sidebar */}
          {(showInfo || showParticipants) && (
            <div className="lg:col-span-1 space-y-4">
              {showInfo && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Room Info
                    </h3>
                    <button
                      onClick={() => setShowInfo(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      ×
                    </button>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{room.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {room.description}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Category
                      </p>
                      <p className="text-sm text-gray-600 capitalize">
                        {room.category.replace("-", " ")}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Participants
                      </p>
                      <p className="text-sm text-gray-600">
                        {room.currentParticipants} / {room.maxParticipants}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Topic</p>
                      <p className="text-sm text-gray-600">{room.topic}</p>
                    </div>
                    {room.rules.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-2">
                          Rules
                        </p>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {room.rules.map((rule, index) => (
                            <li key={index} className="flex items-start">
                              <span className="mr-2">•</span>
                              <span>{rule}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {showParticipants && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Participants ({room.currentParticipants})
                    </h3>
                    <button
                      onClick={() => setShowParticipants(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      ×
                    </button>
                  </div>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {room.participants.map((participant, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-2 h-2 rounded-full ${
                              participant.isOnline
                                ? "bg-green-500"
                                : "bg-gray-300"
                            }`}
                          />
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {participant.displayName}
                            </p>
                            <p className="text-xs text-gray-500 capitalize">
                              {participant.role}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

import { Counselor, <PERSON><PERSON><PERSON><PERSON>lor } from "@/models/Counselor";
import { User } from "@/models/User";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { uploadToCloudinary } from "@/utils/cloudinary";
import { sendEmail } from "@/utils/email";

export interface CounselorRegistrationData {
  bio: string;
  specializations: string[];
  qualifications: {
    degree: string;
    institution: string;
    year: number;
    certificateUrl?: string;
  }[];
  licenses: {
    type: string;
    number: string;
    issuingAuthority: string;
    expiryDate: Date;
    documentUrl?: string;
  }[];
  experience: {
    years: number;
    description: string;
  };
  pricing: {
    currency: "NGN" | "USD";
    ratePerMinute: number;
    minimumSessionDuration: number;
  };
  profile: {
    languages: string[];
    approachDescription: string;
    sessionTypes: ("individual" | "group" | "couples" | "family")[];
  };
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    bankCode: string;
    currency: "NGN" | "USD";
  };
}

export interface AvailabilityData {
  timezone: string;
  schedule: Map<
    string,
    {
      isAvailable: boolean;
      timeSlots: {
        startTime: string;
        endTime: string;
      }[];
    }
  >;
  unavailableDates: Date[];
}

export interface CounselorFilters {
  specializations?: string[];
  minRating?: number;
  maxRate?: number;
  currency?: "NGN" | "USD";
  languages?: string[];
  sessionTypes?: string[];
  isAvailable?: boolean;
  verificationStatus?: "pending" | "approved" | "rejected" | "suspended";
}

export class CounselorService {
  /**
   * Register as a counselor
   */
  static async registerCounselor(
    userId: string,
    data: CounselorRegistrationData
  ): Promise<ICounselor> {
    try {
      // Check if user exists and is not already a counselor
      const user = await User.findById(userId);
      if (!user) {
        throw createError("User not found", 404);
      }

      const existingCounselor = await Counselor.findOne({ userId });
      if (existingCounselor) {
        throw createError("User is already registered as a counselor", 400);
      }

      // Create counselor profile
      const counselor = new Counselor({
        userId,
        bio: data.bio,
        specializations: data.specializations,
        qualifications: data.qualifications,
        licenses: data.licenses,
        experience: data.experience,
        pricing: data.pricing,
        profile: data.profile,
        bankDetails: data.bankDetails,
        verification: {
          status: "pending",
          submittedAt: new Date(),
          documents: {
            idDocument: { url: "", verified: false },
            certificates: [],
          },
        },
      });

      await counselor.save();

      // Update user role to counselor
      user.role = "counselor";
      await user.save();

      logger.info(`Counselor registration submitted: ${user.email}`);

      // Send confirmation email
      await this.sendRegistrationConfirmationEmail(user.email, user.firstName);

      return counselor;
    } catch (error) {
      logger.error("Counselor registration error:", error);
      throw error;
    }
  }

  /**
   * Get counselor profile
   */
  static async getCounselorProfile(counselorId: string): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findById(counselorId).populate(
        "userId",
        "-password -emailVerificationToken -passwordResetToken"
      );

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      return counselor;
    } catch (error) {
      logger.error("Get counselor profile error:", error);
      throw error;
    }
  }

  /**
   * Get counselor by user ID
   */
  static async getCounselorByUserId(userId: string): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findOne({ userId }).populate(
        "userId",
        "-password -emailVerificationToken -passwordResetToken"
      );

      if (!counselor) {
        throw createError("Counselor profile not found", 404);
      }

      return counselor;
    } catch (error) {
      logger.error("Get counselor by user ID error:", error);
      throw error;
    }
  }

  /**
   * Update counselor profile
   */
  static async updateCounselorProfile(
    counselorId: string,
    updateData: Partial<CounselorRegistrationData>
  ): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findById(counselorId);

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      // Update fields
      if (updateData.bio) counselor.bio = updateData.bio;
      if (updateData.specializations)
        counselor.specializations = updateData.specializations;
      if (updateData.qualifications)
        counselor.qualifications = updateData.qualifications;
      if (updateData.licenses) counselor.licenses = updateData.licenses;
      if (updateData.experience) counselor.experience = updateData.experience;
      if (updateData.pricing) counselor.pricing = updateData.pricing;
      if (updateData.profile) {
        counselor.profile = { ...counselor.profile, ...updateData.profile };
      }
      if (updateData.bankDetails)
        counselor.bankDetails = updateData.bankDetails;

      await counselor.save();
      logger.info(`Counselor profile updated: ${counselorId}`);

      return counselor;
    } catch (error) {
      logger.error("Update counselor profile error:", error);
      throw error;
    }
  }

  /**
   * Update availability
   */
  static async updateAvailability(
    counselorId: string,
    availabilityData: AvailabilityData
  ): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findById(counselorId);

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      counselor.availability = {
        ...counselor.availability,
        ...availabilityData,
        availabilityExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      };

      await counselor.save();
      logger.info(`Availability updated for counselor: ${counselorId}`);

      return counselor;
    } catch (error) {
      logger.error("Update availability error:", error);
      throw error;
    }
  }

  /**
   * Upload verification documents
   */
  static async uploadVerificationDocuments(
    counselorId: string,
    idDocument: Express.Multer.File,
    certificates: Express.Multer.File[]
  ): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findById(counselorId);

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      // Upload ID document
      const idDocumentUrl = await uploadToCloudinary(
        idDocument,
        "verification/id-documents"
      );
      counselor.verification.documents.idDocument = {
        url: idDocumentUrl,
        verified: false,
      };

      // Upload certificates
      const certificateUploads = await Promise.all(
        certificates.map(async (cert) => {
          const url = await uploadToCloudinary(
            cert,
            "verification/certificates"
          );
          return {
            url,
            verified: false,
            type: cert.originalname,
          };
        })
      );

      counselor.verification.documents.certificates = certificateUploads;
      counselor.verification.submittedAt = new Date();

      await counselor.save();
      logger.info(
        `Verification documents uploaded for counselor: ${counselorId}`
      );

      return counselor;
    } catch (error) {
      logger.error("Upload verification documents error:", error);
      throw error;
    }
  }

  /**
   * Get counselors with filters and pagination
   */
  static async getCounselors(
    filters: CounselorFilters = {},
    options: any = {}
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "statistics.averageRating",
        sortOrder = "desc",
      } = options;

      // Build query
      const query: any = { isActive: true };

      if (filters.specializations?.length) {
        query.specializations = { $in: filters.specializations };
      }

      if (filters.minRating) {
        query["statistics.averageRating"] = { $gte: filters.minRating };
      }

      if (filters.maxRate) {
        query["pricing.ratePerMinute"] = { $lte: filters.maxRate };
      }

      if (filters.currency) {
        query["pricing.currency"] = filters.currency;
      }

      if (filters.languages?.length) {
        query["profile.languages"] = { $in: filters.languages };
      }

      if (filters.sessionTypes?.length) {
        query["profile.sessionTypes"] = { $in: filters.sessionTypes };
      }

      if (filters.verificationStatus) {
        query["verification.status"] = filters.verificationStatus;
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === "asc" ? 1 : -1;

      const [counselors, total] = await Promise.all([
        Counselor.find(query)
          .populate("userId", "firstName lastName profilePicture")
          .sort(sortOptions)
          .skip(skip)
          .limit(limit),
        Counselor.countDocuments(query),
      ]);

      return {
        counselors,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get counselors error:", error);
      throw error;
    }
  }

  /**
   * Approve counselor (admin only)
   */
  static async approveCounselor(
    counselorId: string,
    adminId: string
  ): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findById(counselorId).populate(
        "userId"
      );

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      counselor.verification.status = "approved";
      counselor.verification.reviewedAt = new Date();
      counselor.verification.reviewedBy = adminId as any;

      await counselor.save();

      // Send approval email
      const user = counselor.userId as any;
      await this.sendApprovalEmail(user.email, user.firstName);

      logger.info(`Counselor approved: ${counselorId} by admin: ${adminId}`);

      return counselor;
    } catch (error) {
      logger.error("Approve counselor error:", error);
      throw error;
    }
  }

  /**
   * Reject counselor (admin only)
   */
  static async rejectCounselor(
    counselorId: string,
    adminId: string,
    reason: string
  ): Promise<ICounselor> {
    try {
      const counselor = await Counselor.findById(counselorId).populate(
        "userId"
      );

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      counselor.verification.status = "rejected";
      counselor.verification.reviewedAt = new Date();
      counselor.verification.reviewedBy = adminId as any;
      counselor.verification.rejectionReason = reason;

      await counselor.save();

      // Send rejection email
      const user = counselor.userId as any;
      await this.sendRejectionEmail(user.email, user.firstName, reason);

      logger.info(`Counselor rejected: ${counselorId} by admin: ${adminId}`);

      return counselor;
    } catch (error) {
      logger.error("Reject counselor error:", error);
      throw error;
    }
  }

  /**
   * Send registration confirmation email
   */
  private static async sendRegistrationConfirmationEmail(
    email: string,
    firstName: string
  ): Promise<void> {
    const html = `
      <h2>Counselor Application Received</h2>
      <p>Hi ${firstName},</p>
      <p>Thank you for applying to become a counselor on Theramea!</p>
      <p>Your application is currently under review. We'll notify you once the review is complete.</p>
      <p>This process typically takes 3-5 business days.</p>
      <p>Best regards,<br>The Theramea Team</p>
    `;

    await sendEmail({
      to: email,
      subject: "Counselor Application Received - Theramea",
      html,
    });
  }

  /**
   * Send approval email
   */
  private static async sendApprovalEmail(
    email: string,
    firstName: string
  ): Promise<void> {
    const html = `
      <h2>Congratulations! Your Counselor Application Approved</h2>
      <p>Hi ${firstName},</p>
      <p>Great news! Your application to become a counselor on Theramea has been approved.</p>
      <p>You can now:</p>
      <ul>
        <li>Set your availability</li>
        <li>Start accepting session bookings</li>
        <li>Manage your counselor profile</li>
      </ul>
      <p>Welcome to the Theramea counselor community!</p>
      <p>Best regards,<br>The Theramea Team</p>
    `;

    await sendEmail({
      to: email,
      subject: "Counselor Application Approved - Welcome to Theramea!",
      html,
    });
  }

  /**
   * Send rejection email
   */
  private static async sendRejectionEmail(
    email: string,
    firstName: string,
    reason: string
  ): Promise<void> {
    const html = `
      <h2>Counselor Application Update</h2>
      <p>Hi ${firstName},</p>
      <p>Thank you for your interest in becoming a counselor on Theramea.</p>
      <p>After careful review, we're unable to approve your application at this time.</p>
      <p><strong>Reason:</strong> ${reason}</p>
      <p>You're welcome to reapply once you've addressed the feedback above.</p>
      <p>Best regards,<br>The Theramea Team</p>
    `;

    await sendEmail({
      to: email,
      subject: "Counselor Application Update - Theramea",
      html,
    });
  }
}

"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { bookingAPI } from "@/lib/booking";
import Header from "@/components/layout/Header";
import { Session } from "@/types/booking";
import {
  CheckCircleIcon,
  CalendarIcon,
  ClockIcon,
  UserIcon,
  VideoCameraIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  ShareIcon,
} from "@heroicons/react/24/outline";

export default function BookingConfirmationPage() {
  const router = useRouter();
  const params = useParams();
  const sessionId = params.id as string;

  const { isAuthenticated, user, tokens, isLoading } = useAuthStore();
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (sessionId && tokens?.accessToken) {
      fetchSession();
    }
  }, [isAuthenticated, isLoading, sessionId, tokens, router]);

  const fetchSession = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await bookingAPI.getBooking(
        sessionId,
        tokens!.accessToken
      );
      setSession(response.data.session);

      // If payment is not completed, redirect to payment page
      if (response.data.session.status !== "completed") {
        router.push(`/booking/${sessionId}/payment`);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load session details"
      );
    } finally {
      setLoading(false);
    }
  };

  const getSessionTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <VideoCameraIcon className="h-5 w-5" />;
      case "audio":
        return <PhoneIcon className="h-5 w-5" />;
      case "chat":
        return <ChatBubbleLeftRightIcon className="h-5 w-5" />;
      default:
        return <VideoCameraIcon className="h-5 w-5" />;
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === "NGN" ? "₦" : "$";
    return `${symbol}${amount.toLocaleString()}`;
  };

  const addToCalendar = () => {
    if (!session) return;

    const startDate = new Date(session.scheduledAt);
    const endDate = new Date(startDate.getTime() + session.duration * 60000);

    const event = {
      title: `Counseling Session with ${
        session.counselor?.user?.firstName || ""
      } ${session.counselor?.user?.lastName || ""}`,
      start: startDate.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z",
      end: endDate.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z",
      description: `Counseling session booked through Theramea. Session ID: ${session._id}`,
      location: "Online - Theramea Platform",
    };

    const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(
      event.title
    )}&dates=${event.start}/${event.end}&details=${encodeURIComponent(
      event.description
    )}&location=${encodeURIComponent(event.location)}`;

    window.open(calendarUrl, "_blank");
  };

  const downloadReceipt = async () => {
    try {
      // TODO: Implement receipt download API
      console.log("Receipt download not yet implemented");
      // const response = await bookingAPI.downloadReceipt(sessionId, tokens!.accessToken);

      // For now, just show a message
      alert("Receipt download feature coming soon!");
    } catch (err) {
      console.error("Failed to download receipt:", err);
    }
  };

  const shareSession = () => {
    if (navigator.share) {
      navigator.share({
        title: "Theramea Session Booked",
        text: `I've booked a counseling session on Theramea for ${new Date(
          session!.scheduledAt
        ).toLocaleDateString()}`,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert("Session link copied to clipboard!");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Booking Not Found
            </h1>
            <p className="text-gray-600 mb-6">
              {error || "Session details could not be loaded."}
            </p>
            <button
              onClick={() => router.push("/dashboard")}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md font-medium"
            >
              Return to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <CheckCircleIcon className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Booking Confirmed!
          </h1>
          <p className="text-lg text-gray-600">
            Your counseling session has been successfully booked and paid for.
          </p>
        </div>

        {/* Session Details Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Session Details
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <UserIcon className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Counselor</p>
                  <p className="text-sm text-gray-600">
                    {session.counselor?.user?.firstName}{" "}
                    {session.counselor?.user?.lastName}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <CalendarIcon className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Date</p>
                  <p className="text-sm text-gray-600">
                    {new Date(session.scheduledAt).toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <ClockIcon className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Time & Duration
                  </p>
                  <p className="text-sm text-gray-600">
                    {new Date(session.scheduledAt).toLocaleTimeString("en-US", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}{" "}
                    ({session.duration} minutes)
                  </p>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                {getSessionTypeIcon(session.sessionType || session.type)}
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Session Type
                  </p>
                  <p className="text-sm text-gray-600 capitalize">
                    {session.sessionType} session
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Session ID
                  </p>
                  <p className="text-sm text-gray-600 font-mono">
                    {session._id.slice(-8).toUpperCase()}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <CheckCircleIcon className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Payment Status
                  </p>
                  <p className="text-sm text-green-600 font-medium">
                    Paid -{" "}
                    {formatCurrency(
                      session.totalAmount || session.pricing.totalAmount || 0,
                      session.currency || session.pricing.currency
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {session.notes && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm font-medium text-gray-900 mb-2">
                Your Notes
              </p>
              <div className="bg-gray-50 rounded-md p-3">
                <p className="text-sm text-gray-700">
                  {session.notes?.counselorNotes ||
                    session.notes?.sessionSummary ||
                    "No additional notes"}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <button
            onClick={addToCalendar}
            className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md font-medium transition-colors"
          >
            <CalendarIcon className="h-5 w-5" />
            <span>Add to Calendar</span>
          </button>

          <button
            onClick={downloadReceipt}
            className="flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-md font-medium transition-colors"
          >
            <ArrowDownTrayIcon className="h-5 w-5" />
            <span>Download Receipt</span>
          </button>

          <button
            onClick={shareSession}
            className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-md font-medium transition-colors"
          >
            <ShareIcon className="h-5 w-5" />
            <span>Share</span>
          </button>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            What's Next?
          </h3>
          <div className="space-y-3 text-sm text-blue-800">
            <div className="flex items-start space-x-2">
              <span className="font-bold">1.</span>
              <span>
                You'll receive a confirmation email with session details and
                join instructions
              </span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-bold">2.</span>
              <span>
                15 minutes before your session, you'll get a reminder
                notification
              </span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-bold">3.</span>
              <span>
                Join your session from your dashboard or the link in your email
              </span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-bold">4.</span>
              <span>
                If you need to reschedule, you can do so up to 24 hours before
                the session
              </span>
            </div>
          </div>
        </div>

        {/* Support */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Need Help?
          </h3>
          <p className="text-gray-600 mb-4">
            If you have any questions or need to make changes to your booking,
            our support team is here to help.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl mb-2">📧</div>
              <p className="text-sm font-medium text-gray-900">Email Support</p>
              <p className="text-sm text-gray-600"><EMAIL></p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">📞</div>
              <p className="text-sm font-medium text-gray-900">Phone Support</p>
              <p className="text-sm text-gray-600">+234 (0) ************</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">💬</div>
              <p className="text-sm font-medium text-gray-900">Live Chat</p>
              <button className="text-sm text-purple-600 hover:text-purple-700">
                Start Chat
              </button>
            </div>
          </div>
        </div>

        {/* Return to Dashboard */}
        <div className="text-center mt-8">
          <button
            onClick={() => router.push("/dashboard")}
            className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-md font-medium transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}

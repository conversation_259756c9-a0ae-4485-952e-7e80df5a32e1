"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";

const MOOD_OPTIONS = [
  { value: 1, emoji: "😔", label: "Very Sad", color: "text-blue-600" },
  { value: 2, emoji: "😟", label: "Sad", color: "text-blue-500" },
  { value: 3, emoji: "😐", label: "Neutral", color: "text-gray-500" },
  { value: 4, emoji: "🙂", label: "Happy", color: "text-green-500" },
  { value: 5, emoji: "😄", label: "Very Happy", color: "text-green-600" },
];

export default function MoodOnboardingPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuthStore();
  const [selectedMood, setSelectedMood] = useState<number | null>(null);
  const [moodNote, setMoodNote] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleContinue = async () => {
    if (selectedMood === null) {
      return; // Don't allow empty selection
    }

    setIsSubmitting(true);

    try {
      // TODO: Save mood data to user profile
      const moodData = {
        mood: selectedMood,
        note: moodNote.trim(),
        timestamp: new Date().toISOString(),
      };

      localStorage.setItem("onboarding_mood", JSON.stringify(moodData));

      // Complete onboarding and redirect to dashboard
      router.push("/dashboard");
    } catch (error) {
      console.error("Error saving mood:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    router.push("/dashboard");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  const selectedMoodData = MOOD_OPTIONS.find(
    (option) => option.value === selectedMood
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">💭</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            How are you feeling today?
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Let us know your current mood to better personalize your experience.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
            <div className="w-8 h-2 bg-purple-600 rounded-full"></div>
            <span>Step 2 of 2</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          {/* Mood Scale */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {MOOD_OPTIONS.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSelectedMood(option.value)}
                  className={`
                    flex flex-col items-center p-3 rounded-lg transition-all duration-200
                    ${
                      selectedMood === option.value
                        ? "bg-purple-100 border-2 border-purple-500 scale-110"
                        : "bg-gray-50 border-2 border-transparent hover:bg-purple-50 hover:border-purple-300"
                    }
                  `}
                >
                  <span className="text-4xl mb-2">{option.emoji}</span>
                  <span className={`text-sm font-medium ${option.color}`}>
                    {option.label}
                  </span>
                </button>
              ))}
            </div>

            {selectedMoodData && (
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">
                  You're feeling:{" "}
                  <span className={selectedMoodData.color}>
                    {selectedMoodData.label}
                  </span>
                </p>
              </div>
            )}
          </div>

          {/* Optional Note */}
          <div>
            <label
              htmlFor="moodNote"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Want to tell us more? (Optional)
            </label>
            <textarea
              id="moodNote"
              rows={3}
              value={moodNote}
              onChange={(e) => setMoodNote(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
              placeholder="Share what's on your mind..."
              maxLength={500}
            />
            <p className="mt-1 text-xs text-gray-500">
              {moodNote.length}/500 characters
            </p>
          </div>
        </div>

        <div className="flex space-x-4">
          <button
            onClick={handleSkip}
            className="flex-1 py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Skip for now
          </button>
          <button
            onClick={handleContinue}
            disabled={selectedMood === null || isSubmitting}
            className="flex-1 py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Completing setup...
              </div>
            ) : (
              "Complete Setup"
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

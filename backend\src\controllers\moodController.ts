import { Request, Response } from "express";
import { MoodEntry, IMoodEntry } from "@/models/MoodEntry";
import { Resource } from "@/models/Resource";
import { createError } from "@/middleware/errorHandler";
import { AuthRequest } from "@/middleware/auth";

export class MoodController {
  /**
   * Create a new mood entry
   */
  static async createMoodEntry(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const moodData = {
        ...req.body,
        userId,
        metadata: {
          ...req.body.metadata,
          source: req.body.metadata?.source || "manual",
          deviceType: req.headers["user-agent"]?.includes("Mobile")
            ? "mobile"
            : "desktop",
        },
      };

      const moodEntry = new MoodEntry(moodData);
      await moodEntry.save();

      res.status(201).json({
        success: true,
        message: "Mood entry created successfully",
        data: moodEntry,
      });
    } catch (error: any) {
      console.error("Error creating mood entry:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create mood entry",
      });
    }
  }

  /**
   * Get user's mood entries with filtering and pagination
   */
  static async getMoodEntries(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const {
        page = 1,
        limit = 20,
        startDate,
        endDate,
        mood,
        tags,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = req.query;

      // Build filter query
      const filter: any = { userId };

      if (startDate || endDate) {
        filter.createdAt = {};
        if (startDate) filter.createdAt.$gte = new Date(startDate as string);
        if (endDate) filter.createdAt.$lte = new Date(endDate as string);
      }

      if (mood) {
        filter.mood = parseInt(mood as string);
      }

      if (tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags];
        filter.tags = { $in: tagArray };
      }

      // Calculate pagination
      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const sortOptions: any = {};
      sortOptions[sortBy as string] = sortOrder === "desc" ? -1 : 1;

      // Execute query
      const [entries, total] = await Promise.all([
        MoodEntry.find(filter)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit as string))
          .lean(),
        MoodEntry.countDocuments(filter),
      ]);

      res.json({
        success: true,
        data: {
          entries,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            pages: Math.ceil(total / parseInt(limit as string)),
          },
        },
      });
    } catch (error: any) {
      console.error("Error fetching mood entries:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch mood entries",
      });
    }
  }

  /**
   * Get a specific mood entry
   */
  static async getMoodEntry(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const { entryId } = req.params;

      const moodEntry = await MoodEntry.findOne({
        _id: entryId,
        userId,
      });

      if (!moodEntry) {
        throw createError("Mood entry not found", 404);
      }

      res.json({
        success: true,
        data: moodEntry,
      });
    } catch (error: any) {
      console.error("Error fetching mood entry:", error);
      res.status(error.statusCode || 500).json({
        success: false,
        message: error.message || "Failed to fetch mood entry",
      });
    }
  }

  /**
   * Update a mood entry
   */
  static async updateMoodEntry(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const { entryId } = req.params;

      const moodEntry = await MoodEntry.findOneAndUpdate(
        { _id: entryId, userId },
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      if (!moodEntry) {
        throw createError("Mood entry not found", 404);
      }

      res.json({
        success: true,
        message: "Mood entry updated successfully",
        data: moodEntry,
      });
    } catch (error: any) {
      console.error("Error updating mood entry:", error);
      res.status(error.statusCode || 400).json({
        success: false,
        message: error.message || "Failed to update mood entry",
      });
    }
  }

  /**
   * Delete a mood entry
   */
  static async deleteMoodEntry(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const { entryId } = req.params;

      const moodEntry = await MoodEntry.findOneAndDelete({
        _id: entryId,
        userId,
      });

      if (!moodEntry) {
        throw createError("Mood entry not found", 404);
      }

      res.json({
        success: true,
        message: "Mood entry deleted successfully",
      });
    } catch (error: any) {
      console.error("Error deleting mood entry:", error);
      res.status(error.statusCode || 500).json({
        success: false,
        message: error.message || "Failed to delete mood entry",
      });
    }
  }

  /**
   * Get mood analytics and insights
   */
  static async getMoodAnalytics(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const { period = "30d" } = req.query;

      // Calculate date range
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case "7d":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "30d":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case "90d":
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case "1y":
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      // Aggregation pipeline for analytics
      const analytics = await MoodEntry.aggregate([
        {
          $match: {
            userId: userId,
            createdAt: { $gte: startDate, $lte: now },
          },
        },
        {
          $group: {
            _id: null,
            avgMood: { $avg: "$mood" },
            avgEnergy: { $avg: "$energy" },
            avgAnxiety: { $avg: "$anxiety" },
            totalEntries: { $sum: 1 },
            moodDistribution: {
              $push: {
                mood: "$mood",
                energy: "$energy",
                anxiety: "$anxiety",
                date: "$createdAt",
              },
            },
            commonTags: { $push: "$tags" },
            commonTriggers: { $push: "$triggers" },
            commonActivities: { $push: "$activities" },
          },
        },
      ]);

      // Get daily mood trends
      const dailyTrends = await MoodEntry.aggregate([
        {
          $match: {
            userId: userId,
            createdAt: { $gte: startDate, $lte: now },
          },
        },
        {
          $group: {
            _id: {
              $dateToString: { format: "%Y-%m-%d", date: "$createdAt" },
            },
            avgMood: { $avg: "$mood" },
            avgEnergy: { $avg: "$energy" },
            avgAnxiety: { $avg: "$anxiety" },
            entryCount: { $sum: 1 },
          },
        },
        {
          $sort: { _id: 1 },
        },
      ]);

      const result = analytics[0] || {
        avgMood: 0,
        avgEnergy: 0,
        avgAnxiety: 0,
        totalEntries: 0,
        moodDistribution: [],
        commonTags: [],
        commonTriggers: [],
        commonActivities: [],
      };

      // Process common tags, triggers, and activities
      const flatTags = result.commonTags.flat().filter(Boolean);
      const flatTriggers = result.commonTriggers.flat().filter(Boolean);
      const flatActivities = result.commonActivities.flat().filter(Boolean);

      const tagCounts = flatTags.reduce((acc: any, tag: string) => {
        acc[tag] = (acc[tag] || 0) + 1;
        return acc;
      }, {});

      const triggerCounts = flatTriggers.reduce((acc: any, trigger: string) => {
        acc[trigger] = (acc[trigger] || 0) + 1;
        return acc;
      }, {});

      const activityCounts = flatActivities.reduce(
        (acc: any, activity: string) => {
          acc[activity] = (acc[activity] || 0) + 1;
          return acc;
        },
        {}
      );

      res.json({
        success: true,
        data: {
          summary: {
            averageMood: Math.round(result.avgMood * 100) / 100,
            averageEnergy: Math.round(result.avgEnergy * 100) / 100,
            averageAnxiety: Math.round(result.avgAnxiety * 100) / 100,
            totalEntries: result.totalEntries,
            period: period,
          },
          trends: dailyTrends,
          insights: {
            topTags: Object.entries(tagCounts)
              .sort(([, a], [, b]) => (b as number) - (a as number))
              .slice(0, 5),
            topTriggers: Object.entries(triggerCounts)
              .sort(([, a], [, b]) => (b as number) - (a as number))
              .slice(0, 5),
            topActivities: Object.entries(activityCounts)
              .sort(([, a], [, b]) => (b as number) - (a as number))
              .slice(0, 5),
          },
        },
      });
    } catch (error: any) {
      console.error("Error fetching mood analytics:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch mood analytics",
      });
    }
  }

  /**
   * Get personalized feedback based on mood entry and user history
   */
  static async getPersonalizedFeedback(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const moodEntry = req.body;

      // Get user's recent mood history for context
      const recentEntries = await MoodEntry.find({ userId })
        .sort({ createdAt: -1 })
        .limit(7);

      // Generate personalized feedback
      const feedback = await MoodController.generatePersonalizedFeedback(
        moodEntry,
        recentEntries
      );

      res.json({
        success: true,
        data: { feedback },
      });
    } catch (error: any) {
      console.error("Error generating personalized feedback:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate personalized feedback",
      });
    }
  }

  /**
   * Get resource suggestions based on mood entry
   */
  static async getResourceSuggestions(req: AuthRequest, res: Response) {
    try {
      const userId = req.user!._id;
      const moodEntry = req.body;

      // Get user's mood history for better suggestions
      const userHistory = await MoodEntry.find({ userId })
        .sort({ createdAt: -1 })
        .limit(10);

      // Generate resource suggestions
      const suggestions = await MoodController.generateResourceSuggestions(
        moodEntry,
        userHistory
      );

      res.json({
        success: true,
        data: { suggestions },
      });
    } catch (error: any) {
      console.error("Error generating resource suggestions:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate resource suggestions",
      });
    }
  }

  /**
   * Generate personalized feedback based on mood entry and history
   */
  private static async generatePersonalizedFeedback(
    currentEntry: any,
    recentEntries: IMoodEntry[]
  ): Promise<string> {
    const { mood, energy, anxiety, triggers, tags, note } = currentEntry;

    // Analyze patterns from recent entries
    const avgMood =
      recentEntries.length > 0
        ? recentEntries.reduce((sum, entry) => sum + entry.mood, 0) /
          recentEntries.length
        : mood;

    const avgAnxiety =
      recentEntries.length > 0
        ? recentEntries.reduce((sum, entry) => sum + entry.anxiety, 0) /
          recentEntries.length
        : anxiety;

    const avgEnergy =
      recentEntries.length > 0
        ? recentEntries.reduce((sum, entry) => sum + entry.energy, 0) /
          recentEntries.length
        : energy;

    // Common triggers from recent entries
    const commonTriggers = recentEntries
      .flatMap((entry) => entry.triggers || [])
      .reduce((acc: Record<string, number>, trigger) => {
        acc[trigger] = (acc[trigger] || 0) + 1;
        return acc;
      }, {});

    // Generate contextual feedback
    const feedbackMessages: string[] = [];

    // Current mood feedback
    if (mood <= 2) {
      if (anxiety >= 4) {
        feedbackMessages.push(
          "I notice you're feeling down and anxious. These feelings are valid, and acknowledging them shows self-awareness and courage."
        );
      } else {
        feedbackMessages.push(
          "Thank you for sharing how you're feeling. It takes strength to acknowledge difficult emotions, and you're taking an important step by tracking them."
        );
      }
    } else if (mood >= 4) {
      if (avgMood < 3) {
        feedbackMessages.push(
          "It's wonderful to see you feeling good today! This positive shift is meaningful - try to notice what might have contributed to this improvement."
        );
      } else {
        feedbackMessages.push(
          "Your positive mood is beautiful to see! These moments of joy and contentment are important to celebrate and remember."
        );
      }
    } else {
      feedbackMessages.push(
        "Thank you for taking time to check in with yourself. This kind of self-awareness is a powerful foundation for emotional wellbeing."
      );
    }

    // Pattern-based insights
    if (recentEntries.length >= 3) {
      if (avgAnxiety > 3.5) {
        feedbackMessages.push(
          "I've noticed your anxiety levels have been elevated recently. Remember that anxiety often signals our mind trying to protect us, even when we're safe."
        );
      }

      if (avgEnergy < 2.5) {
        feedbackMessages.push(
          "Your energy has been lower lately. This could be your body's way of asking for more rest, better nutrition, or gentle movement."
        );
      }

      // Trigger patterns
      const topTrigger = Object.entries(commonTriggers).sort(
        ([, a], [, b]) => b - a
      )[0];

      if (topTrigger && topTrigger[1] >= 2) {
        const triggerName = topTrigger[0];
        if (triggerName.includes("work")) {
          feedbackMessages.push(
            "Work stress has been showing up frequently in your entries. Consider setting small boundaries or planning brief moments of calm during your workday."
          );
        } else if (triggerName.includes("sleep")) {
          feedbackMessages.push(
            "Sleep challenges seem to be affecting your mood regularly. Even small improvements to your sleep routine can make a meaningful difference."
          );
        } else if (triggerName.includes("social")) {
          feedbackMessages.push(
            "Social situations have been triggering for you lately. It's okay to take social interactions at your own pace and prioritize what feels comfortable."
          );
        }
      }
    }

    // Return a random selection or combine messages
    return feedbackMessages.join(" ");
  }

  /**
   * Generate resource suggestions based on mood entry and history
   */
  private static async generateResourceSuggestions(
    currentEntry: any,
    userHistory: IMoodEntry[]
  ): Promise<any[]> {
    const { mood, energy, anxiety, triggers, tags } = currentEntry;

    // Determine primary concern
    let primaryCategory = "";
    let searchTags: string[] = [];
    let resourceType = "article";

    // Analyze current state
    if (mood <= 2) {
      primaryCategory = "depression-support";
      searchTags = ["depression", "sadness", "coping", "support"];
      resourceType = anxiety >= 4 ? "tool" : "article";
    } else if (anxiety >= 4) {
      primaryCategory = "anxiety-management";
      searchTags = ["anxiety", "stress", "breathing", "calm", "relaxation"];
      resourceType = "tool"; // Prefer interactive tools for anxiety
    } else if (energy <= 2) {
      primaryCategory = "self-care";
      searchTags = ["energy", "motivation", "self-care", "wellness"];
      resourceType = "guide";
    } else if (triggers?.some((t) => t.toLowerCase().includes("work"))) {
      primaryCategory = "workplace-wellness";
      searchTags = ["work", "stress", "balance", "boundaries"];
      resourceType = "article";
    } else if (
      triggers?.some((t) => t.toLowerCase().includes("relationship"))
    ) {
      primaryCategory = "relationship-skills";
      searchTags = ["relationships", "communication", "boundaries"];
      resourceType = "guide";
    } else {
      // For neutral/positive moods, suggest growth resources
      primaryCategory = "mindfulness-meditation";
      searchTags = ["mindfulness", "meditation", "growth", "wellbeing"];
      resourceType = "tool";
    }

    // Build query for resources
    const query: any = {
      isPublished: true,
      category: primaryCategory,
      tags: { $in: searchTags },
    };

    // Prefer beginner-friendly resources
    if (userHistory.length < 5) {
      query.difficulty = "beginner";
    }

    try {
      // Get matching resources
      const resources = await Resource.find(query)
        .sort({
          "statistics.averageRating": -1,
          "statistics.views": -1,
        })
        .limit(3)
        .select(
          "title description seo.slug type category estimatedReadTime estimatedDuration"
        );

      // If no specific resources found, get general wellness resources
      if (resources.length === 0) {
        const fallbackResources = await Resource.find({
          isPublished: true,
          category: "mindfulness-meditation",
          difficulty: "beginner",
        })
          .sort({ "statistics.averageRating": -1 })
          .limit(1)
          .select(
            "title description seo.slug type category estimatedReadTime estimatedDuration"
          );

        return fallbackResources;
      }

      return resources;
    } catch (error) {
      console.error("Error fetching resources:", error);
      return [];
    }
  }
}

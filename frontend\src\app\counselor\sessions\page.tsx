"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { counselorPortalAPI } from "@/lib/counselorPortal";
import CounselorLayout from "@/components/counselor/CounselorLayout";
import SessionCard from "@/components/counselor/SessionCard";
import { Session } from "@/types/counselor";
import {
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

type SessionStatus =
  | "all"
  | "scheduled"
  | "completed"
  | "cancelled"
  | "inProgress";

export default function SessionsPage() {
  const router = useRouter();
  const { isAuthenticated, user, tokens } = useAuthStore();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [activeFilter, setActiveFilter] = useState<SessionStatus>("all");

  useEffect(() => {
    if (!isAuthenticated || user?.role !== "counselor") {
      router.push("/auth/login");
      return;
    }

    fetchSessions();
  }, [isAuthenticated, user, router, currentPage, activeFilter]);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      setError(null);

      const status = activeFilter === "all" ? undefined : activeFilter;
      const response = await counselorPortalAPI.getMySessions(
        status,
        currentPage,
        10,
        tokens!.accessToken
      );

      setSessions(response.data.sessions);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load sessions");
    } finally {
      setLoading(false);
    }
  };

  const handleSessionAction = async (
    sessionId: string,
    action: "approve" | "reject" | "cancel",
    reason?: string
  ) => {
    try {
      setError(null);

      switch (action) {
        case "approve":
          await counselorPortalAPI.approveSession(
            sessionId,
            tokens!.accessToken
          );
          break;
        case "reject":
          await counselorPortalAPI.rejectSession(
            sessionId,
            reason || "",
            tokens!.accessToken
          );
          break;
        case "cancel":
          await counselorPortalAPI.cancelSession(
            sessionId,
            reason || "",
            tokens!.accessToken
          );
          break;
      }

      // Refresh sessions list
      fetchSessions();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : `Failed to ${action} session`
      );
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "scheduled":
        return <CalendarIcon className="h-5 w-5 text-blue-600" />;
      case "completed":
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case "cancelled":
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
      case "inProgress":
        return <ExclamationTriangleIcon className="h-5 w-5 text-blue-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "inProgress":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const sessionCounts = {
    all: sessions.length,
    scheduled: sessions.filter((s) => s.status === "scheduled").length,
    inProgress: sessions.filter((s) => s.status === "in-progress").length,
    completed: sessions.filter((s) => s.status === "completed").length,
    cancelled: sessions.filter((s) => s.status === "cancelled").length,
  };

  if (loading && sessions.length === 0) {
    return (
      <CounselorLayout>
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </CounselorLayout>
    );
  }

  return (
    <CounselorLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <UserGroupIcon className="h-8 w-8 text-purple-600 mr-3" />
                Session Management
              </h1>
              <p className="mt-2 text-gray-600">
                Manage your counseling sessions and client bookings
              </p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Session Filters */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex flex-wrap gap-2">
            {(
              [
                "all",
                "inProgress",
                "scheduled",
                "completed",
                "cancelled",
              ] as SessionStatus[]
            ).map((status) => (
              <button
                key={status}
                onClick={() => {
                  setActiveFilter(status);
                  setCurrentPage(1);
                }}
                className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeFilter === status
                    ? "bg-purple-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {getStatusIcon(status)}
                <span className="ml-2 capitalize">{status}</span>
                <span className="ml-2 bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">
                  {sessionCounts[status]}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Sessions List */}
        <div className="bg-white shadow rounded-lg">
          {sessions.length === 0 ? (
            <div className="text-center py-12">
              <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No sessions found
              </h3>
              <p className="text-gray-600">
                {activeFilter === "all"
                  ? "You don't have any sessions yet."
                  : `No ${activeFilter} sessions found.`}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {sessions.map((session) => (
                <SessionCard
                  key={session._id}
                  session={session}
                  onAction={handleSessionAction}
                />
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {sessionCounts.inProgress}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Scheduled</p>
                <p className="text-2xl font-bold text-gray-900">
                  {sessionCounts.scheduled}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {sessionCounts.completed}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Cancelled</p>
                <p className="text-2xl font-bold text-gray-900">
                  {sessionCounts.cancelled}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CounselorLayout>
  );
}

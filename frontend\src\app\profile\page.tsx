"use client";

import { useState, useEffect } from "react";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";
import ProfileForm from "@/components/profile/ProfileForm";
import ProfilePicture from "@/components/profile/ProfilePicture";
import PreferencesSection from "@/components/profile/PreferencesSection";
import AccountSection from "@/components/profile/AccountSection";
import {
  UserIcon,
  CogIcon,
  ShieldCheckIcon,
  BellIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

export default function ProfilePage() {
  const { user, isLoading } = useAuthStore();
  const [activeTab, setActiveTab] = useState("profile");
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
            <p className="text-gray-600">Loading your profile...</p>
          </div>
        </div>
      </div>
    );
  }

  // If we reach here, the middleware has already verified authentication
  // Just ensure we have user data
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
            <p className="text-gray-600">Loading user data...</p>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    {
      id: "profile",
      name: "Profile Information",
      icon: UserIcon,
      description: "Manage your personal information",
    },
    {
      id: "preferences",
      name: "Preferences",
      icon: CogIcon,
      description: "Customize your experience",
    },
    {
      id: "notifications",
      name: "Notifications",
      icon: BellIcon,
      description: "Control notification settings",
    },
    {
      id: "account",
      name: "Account & Security",
      icon: ShieldCheckIcon,
      description: "Security and privacy settings",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
      <Header />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="mb-8">
          <div className="text-center md:text-left">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Welcome back, {user.firstName || "User"}!
            </h1>
            <p className="text-lg text-gray-600">
              Manage your account settings and personalize your experience
            </p>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobile ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">
                Settings
              </h2>
              <div className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? "bg-purple-50 text-purple-700 border border-purple-200"
                          : "hover:bg-gray-50 text-gray-700"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="h-5 w-5" />
                        <div className="text-left">
                          <div className="font-medium">{tab.name}</div>
                          <div className="text-sm text-gray-500">
                            {tab.description}
                          </div>
                        </div>
                      </div>
                      <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        ) : (
          /* Desktop Layout */
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Settings
                  </h2>
                </div>
                <nav className="p-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-colors ${
                          activeTab === tab.id
                            ? "bg-purple-50 text-purple-700 border border-purple-200"
                            : "hover:bg-gray-50 text-gray-700"
                        }`}
                      >
                        <Icon className="h-5 w-5 flex-shrink-0" />
                        <div>
                          <div className="font-medium">{tab.name}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {tab.description}
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </nav>
              </div>

              {/* User Quick Info */}
              <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                    {user.profilePicture ? (
                      <img
                        src={user.profilePicture}
                        alt="Profile"
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-purple-600 font-semibold text-lg">
                        {user.firstName?.charAt(0) || "U"}
                        {user.lastName?.charAt(0) || ""}
                      </span>
                    )}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {user.firstName || "User"} {user.lastName || ""}
                    </div>
                    <div className="text-sm text-gray-500">
                      @{user.username || "user"}
                    </div>
                    <div className="text-xs text-gray-400 capitalize">
                      {user.role}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Content Header */}
                <div className="p-6 border-b border-gray-200">
                  {(() => {
                    const activeTabData = tabs.find(
                      (tab) => tab.id === activeTab
                    );
                    const Icon = activeTabData?.icon || UserIcon;
                    return (
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Icon className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">
                            {activeTabData?.name}
                          </h2>
                          <p className="text-gray-600">
                            {activeTabData?.description}
                          </p>
                        </div>
                      </div>
                    );
                  })()}
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {activeTab === "profile" && (
                    <div className="space-y-8">
                      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                        <div className="xl:col-span-1">
                          <div className="sticky top-6">
                            <ProfilePicture user={user} />
                          </div>
                        </div>
                        <div className="xl:col-span-2">
                          <ProfileForm user={user} />
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === "preferences" && (
                    <PreferencesSection user={user} />
                  )}

                  {activeTab === "notifications" && (
                    <PreferencesSection user={user} type="notifications" />
                  )}

                  {activeTab === "account" && <AccountSection user={user} />}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Content (when mobile navigation is used) */}
        {isMobile && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* Content Header */}
            <div className="p-4 border-b border-gray-200">
              {(() => {
                const activeTabData = tabs.find((tab) => tab.id === activeTab);
                const Icon = activeTabData?.icon || UserIcon;
                return (
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Icon className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h2 className="text-lg font-bold text-gray-900">
                        {activeTabData?.name}
                      </h2>
                      <p className="text-sm text-gray-600">
                        {activeTabData?.description}
                      </p>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Mobile Tab Content */}
            <div className="p-4">
              {activeTab === "profile" && (
                <div className="space-y-6">
                  <div className="flex justify-center">
                    <ProfilePicture user={user} />
                  </div>
                  <ProfileForm user={user} />
                </div>
              )}

              {activeTab === "preferences" && (
                <PreferencesSection user={user} />
              )}

              {activeTab === "notifications" && (
                <PreferencesSection user={user} type="notifications" />
              )}

              {activeTab === "account" && <AccountSection user={user} />}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

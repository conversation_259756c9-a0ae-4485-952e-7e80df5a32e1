import mongoose, { Document, Schema } from "mongoose";

export interface IResource extends Document {
  _id: mongoose.Types.ObjectId;
  title: string;
  description: string;
  content: string;
  type: "article" | "video" | "audio" | "tool" | "worksheet" | "guide";
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedReadTime?: number; // in minutes
  estimatedDuration?: number; // in minutes for videos/audio
  media: {
    thumbnailUrl?: string;
    videoUrl?: string;
    audioUrl?: string;
    downloadUrl?: string;
    fileSize?: number;
  };
  author: {
    name: string;
    credentials?: string;
    bio?: string;
    profilePicture?: string;
  };
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords: string[];
    slug: string;
  };
  accessibility: {
    hasTranscript: boolean;
    hasClosedCaptions: boolean;
    isScreenReaderFriendly: boolean;
    alternativeFormats: string[];
  };
  engagement: {
    views: number;
    likes: number;
    dislikes: number;
    bookmarks: number;
    shares: number;
    averageRating: number;
    totalRatings: number;
    comments: number;
  };
  // Additional statistics property (alias for engagement for compatibility)
  statistics: {
    views: number;
    likes: number;
    bookmarks: number;
    shares: number;
    downloads: number;
    completions: number;
    averageRating: number;
    totalRatings: number;
  };
  // Interaction tracking
  interactions: {
    userId: mongoose.Types.ObjectId;
    action: "view" | "like" | "bookmark" | "share" | "download" | "complete";
    timestamp: Date;
    metadata?: any;
  }[];
  // SEO data (alias for seo for compatibility)
  seoData: {
    metaTitle?: string;
    metaDescription?: string;
    keywords: string[];
    slug: string;
  };
  // Additional metadata
  metadata: {
    version?: string;
    lastReviewed?: Date;
    contentHash?: string;
    wordCount?: number;
    readingLevel?: string;
    [key: string]: any;
  };
  // Mental health topics
  mentalHealthTopics: string[];
  ratings: {
    userId: mongoose.Types.ObjectId;
    rating: number; // 1-5
    review?: string;
    createdAt: Date;
  }[];
  bookmarkedBy: mongoose.Types.ObjectId[];
  relatedResources: mongoose.Types.ObjectId[];
  prerequisites: string[];
  learningObjectives: string[];
  isPublished: boolean;
  isFeatured: boolean;
  isPremium: boolean;
  publishedAt?: Date;
  lastUpdatedAt: Date;
  createdBy: mongoose.Types.ObjectId;
  reviewedBy?: mongoose.Types.ObjectId;
  reviewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const resourceSchema = new Schema<IResource>(
  {
    title: {
      type: String,
      required: [true, "Resource title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"],
    },
    description: {
      type: String,
      required: [true, "Resource description is required"],
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    content: {
      type: String,
      required: [true, "Resource content is required"],
    },
    type: {
      type: String,
      enum: ["article", "video", "audio", "tool", "worksheet", "guide"],
      required: true,
    },
    category: {
      type: String,
      required: true,
      enum: [
        "mental-health-basics",
        "anxiety-management",
        "depression-support",
        "stress-relief",
        "relationship-skills",
        "self-care",
        "mindfulness-meditation",
        "coping-strategies",
        "crisis-resources",
        "workplace-wellness",
        "student-support",
        "parenting-guidance",
        "grief-support",
        "addiction-recovery",
        "trauma-healing",
      ],
    },
    subcategory: String,
    tags: [
      {
        type: String,
        lowercase: true,
        trim: true,
      },
    ],
    difficulty: {
      type: String,
      enum: ["beginner", "intermediate", "advanced"],
      default: "beginner",
    },
    estimatedReadTime: {
      type: Number,
      min: 1,
      max: 120, // 2 hours max
    },
    estimatedDuration: {
      type: Number,
      min: 1,
      max: 300, // 5 hours max
    },
    media: {
      thumbnailUrl: String,
      videoUrl: String,
      audioUrl: String,
      downloadUrl: String,
      fileSize: {
        type: Number,
        min: 0,
      },
    },
    author: {
      name: {
        type: String,
        required: true,
        trim: true,
      },
      credentials: String,
      bio: {
        type: String,
        maxlength: [500, "Author bio cannot exceed 500 characters"],
      },
      profilePicture: String,
    },
    seo: {
      metaTitle: {
        type: String,
        maxlength: [60, "Meta title cannot exceed 60 characters"],
      },
      metaDescription: {
        type: String,
        maxlength: [160, "Meta description cannot exceed 160 characters"],
      },
      keywords: [
        {
          type: String,
          lowercase: true,
          trim: true,
        },
      ],
      slug: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: [
          /^[a-z0-9-]+$/,
          "Slug can only contain lowercase letters, numbers, and hyphens",
        ],
      },
    },
    accessibility: {
      hasTranscript: {
        type: Boolean,
        default: false,
      },
      hasClosedCaptions: {
        type: Boolean,
        default: false,
      },
      isScreenReaderFriendly: {
        type: Boolean,
        default: true,
      },
      alternativeFormats: [
        {
          type: String,
          enum: ["pdf", "audio", "large-print", "braille"],
        },
      ],
    },
    engagement: {
      views: { type: Number, default: 0, min: 0 },
      likes: { type: Number, default: 0, min: 0 },
      dislikes: { type: Number, default: 0, min: 0 },
      bookmarks: { type: Number, default: 0, min: 0 },
      shares: { type: Number, default: 0, min: 0 },
      averageRating: { type: Number, default: 0, min: 0, max: 5 },
      totalRatings: { type: Number, default: 0, min: 0 },
      comments: { type: Number, default: 0, min: 0 },
    },
    statistics: {
      views: { type: Number, default: 0, min: 0 },
      likes: { type: Number, default: 0, min: 0 },
      bookmarks: { type: Number, default: 0, min: 0 },
      shares: { type: Number, default: 0, min: 0 },
      downloads: { type: Number, default: 0, min: 0 },
      completions: { type: Number, default: 0, min: 0 },
      averageRating: { type: Number, default: 0, min: 0, max: 5 },
      totalRatings: { type: Number, default: 0, min: 0 },
    },
    interactions: [
      {
        userId: {
          type: Schema.Types.ObjectId,
          ref: "User",
          required: true,
        },
        action: {
          type: String,
          enum: ["view", "like", "bookmark", "share", "download", "complete"],
          required: true,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        metadata: Schema.Types.Mixed,
      },
    ],
    seoData: {
      metaTitle: {
        type: String,
        maxlength: [60, "Meta title cannot exceed 60 characters"],
      },
      metaDescription: {
        type: String,
        maxlength: [160, "Meta description cannot exceed 160 characters"],
      },
      keywords: [
        {
          type: String,
          lowercase: true,
          trim: true,
        },
      ],
      slug: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: [
          /^[a-z0-9-]+$/,
          "Slug can only contain lowercase letters, numbers, and hyphens",
        ],
      },
    },
    metadata: {
      version: String,
      lastReviewed: Date,
      contentHash: String,
      wordCount: Number,
      readingLevel: String,
    },
    mentalHealthTopics: [
      {
        type: String,
        lowercase: true,
        trim: true,
      },
    ],
    ratings: [
      {
        userId: {
          type: Schema.Types.ObjectId,
          ref: "User",
          required: true,
        },
        rating: {
          type: Number,
          required: true,
          min: 1,
          max: 5,
        },
        review: {
          type: String,
          maxlength: [500, "Review cannot exceed 500 characters"],
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    bookmarkedBy: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    relatedResources: [
      {
        type: Schema.Types.ObjectId,
        ref: "Resource",
      },
    ],
    prerequisites: [
      {
        type: String,
        maxlength: [100, "Each prerequisite cannot exceed 100 characters"],
      },
    ],
    learningObjectives: [
      {
        type: String,
        maxlength: [
          200,
          "Each learning objective cannot exceed 200 characters",
        ],
      },
    ],
    isPublished: {
      type: Boolean,
      default: false,
    },
    isFeatured: {
      type: Boolean,
      default: false,
    },
    isPremium: {
      type: Boolean,
      default: false,
    },
    publishedAt: Date,
    lastUpdatedAt: {
      type: Date,
      default: Date.now,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    reviewedAt: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
resourceSchema.index({ "seo.slug": 1 });
resourceSchema.index({ category: 1, isPublished: 1 });
resourceSchema.index({ type: 1, isPublished: 1 });
resourceSchema.index({ tags: 1 });
resourceSchema.index({ isFeatured: 1, isPublished: 1 });
resourceSchema.index({ "engagement.views": -1 });
resourceSchema.index({ "engagement.averageRating": -1 });
resourceSchema.index({ publishedAt: -1 });
resourceSchema.index({ createdAt: -1 });

// Text index for search
resourceSchema.index({
  title: "text",
  description: "text",
  content: "text",
  tags: "text",
});

// Compound indexes
resourceSchema.index({ category: 1, difficulty: 1, isPublished: 1 });
resourceSchema.index({ isPublished: 1, isFeatured: 1, publishedAt: -1 });

// Virtual to populate creator details
resourceSchema.virtual("creator", {
  ref: "User",
  localField: "createdBy",
  foreignField: "_id",
  justOne: true,
});

// Virtual to populate reviewer details
resourceSchema.virtual("reviewer", {
  ref: "User",
  localField: "reviewedBy",
  foreignField: "_id",
  justOne: true,
});

// Pre-save middleware to update lastUpdatedAt and sync statistics/seoData
resourceSchema.pre("save", function (next) {
  if (this.isModified() && !this.isNew) {
    this.lastUpdatedAt = new Date();
  }

  // Sync statistics with engagement for compatibility
  if (this.engagement) {
    this.statistics = {
      views: this.engagement.views,
      likes: this.engagement.likes,
      bookmarks: this.engagement.bookmarks,
      shares: this.engagement.shares,
      downloads: this.statistics?.downloads || 0,
      completions: this.statistics?.completions || 0,
      averageRating: this.engagement.averageRating,
      totalRatings: this.engagement.totalRatings,
    };
  }

  // Sync seoData with seo for compatibility
  if (this.seo) {
    this.seoData = {
      metaTitle: this.seo.metaTitle,
      metaDescription: this.seo.metaDescription,
      keywords: this.seo.keywords,
      slug: this.seo.slug,
    };
  }

  next();
});

// Method to add rating
resourceSchema.methods.addRating = function (
  userId: string,
  rating: number,
  review?: string
) {
  // Remove existing rating from this user
  this.ratings = this.ratings.filter(
    (r: any) => r.userId.toString() !== userId
  );

  // Add new rating
  this.ratings.push({
    userId: userId as any,
    rating,
    review,
    createdAt: new Date(),
  });

  // Recalculate average rating
  const totalRating = this.ratings.reduce(
    (sum: number, r: any) => sum + r.rating,
    0
  );
  this.engagement.averageRating = totalRating / this.ratings.length;
  this.engagement.totalRatings = this.ratings.length;
};

// Method to increment view count
resourceSchema.methods.incrementViews = function () {
  this.engagement.views += 1;
};

// Method to toggle bookmark
resourceSchema.methods.toggleBookmark = function (userId: string) {
  const userObjectId = userId as any;
  const bookmarkIndex = this.bookmarkedBy.findIndex(
    (id: any) => id.toString() === userId
  );

  if (bookmarkIndex > -1) {
    this.bookmarkedBy.splice(bookmarkIndex, 1);
    this.engagement.bookmarks = Math.max(0, this.engagement.bookmarks - 1);
    return false; // unbookmarked
  } else {
    this.bookmarkedBy.push(userObjectId);
    this.engagement.bookmarks += 1;
    return true; // bookmarked
  }
};

export const Resource = mongoose.model<IResource>("Resource", resourceSchema);

import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import {
  Check<PERSON><PERSON><PERSON>,
  UserCheck,
  Calendar,
  MessageCircle,
  Shield,
} from "lucide-react";

const steps = [
  {
    icon: UserCheck,
    title: "1. Create Your Profile",
    description:
      "Sign up and tell us about yourself, your needs, and preferences. Our matching system will help find the right counselor for you.",
  },
  {
    icon: Calendar,
    title: "2. Find Your Counselor",
    description:
      "Browse our verified, licensed counselors or let us match you based on your specific needs, schedule, and therapy preferences.",
  },
  {
    icon: MessageCircle,
    title: "3. Start Your Journey",
    description:
      "Begin with secure video sessions, chat support, or join community groups. Your mental health journey is personalized to you.",
  },
];

const features = [
  "Licensed & verified counselors",
  "HIPAA compliant platform",
  "Flexible scheduling",
  "Multiple communication options",
  "Crisis support available 24/7",
  "Affordable pricing plans",
];

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gray-50">
            <Header />
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            How Theramea Works
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Getting started with your mental health journey is simple and secure
          </p>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 text-gray-800">
              Simple Steps to Better Mental Health
            </h2>

            <div className="space-y-12">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className="flex flex-col md:flex-row items-center gap-8"
                >
                  <div className="flex-shrink-0">
                    <div className="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center">
                      <step.icon className="h-12 w-12 text-purple-600" />
                    </div>
                  </div>
                  <div className="text-center md:text-left">
                    <h3 className="text-2xl font-bold mb-4 text-gray-800">
                      {step.title}
                    </h3>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-16 text-gray-800">
              Why Choose Theramea?
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 text-left">
                  <CheckCircle className="h-6 w-6 text-green-500 flex-shrink-0" />
                  <span className="text-lg text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            <div className="mt-12">
              <a
                href="/counselors"
                className="inline-block bg-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-purple-700 transition-colors"
              >
                Get Started Today
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Security Section */}
      <section className="py-20 bg-gray-100">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <Shield className="h-16 w-16 text-purple-600 mx-auto mb-6" />
            <h2 className="text-3xl font-bold mb-6 text-gray-800">
              Your Privacy & Security
            </h2>
            <p className="text-lg text-gray-600 leading-relaxed">
              We take your privacy seriously. Theramea is fully HIPAA compliant,
              uses end-to-end encryption for all communications, and follows the
              highest standards of data protection to ensure your information
              remains secure.
            </p>
          </div>
        </div>
      </section>
      <Footer />
    </div>

  );
}

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { generateRandomGuestName } from "@/lib/guestUtils";
import Header from "@/components/layout/Header";

export default function GuestStartPage() {
  const router = useRouter();
  const { generateGuestToken, isAuthenticated, isGuest } = useAuthStore();
  const [displayName, setDisplayName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    // Generate a random guest name on component mount
    setDisplayName(generateRandomGuestName());
  }, []);

  useEffect(() => {
    // If already authenticated, redirect appropriately
    if (isAuthenticated) {
      if (isGuest) {
        router.push("/explore");
      } else {
        router.push("/dashboard");
      }
    }
  }, [isAuthenticated, isGuest, router]);

  const handleGenerateNewName = () => {
    setDisplayName(generateRandomGuestName());
  };

  const handleContinue = async () => {
    if (!displayName.trim()) {
      setError("Please enter a display name");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      await generateGuestToken(displayName.trim());
      router.push("/guest/interests");
    } catch (err: any) {
      setError(err.message || "Failed to start guest session");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-md mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">👋</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome, Guest!
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Choose a display name to get started. You can always create a full
            account later.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          <div className="mb-6">
            <label
              htmlFor="displayName"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Your display name
            </label>
            <div className="flex space-x-2">
              <input
                id="displayName"
                type="text"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                placeholder="Enter a name"
                maxLength={30}
              />
              <button
                type="button"
                onClick={handleGenerateNewName}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                🎲
              </button>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              This is how you'll appear to others. You can change it anytime.
            </p>
          </div>

          <button
            onClick={handleContinue}
            disabled={!displayName.trim() || isSubmitting}
            className="w-full py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Starting...
              </div>
            ) : (
              "Continue as Guest"
            )}
          </button>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500 mb-3">
              Want full access to all features?
            </p>
            <div className="space-y-2">
              <button
                onClick={() => router.push("/auth/signup")}
                className="w-full py-2 px-4 border border-purple-600 text-purple-600 rounded-md text-sm font-medium hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Create Free Account
              </button>
              <button
                onClick={() => router.push("/auth/login")}
                className="w-full py-2 px-4 text-gray-600 text-sm hover:text-gray-900"
              >
                Already have an account? Sign in
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

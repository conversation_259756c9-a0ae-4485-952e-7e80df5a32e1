"use client";

interface UserGrowthChartProps {
  data: any[];
  period: "week" | "month" | "quarter" | "year";
}

export default function UserGrowthChart({
  data,
  period,
}: UserGrowthChartProps) {
  const formatNumber = (value: number) => {
    return value.toLocaleString();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">User Growth</h3>
        <div className="text-sm text-gray-500 capitalize">{period}ly view</div>
      </div>

      <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
        <div className="text-center">
          <p className="text-gray-500 mb-4">Chart visualization coming soon</p>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="bg-white p-4 rounded-lg">
              <p className="text-gray-500">Total Users</p>
              <p className="text-xl font-semibold text-purple-600">
                {formatNumber(
                  data.reduce((sum, item) => sum + (item.users || 0), 0)
                )}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <p className="text-gray-500">Growth Rate</p>
              <p className="text-xl font-semibold text-green-600">
                +
                {data.length > 1
                  ? Math.round(
                      (((data[data.length - 1]?.users || 0) -
                        (data[0]?.users || 0)) /
                        (data[0]?.users || 1)) *
                        100
                    )
                  : 0}
                %
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { 
  CheckCircleIcon, 
  ClockIcon, 
  ExclamationCircleIcon,
  XCircleIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';

interface Payout {
  id: string;
  amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  date: string;
  method: string;
}

interface PayoutHistoryProps {
  payouts: Payout[];
  currency: string;
}

export default function PayoutHistory({ payouts, currency }: PayoutHistoryProps) {
  const formatCurrency = (amount: number) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      case 'pending':
        return <ExclamationCircleIcon className="h-5 w-5 text-yellow-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Successfully transferred to your account';
      case 'processing':
        return 'Transfer is being processed by the bank';
      case 'pending':
        return 'Waiting to be processed';
      case 'failed':
        return 'Transfer failed - please contact support';
      default:
        return 'Status unknown';
    }
  };

  if (!payouts || payouts.length === 0) {
    return (
      <div className="text-center py-8">
        <BanknotesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No payouts yet</h3>
        <p className="text-gray-600">
          Your payout history will appear here once you start earning from sessions.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {payouts.map((payout) => (
        <div
          key={payout.id}
          className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {getStatusIcon(payout.status)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-1">
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(payout.amount)}
                </p>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payout.status)}`}>
                  {payout.status}
                </span>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  {new Date(payout.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
                <span>•</span>
                <span>{payout.method}</span>
              </div>
              
              <p className="text-xs text-gray-500 mt-1">
                {getStatusDescription(payout.status)}
              </p>
            </div>
          </div>

          <div className="flex-shrink-0">
            {payout.status === 'failed' && (
              <button className="text-sm text-purple-600 hover:text-purple-700 font-medium">
                Retry
              </button>
            )}
            {payout.status === 'completed' && (
              <button className="text-sm text-gray-600 hover:text-gray-700 font-medium">
                Receipt
              </button>
            )}
          </div>
        </div>
      ))}

      {/* Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-green-600">
              {payouts.filter(p => p.status === 'completed').length}
            </div>
            <div className="text-xs text-gray-600">Completed</div>
          </div>
          
          <div>
            <div className="text-lg font-semibold text-blue-600">
              {payouts.filter(p => p.status === 'processing').length}
            </div>
            <div className="text-xs text-gray-600">Processing</div>
          </div>
          
          <div>
            <div className="text-lg font-semibold text-yellow-600">
              {payouts.filter(p => p.status === 'pending').length}
            </div>
            <div className="text-xs text-gray-600">Pending</div>
          </div>
          
          <div>
            <div className="text-lg font-semibold text-red-600">
              {payouts.filter(p => p.status === 'failed').length}
            </div>
            <div className="text-xs text-gray-600">Failed</div>
          </div>
        </div>
      </div>

      {/* Total Payouts */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-purple-900">Total Payouts</h4>
            <p className="text-xs text-purple-700">All successful transfers</p>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-purple-900">
              {formatCurrency(
                payouts
                  .filter(p => p.status === 'completed')
                  .reduce((sum, p) => sum + p.amount, 0)
              )}
            </div>
            <div className="text-xs text-purple-700">
              {payouts.filter(p => p.status === 'completed').length} transfers
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

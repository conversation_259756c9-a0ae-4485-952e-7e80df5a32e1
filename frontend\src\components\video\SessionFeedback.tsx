'use client';

import { useState } from 'react';
import { videoAPI } from '@/lib/video';
import { SessionFeedback as FeedbackType } from '@/types/video';
import { 
  StarIcon, 
  HandThumbUpIcon, 
  HandThumbDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface SessionFeedbackProps {
  sessionId: string;
  userRole: 'client' | 'counselor';
  token: string;
  onSubmit?: (feedback: FeedbackType) => void;
  onSkip?: () => void;
}

export default function SessionFeedback({ 
  sessionId, 
  userRole, 
  token, 
  onSubmit, 
  onSkip 
}: SessionFeedbackProps) {
  const [feedback, setFeedback] = useState<Partial<FeedbackType>>({
    rating: 0,
    comment: '',
    wouldRecommend: true,
    sessionQuality: 5,
    clientEngagement: 5,
    technicalIssues: false,
    notes: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleRatingChange = (rating: number) => {
    setFeedback(prev => ({ ...prev, rating }));
  };

  const handleQualityChange = (quality: number) => {
    setFeedback(prev => ({ ...prev, sessionQuality: quality }));
  };

  const handleEngagementChange = (engagement: number) => {
    setFeedback(prev => ({ ...prev, clientEngagement: engagement }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (feedback.rating === 0) {
      setError('Please provide a rating for the session');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const completeFeedback: FeedbackType = {
        rating: feedback.rating!,
        comment: feedback.comment || '',
        wouldRecommend: feedback.wouldRecommend!,
        sessionQuality: feedback.sessionQuality || 5,
        clientEngagement: feedback.clientEngagement || 5,
        technicalIssues: feedback.technicalIssues || false,
        notes: feedback.notes || '',
      };

      await videoAPI.submitSessionFeedback(sessionId, completeFeedback, token);
      setSubmitted(true);
      
      if (onSubmit) {
        onSubmit(completeFeedback);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRatingChange(star)}
            className="focus:outline-none"
          >
            {star <= rating ? (
              <StarIconSolid className="h-6 w-6 text-yellow-400 hover:text-yellow-500" />
            ) : (
              <StarIcon className="h-6 w-6 text-gray-300 hover:text-yellow-400" />
            )}
          </button>
        ))}
      </div>
    );
  };

  if (submitted) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
        <div className="text-center">
          <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Thank You!
          </h2>
          <p className="text-gray-600 mb-6">
            Your feedback has been submitted successfully. It helps us improve our service.
          </p>
          <button
            onClick={() => window.close()}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Session Feedback
        </h2>
        <p className="text-gray-600">
          Help us improve by sharing your experience with this session
        </p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Overall Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Overall Rating *
          </label>
          <div className="flex items-center space-x-2">
            {renderStars(feedback.rating || 0, handleRatingChange)}
            <span className="text-sm text-gray-600 ml-2">
              {feedback.rating ? `${feedback.rating}/5` : 'Select rating'}
            </span>
          </div>
        </div>

        {/* Session Quality (Counselor only) */}
        {userRole === 'counselor' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Session Quality
            </label>
            <div className="flex items-center space-x-2">
              {renderStars(feedback.sessionQuality || 0, handleQualityChange)}
              <span className="text-sm text-gray-600 ml-2">
                {feedback.sessionQuality}/5
              </span>
            </div>
          </div>
        )}

        {/* Client Engagement (Counselor only) */}
        {userRole === 'counselor' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Client Engagement
            </label>
            <div className="flex items-center space-x-2">
              {renderStars(feedback.clientEngagement || 0, handleEngagementChange)}
              <span className="text-sm text-gray-600 ml-2">
                {feedback.clientEngagement}/5
              </span>
            </div>
          </div>
        )}

        {/* Would Recommend (Client only) */}
        {userRole === 'client' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Would you recommend this counselor?
            </label>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => setFeedback(prev => ({ ...prev, wouldRecommend: true }))}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md border transition-colors ${
                  feedback.wouldRecommend
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-300 hover:border-green-300'
                }`}
              >
                <HandThumbUpIcon className="h-5 w-5" />
                <span>Yes</span>
              </button>
              <button
                type="button"
                onClick={() => setFeedback(prev => ({ ...prev, wouldRecommend: false }))}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md border transition-colors ${
                  feedback.wouldRecommend === false
                    ? 'border-red-500 bg-red-50 text-red-700'
                    : 'border-gray-300 hover:border-red-300'
                }`}
              >
                <HandThumbDownIcon className="h-5 w-5" />
                <span>No</span>
              </button>
            </div>
          </div>
        )}

        {/* Technical Issues */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Technical Issues
          </label>
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="technicalIssues"
              checked={feedback.technicalIssues}
              onChange={(e) => setFeedback(prev => ({ ...prev, technicalIssues: e.target.checked }))}
              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
            />
            <label htmlFor="technicalIssues" className="text-sm text-gray-700">
              I experienced technical issues during this session
            </label>
          </div>
        </div>

        {/* Comments */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Comments (Optional)
          </label>
          <textarea
            value={feedback.comment}
            onChange={(e) => setFeedback(prev => ({ ...prev, comment: e.target.value }))}
            rows={4}
            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            placeholder="Share your thoughts about the session..."
          />
        </div>

        {/* Notes (Counselor only) */}
        {userRole === 'counselor' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Session Notes (Private)
            </label>
            <textarea
              value={feedback.notes}
              onChange={(e) => setFeedback(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
              placeholder="Private notes about the session..."
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          {onSkip && (
            <button
              type="button"
              onClick={onSkip}
              className="text-gray-600 hover:text-gray-700 font-medium"
            >
              Skip Feedback
            </button>
          )}
          
          <div className="flex space-x-3">
            <button
              type="submit"
              disabled={isSubmitting || feedback.rating === 0}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </button>
          </div>
        </div>
      </form>

      {/* Privacy Notice */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <p className="text-xs text-blue-800">
          <strong>Privacy Notice:</strong> Your feedback helps us improve our service. 
          {userRole === 'client' 
            ? ' Your counselor will see your rating and comments, but not your recommendation choice.'
            : ' Your notes are private and will not be shared with the client.'
          }
        </p>
      </div>
    </div>
  );
}

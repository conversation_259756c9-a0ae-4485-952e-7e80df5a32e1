'use client';

import { useState } from 'react';
import { 
  CreditCardIcon, 
  BanknotesIcon, 
  DevicePhoneMobileIcon,
  BuildingLibraryIcon,
  QrCodeIcon
} from '@heroicons/react/24/outline';

interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  fee: number;
  processingTime: string;
  available: boolean;
}

interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onMethodChange: (method: string) => void;
  amount: number;
  currency: string;
}

export default function PaymentMethodSelector({ 
  selectedMethod, 
  onMethodChange, 
  amount, 
  currency 
}: PaymentMethodSelectorProps) {
  const formatCurrency = (amount: number) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const calculateFee = (method: string, amount: number): number => {
    const fees = {
      card: Math.min(Math.round(amount * 0.015) + 100, 200000), // 1.5% + ₦1.00, capped at ₦2000
      bank: Math.min(Math.round(amount * 0.014) + 100, 200000), // 1.4% + ₦1.00, capped at ₦2000
      ussd: 5000, // Flat ₦50
      transfer: 5000, // Flat ₦50
      mobile_money: Math.min(Math.round(amount * 0.015), 200000), // 1.5%, capped at ₦2000
      qr: 0, // Free for QR codes
    };
    
    return fees[method as keyof typeof fees] || 0;
  };

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Debit/Credit Card',
      description: 'Pay with your Visa, Mastercard, or Verve card',
      icon: CreditCardIcon,
      fee: calculateFee('card', amount),
      processingTime: 'Instant',
      available: true,
    },
    {
      id: 'bank',
      name: 'Bank Transfer',
      description: 'Direct transfer from your bank account',
      icon: BuildingLibraryIcon,
      fee: calculateFee('bank', amount),
      processingTime: 'Instant',
      available: true,
    },
    {
      id: 'ussd',
      name: 'USSD',
      description: 'Pay using your mobile phone USSD code',
      icon: DevicePhoneMobileIcon,
      fee: calculateFee('ussd', amount),
      processingTime: '1-2 minutes',
      available: true,
    },
    {
      id: 'transfer',
      name: 'Bank Transfer',
      description: 'Transfer to our account details',
      icon: BanknotesIcon,
      fee: calculateFee('transfer', amount),
      processingTime: '5-10 minutes',
      available: true,
    },
    {
      id: 'mobile_money',
      name: 'Mobile Money',
      description: 'Pay with your mobile money wallet',
      icon: DevicePhoneMobileIcon,
      fee: calculateFee('mobile_money', amount),
      processingTime: 'Instant',
      available: currency === 'NGN',
    },
    {
      id: 'qr',
      name: 'QR Code',
      description: 'Scan QR code with your banking app',
      icon: QrCodeIcon,
      fee: calculateFee('qr', amount),
      processingTime: 'Instant',
      available: true,
    },
  ];

  const availableMethods = paymentMethods.filter(method => method.available);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Choose Payment Method
      </h3>
      
      <div className="grid grid-cols-1 gap-3">
        {availableMethods.map((method) => {
          const Icon = method.icon;
          const isSelected = selectedMethod === method.id;
          const totalAmount = amount + method.fee;
          
          return (
            <button
              key={method.id}
              onClick={() => onMethodChange(method.id)}
              className={`relative p-4 border rounded-lg text-left transition-all ${
                isSelected
                  ? 'border-purple-600 bg-purple-50 ring-2 ring-purple-600'
                  : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-start space-x-3">
                <div className={`flex-shrink-0 p-2 rounded-lg ${
                  isSelected ? 'bg-purple-100' : 'bg-gray-100'
                }`}>
                  <Icon className={`h-6 w-6 ${
                    isSelected ? 'text-purple-600' : 'text-gray-600'
                  }`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={`text-sm font-medium ${
                      isSelected ? 'text-purple-900' : 'text-gray-900'
                    }`}>
                      {method.name}
                    </h4>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        isSelected ? 'text-purple-900' : 'text-gray-900'
                      }`}>
                        {formatCurrency(totalAmount / 100)}
                      </div>
                      {method.fee > 0 && (
                        <div className="text-xs text-gray-500">
                          +{formatCurrency(method.fee / 100)} fee
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <p className={`text-sm ${
                    isSelected ? 'text-purple-700' : 'text-gray-600'
                  }`}>
                    {method.description}
                  </p>
                  
                  <div className="flex items-center justify-between mt-2">
                    <span className={`text-xs ${
                      isSelected ? 'text-purple-600' : 'text-gray-500'
                    }`}>
                      Processing: {method.processingTime}
                    </span>
                    
                    {method.fee === 0 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        No fees
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Payment Security Notice */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start space-x-2">
          <div className="text-green-600 text-lg">🔒</div>
          <div>
            <h4 className="text-sm font-medium text-green-900 mb-1">
              Secure Payment
            </h4>
            <p className="text-sm text-green-700">
              All payments are processed securely through Paystack with 256-bit SSL encryption. 
              Your card details are never stored on our servers.
            </p>
          </div>
        </div>
      </div>

      {/* Popular Payment Methods */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          💡 Popular Payment Methods
        </h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• <strong>Card:</strong> Most convenient, instant processing</p>
          <p>• <strong>Bank Transfer:</strong> Lower fees, direct from account</p>
          <p>• <strong>USSD:</strong> No internet required, works on any phone</p>
        </div>
      </div>

      {/* Supported Banks/Cards */}
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          Supported Payment Options
        </h4>
        <div className="flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Visa
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Mastercard
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Verve
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            GTBank
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Access Bank
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            First Bank
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Zenith Bank
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            +50 more
          </span>
        </div>
      </div>
    </div>
  );
}

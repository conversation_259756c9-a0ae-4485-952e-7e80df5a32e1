const mongoose = require("mongoose");

const MONGODB_URI =
  process.env.MONGODB_URI ||
  "****************************************************************";

async function patchSessionDate() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB successfully");

    const sessionId = "689e8ac358caf361f78ce110";
    // Set a valid scheduled date (e.g., tomorrow at 10:00 AM)
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + 1);
    scheduledDate.setHours(10, 0, 0, 0);

    const result = await mongoose.connection.db
      .collection("sessions")
      .updateOne(
        { _id: new mongoose.Types.ObjectId(sessionId) },
        { $set: { scheduledAt: scheduledDate, scheduledDate: scheduledDate } }
      );

    if (result.modifiedCount > 0) {
      console.log("✅ Session date patched successfully!");
      console.log(`New scheduledAt: ${scheduledDate.toISOString()}`);
    } else {
      console.log("❌ No changes made - session not found or already correct");
    }
  } catch (error) {
    console.error("❌ Error patching session date:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

patchSessionDate();

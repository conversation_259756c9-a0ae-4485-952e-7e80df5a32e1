"use client";

import { useEffect, useRef, useState } from "react";
import { io } from "socket.io-client";
import { useAuthStore } from "@/store/authStore";
import { ChatMessage, TypingUser } from "@/types/chat";

interface UseSocketProps {
  roomId?: string;
  onNewMessage?: (message: ChatMessage) => void;
  onMessageEdited?: (data: {
    messageId: string;
    newContent: string;
    editedAt: Date;
    isEdited: boolean;
  }) => void;
  onMessageDeleted?: (data: { messageId: string; deletedAt: Date }) => void;
  onUserJoined?: (user: any) => void;
  onUserLeft?: (user: any) => void;
  onTypingStart?: (user: TypingUser) => void;
  onTypingStop?: (user: TypingUser) => void;
  onError?: (error: string) => void;
}

export const useSocket = ({
  roomId,
  onNewMessage,
  onMessageEdited,
  onMessageDeleted,
  onUserJoined,
  onUserLeft,
  onTypingStart,
  onTypingStop,
  onError,
}: UseSocketProps = {}) => {
  const { user, tokens, guestToken, isAuthenticated, isGuest } = useAuthStore();
  const socketRef = useRef<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  const connect = () => {
    if (socketRef.current?.connected) return;

    setIsConnecting(true);

    const token = tokens?.accessToken || guestToken;
    if (!token) {
      setIsConnecting(false);
      return;
    }

    const socket = io(
      process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:5000",
      {
        auth: {
          token,
        },
        transports: ["websocket", "polling"],
      }
    ) as any; // Type assertion to fix Socket.IO typing issues

    socketRef.current = socket;

    socket.on("connect", () => {
      console.log("Socket connected");
      setIsConnected(true);
      setIsConnecting(false);
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected");
      setIsConnected(false);
    });

    socket.on("connect_error", (error: any) => {
      console.error("Socket connection error:", error);
      setIsConnecting(false);
      onError?.(error.message || "Connection failed");
    });

    // Chat event listeners
    socket.on("new-message", (message: ChatMessage) => {
      onNewMessage?.(message);
    });

    socket.on(
      "message-edited",
      (data: {
        messageId: string;
        newContent: string;
        editedAt: Date;
        isEdited: boolean;
      }) => {
        onMessageEdited?.(data);
      }
    );

    socket.on(
      "message-deleted",
      (data: { messageId: string; deletedAt: Date }) => {
        onMessageDeleted?.(data);
      }
    );

    socket.on("user-joined", (user: any) => {
      onUserJoined?.(user);
    });

    socket.on("user-left", (user: any) => {
      onUserLeft?.(user);
    });

    socket.on("user-typing", (user: TypingUser) => {
      onTypingStart?.(user);
    });

    socket.on("user-stopped-typing", (user: TypingUser) => {
      onTypingStop?.(user);
    });

    socket.on("error", (error: { message: string }) => {
      onError?.(error.message);
    });
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  };

  const joinRoom = (roomId: string, displayName: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("join-room", {
      roomId,
      displayName,
    });
  };

  const leaveRoom = (roomId: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("leave-room", {
      roomId,
    });
  };

  const sendMessage = (
    roomId: string,
    content: any,
    replyTo?: string,
    mentions?: string[]
  ) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("send-message", {
      roomId,
      content,
      replyTo,
      mentions,
    });
  };

  const startTyping = (roomId: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("typing-start", { roomId });
  };

  const stopTyping = (roomId: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("typing-stop", { roomId });
  };

  const editMessage = (messageId: string, newContent: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("edit-message", {
      messageId,
      newContent,
    });
  };

  const deleteMessage = (messageId: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("delete-message", {
      messageId,
    });
  };

  const addReaction = (messageId: string, emoji: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("add-reaction", {
      messageId,
      emoji,
    });
  };

  const removeReaction = (messageId: string, emoji: string) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit("remove-reaction", {
      messageId,
      emoji,
    });
  };

  useEffect(() => {
    if (isAuthenticated || isGuest) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, isGuest, tokens, guestToken]);

  useEffect(() => {
    if (roomId && isConnected) {
      const displayName = isGuest
        ? `Guest_${Math.random().toString(36).substr(2, 6)}`
        : `${user?.firstName} ${user?.lastName}`;

      joinRoom(roomId, displayName);

      return () => {
        leaveRoom(roomId);
      };
    }
  }, [roomId, isConnected, user, isGuest]);

  return {
    socket: socketRef.current,
    isConnected,
    isConnecting,
    connect,
    disconnect,
    joinRoom,
    leaveRoom,
    sendMessage,
    editMessage,
    deleteMessage,
    startTyping,
    stopTyping,
    addReaction,
    removeReaction,
  };
};

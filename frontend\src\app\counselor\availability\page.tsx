'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { counselorPortalAPI } from '@/lib/counselorPortal';
import CounselorLayout from '@/components/counselor/CounselorLayout';
import AvailabilityCalendar from '@/components/counselor/AvailabilityCalendar';
import WeeklySchedule from '@/components/counselor/WeeklySchedule';
import { CalendarIcon, ClockIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

interface TimeSlot {
  startTime: string;
  endTime: string;
}

interface DaySchedule {
  isAvailable: boolean;
  timeSlots: TimeSlot[];
}

interface WeeklySchedule {
  [key: string]: DaySchedule;
}

export default function AvailabilityPage() {
  const router = useRouter();
  const { isAuthenticated, user, tokens } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const [schedule, setSchedule] = useState<WeeklySchedule>({
    monday: { isAvailable: false, timeSlots: [] },
    tuesday: { isAvailable: false, timeSlots: [] },
    wednesday: { isAvailable: false, timeSlots: [] },
    thursday: { isAvailable: false, timeSlots: [] },
    friday: { isAvailable: false, timeSlots: [] },
    saturday: { isAvailable: false, timeSlots: [] },
    sunday: { isAvailable: false, timeSlots: [] },
  });
  
  const [unavailableDates, setUnavailableDates] = useState<string[]>([]);
  const [timezone, setTimezone] = useState(Intl.DateTimeFormat().resolvedOptions().timeZone);

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'counselor') {
      router.push('/auth/login');
      return;
    }

    fetchAvailability();
  }, [isAuthenticated, user, router]);

  const fetchAvailability = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await counselorPortalAPI.getAvailability(tokens!.accessToken);
      
      if (response.data.availability) {
        setSchedule(response.data.availability.schedule || schedule);
        setUnavailableDates(response.data.availability.unavailableDates || []);
        setTimezone(response.data.availability.timezone || timezone);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load availability');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAvailability = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      await counselorPortalAPI.updateAvailability(
        {
          schedule,
          unavailableDates,
          timezone,
        },
        tokens!.accessToken
      );

      setSuccess('Availability updated successfully!');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update availability');
    } finally {
      setSaving(false);
    }
  };

  const handleScheduleChange = (day: string, daySchedule: DaySchedule) => {
    setSchedule(prev => ({
      ...prev,
      [day]: daySchedule,
    }));
  };

  const handleUnavailableDatesChange = (dates: string[]) => {
    setUnavailableDates(dates);
  };

  if (loading) {
    return (
      <CounselorLayout>
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </CounselorLayout>
    );
  }

  return (
    <CounselorLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <CalendarIcon className="h-8 w-8 text-purple-600 mr-3" />
                Availability Management
              </h1>
              <p className="mt-2 text-gray-600">
                Set your weekly schedule and manage unavailable dates
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center text-sm text-gray-600">
                <GlobeAltIcon className="h-4 w-4 mr-1" />
                {timezone}
              </div>
              <button
                onClick={handleSaveAvailability}
                disabled={saving}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="text-sm text-green-700">{success}</div>
          </div>
        )}

        {/* Timezone Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <GlobeAltIcon className="h-5 w-5 text-gray-600 mr-2" />
            Timezone Settings
          </h2>
          <div className="max-w-md">
            <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-2">
              Select your timezone
            </label>
            <select
              id="timezone"
              value={timezone}
              onChange={(e) => setTimezone(e.target.value)}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            >
              <option value="Africa/Lagos">Africa/Lagos (WAT)</option>
              <option value="America/New_York">America/New_York (EST/EDT)</option>
              <option value="America/Los_Angeles">America/Los_Angeles (PST/PDT)</option>
              <option value="Europe/London">Europe/London (GMT/BST)</option>
              <option value="Asia/Tokyo">Asia/Tokyo (JST)</option>
              <option value="Australia/Sydney">Australia/Sydney (AEST/AEDT)</option>
            </select>
          </div>
        </div>

        {/* Weekly Schedule */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ClockIcon className="h-5 w-5 text-gray-600 mr-2" />
            Weekly Schedule
          </h2>
          <WeeklySchedule
            schedule={schedule}
            onChange={handleScheduleChange}
          />
        </div>

        {/* Unavailable Dates Calendar */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CalendarIcon className="h-5 w-5 text-gray-600 mr-2" />
            Unavailable Dates
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Mark specific dates when you won't be available for sessions
          </p>
          <AvailabilityCalendar
            unavailableDates={unavailableDates}
            onChange={handleUnavailableDatesChange}
          />
        </div>

        {/* Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">💡 Tips for Managing Availability</h3>
          <ul className="text-sm text-blue-800 space-y-2">
            <li>• Set consistent weekly hours to help clients know when to book</li>
            <li>• Update your availability at least 24 hours in advance</li>
            <li>• Block out time for breaks between sessions</li>
            <li>• Consider different time zones if you serve international clients</li>
            <li>• Mark holidays and vacation days as unavailable in advance</li>
          </ul>
        </div>
      </div>
    </CounselorLayout>
  );
}

import request from "supertest";
import { app } from "../app";
import { MoodEntry } from "../models/MoodEntry";
import { Resource } from "../models/Resource";
import { User } from "../models/User";
import jwt from "jsonwebtoken";

describe("Mood Feedback and Suggestions", () => {
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    // Create a test user
    const testUser = new User({
      email: "<EMAIL>",
      password: "password123",
      profile: { firstName: "Test", lastName: "User" },
    });
    await testUser.save();
    userId = testUser._id.toString();

    // Generate auth token
    authToken = jwt.sign(
      { userId: testUser._id },
      process.env.JWT_SECRET || "test-secret"
    );

    // Create some test mood entries for pattern analysis
    const testEntries = [
      {
        userId,
        mood: 2,
        energy: 2,
        anxiety: 4,
        triggers: ["work", "stress"],
        tags: ["tired"],
        note: "Stressful day at work",
      },
      {
        userId,
        mood: 3,
        energy: 3,
        anxiety: 3,
        triggers: ["work"],
        tags: ["neutral"],
        note: "Regular day",
      },
      {
        userId,
        mood: 1,
        energy: 1,
        anxiety: 5,
        triggers: ["work", "deadline"],
        tags: ["overwhelmed"],
        note: "Very stressful deadline",
      },
    ];

    await MoodEntry.insertMany(testEntries);

    // Create a test resource
    const testResource = new Resource({
      title: "Quick Breathing Exercise",
      description: "A 5-minute breathing exercise to reduce anxiety",
      content: "Test content",
      category: "anxiety-management",
      tags: ["anxiety", "breathing", "stress"],
      difficulty: "beginner",
      type: "tool",
      isPublished: true,
      seo: { slug: "breathing-exercise" },
      statistics: { averageRating: 4.5, views: 100 },
    });
    await testResource.save();
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({ email: "<EMAIL>" });
    await MoodEntry.deleteMany({ userId });
    await Resource.deleteMany({ title: "Quick Breathing Exercise" });
  });

  describe("POST /api/mood/feedback", () => {
    it("should generate personalized feedback for low mood with high anxiety", async () => {
      const moodEntry = {
        mood: 2,
        energy: 2,
        anxiety: 4,
        triggers: ["work"],
        tags: ["stressed"],
        note: "Feeling overwhelmed",
      };

      const response = await request(app)
        .post("/api/mood/feedback")
        .set("Authorization", `Bearer ${authToken}`)
        .send(moodEntry)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.feedback).toBeDefined();
      expect(typeof response.body.data.feedback).toBe("string");
      expect(response.body.data.feedback.length).toBeGreaterThan(0);
    });

    it("should generate different feedback for positive mood", async () => {
      const moodEntry = {
        mood: 4,
        energy: 4,
        anxiety: 2,
        triggers: [],
        tags: ["happy"],
        note: "Great day!",
      };

      const response = await request(app)
        .post("/api/mood/feedback")
        .set("Authorization", `Bearer ${authToken}`)
        .send(moodEntry)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.feedback).toBeDefined();
      expect(response.body.data.feedback).toContain("positive");
    });
  });

  describe("POST /api/mood/suggestions", () => {
    it("should suggest anxiety resources for high anxiety mood", async () => {
      const moodEntry = {
        mood: 3,
        energy: 3,
        anxiety: 4,
        triggers: ["stress"],
        tags: ["anxious"],
        note: "Feeling anxious",
      };

      const response = await request(app)
        .post("/api/mood/suggestions")
        .set("Authorization", `Bearer ${authToken}`)
        .send(moodEntry)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toBeDefined();
      expect(Array.isArray(response.body.data.suggestions)).toBe(true);
    });

    it("should handle requests without authentication", async () => {
      const moodEntry = {
        mood: 3,
        energy: 3,
        anxiety: 3,
      };

      const response = await request(app)
        .post("/api/mood/suggestions")
        .send(moodEntry)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});

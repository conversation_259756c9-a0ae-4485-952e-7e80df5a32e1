const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// User Schema (simplified for test data creation)
const userSchema = new mongoose.Schema({
  firstName: String,
  lastName: String,
  email: { type: String, unique: true },
  password: String,
  role: { type: String, enum: ["user", "counselor", "admin"], default: "user" },
  isActive: { type: Boolean, default: true },
  isEmailVerified: { type: Boolean, default: false },
  profilePicture: String,
  phone: String,
  dateOfBirth: Date,
  gender: String,
  location: String,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Counselor <PERSON> (simplified)
const counselorSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  bio: String,
  specializations: [String],
  experience: {
    years: Number,
    description: String,
  },
  qualifications: [
    {
      degree: String,
      institution: String,
      year: Number,
    },
  ],
  licenses: [
    {
      type: String,
      number: String,
      issuingAuthority: String,
      expiryDate: Date,
    },
  ],
  profile: {
    languages: [String],
    approachDescription: String,
    sessionTypes: [String],
  },
  pricing: {
    currency: { type: String, enum: ["NGN", "USD"], default: "NGN" },
    ratePerMinute: Number,
    minimumSessionDuration: { type: Number, default: 30 },
  },
  verification: {
    status: {
      type: String,
      enum: ["pending", "approved", "rejected"],
      default: "pending",
    },
    submittedAt: Date,
    reviewedAt: Date,
    reviewedBy: String,
    rejectionReason: String,
  },
  settings: {
    acceptingNewClients: { type: Boolean, default: true },
    maxSessionsPerDay: { type: Number, default: 8 },
    bufferTimeBetweenSessions: { type: Number, default: 15 },
  },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Chatroom Schema (simplified)
const chatroomSchema = new mongoose.Schema({
  name: String,
  description: String,
  category: String,
  isPublic: { type: Boolean, default: true },
  maxParticipants: { type: Number, default: 50 },
  rules: [String],
  moderators: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
  isActive: { type: Boolean, default: true },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  createdAt: { type: Date, default: Date.now },
});

// Resource Schema (simplified)
const resourceSchema = new mongoose.Schema({
  title: String,
  description: String,
  content: String,
  type: {
    type: String,
    enum: ["article", "video", "audio", "pdf", "exercise"],
  },
  category: String,
  tags: [String],
  author: String,
  difficulty: { type: String, enum: ["beginner", "intermediate", "advanced"] },
  estimatedReadTime: Number,
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  createdAt: { type: Date, default: Date.now },
});

// Create models
const User = mongoose.model("User", userSchema);
const Counselor = mongoose.model("Counselor", counselorSchema);
const Chatroom = mongoose.model("Chatroom", chatroomSchema);
const Resource = mongoose.model("Resource", resourceSchema);

// Test data
const testUsers = [
  {
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    role: "user",
    isEmailVerified: true,
    phone: "+2348012345678",
    gender: "male",
    location: "Lagos, Nigeria",
  },
  {
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    role: "user",
    isEmailVerified: true,
    phone: "+2348087654321",
    gender: "female",
    location: "Abuja, Nigeria",
  },
  {
    firstName: "Dr. Sarah",
    lastName: "Johnson",
    email: "<EMAIL>",
    role: "counselor",
    isEmailVerified: true,
    phone: "+2348011111111",
    gender: "female",
    location: "Lagos, Nigeria",
  },
  {
    firstName: "Dr. Michael",
    lastName: "Brown",
    email: "<EMAIL>",
    role: "counselor",
    isEmailVerified: true,
    phone: "+2348022222222",
    gender: "male",
    location: "Port Harcourt, Nigeria",
  },
  {
    firstName: "Admin",
    lastName: "User",
    email: "<EMAIL>",
    role: "admin",
    isEmailVerified: true,
    phone: "+2348099999999",
    location: "Lagos, Nigeria",
  },
];

const testCounselors = [
  {
    bio: "Experienced clinical psychologist specializing in anxiety and depression treatment with over 8 years of practice.",
    specializations: ["anxiety", "depression", "stress-management"],
    experience: {
      years: 8,
      description:
        "Clinical psychologist with extensive experience in cognitive behavioral therapy and mindfulness-based interventions.",
    },
    qualifications: [
      {
        degree: "Ph.D. in Clinical Psychology",
        institution: "University of Lagos",
        year: 2015,
      },
    ],
    licenses: [
      {
        type: "Licensed Clinical Psychologist",
        number: "LCP-2015-001",
        issuingAuthority: "Nigerian Psychological Association",
        expiryDate: new Date("2025-12-31"),
      },
    ],
    profile: {
      languages: ["english", "yoruba"],
      approachDescription:
        "I use evidence-based approaches including CBT and mindfulness to help clients overcome challenges.",
      sessionTypes: ["individual", "couples"],
    },
    pricing: {
      currency: "NGN",
      ratePerMinute: 150,
      minimumSessionDuration: 45,
    },
    verification: {
      status: "approved",
      submittedAt: new Date("2024-01-15"),
      reviewedAt: new Date("2024-01-20"),
    },
  },
  {
    bio: "Licensed therapist focusing on relationship counseling and family therapy with a holistic approach.",
    specializations: ["relationships", "family-therapy", "couples-counseling"],
    experience: {
      years: 6,
      description:
        "Family therapist with specialization in systemic therapy and emotionally focused therapy.",
    },
    qualifications: [
      {
        degree: "Master of Arts in Marriage and Family Therapy",
        institution: "University of Ibadan",
        year: 2018,
      },
    ],
    licenses: [
      {
        type: "Licensed Marriage and Family Therapist",
        number: "LMFT-2018-045",
        issuingAuthority:
          "Association of Marriage and Family Therapists Nigeria",
        expiryDate: new Date("2025-06-30"),
      },
    ],
    profile: {
      languages: ["english", "igbo"],
      approachDescription:
        "I help couples and families build stronger relationships through effective communication and understanding.",
      sessionTypes: ["couples", "family", "individual"],
    },
    pricing: {
      currency: "NGN",
      ratePerMinute: 120,
      minimumSessionDuration: 60,
    },
    verification: {
      status: "approved",
      submittedAt: new Date("2024-02-01"),
      reviewedAt: new Date("2024-02-05"),
    },
  },
];

const testChatrooms = [
  {
    name: "Anxiety Support",
    description:
      "A safe space to discuss anxiety-related challenges and share coping strategies.",
    category: "Mental Health",
    rules: [
      "Be respectful and supportive",
      "No medical advice - seek professional help",
      "Maintain confidentiality",
      "No spam or promotional content",
    ],
  },
  {
    name: "Depression Support",
    description:
      "Connect with others who understand depression and share your journey.",
    category: "Mental Health",
    rules: [
      "Be kind and understanding",
      "Share experiences, not advice",
      "Respect privacy and boundaries",
      "Report inappropriate behavior",
    ],
  },
  {
    name: "Relationship Talk",
    description:
      "Discuss relationship challenges and celebrate healthy connections.",
    category: "Relationships",
    rules: [
      "Respect all relationship types",
      "No personal attacks or judgment",
      "Keep discussions constructive",
      "Maintain appropriate boundaries",
    ],
  },
  {
    name: "Mindfulness & Wellness",
    description:
      "Share mindfulness practices and wellness tips for mental health.",
    category: "Wellness",
    rules: [
      "Share evidence-based practices",
      "Be supportive and encouraging",
      "No medical claims without sources",
      "Respect different approaches",
    ],
  },
];

const testResources = [
  {
    title: "Understanding Anxiety: A Beginner's Guide",
    description:
      "Learn about anxiety symptoms, causes, and basic coping strategies.",
    content:
      "Anxiety is a normal human emotion that everyone experiences from time to time...",
    type: "article",
    category: "Anxiety",
    tags: ["anxiety", "mental-health", "coping-strategies"],
    author: "Dr. Sarah Johnson",
    difficulty: "beginner",
    estimatedReadTime: 5,
    isFeatured: true,
  },
  {
    title: "Breathing Exercises for Stress Relief",
    description:
      "Simple breathing techniques to help manage stress and anxiety.",
    content:
      "Deep breathing is one of the most effective ways to reduce stress...",
    type: "exercise",
    category: "Stress Management",
    tags: ["breathing", "stress-relief", "relaxation"],
    author: "Dr. Michael Brown",
    difficulty: "beginner",
    estimatedReadTime: 3,
    isFeatured: true,
  },
  {
    title: "Building Healthy Relationships",
    description:
      "Key principles for maintaining strong and healthy relationships.",
    content:
      "Healthy relationships are built on trust, communication, and mutual respect...",
    type: "article",
    category: "Relationships",
    tags: ["relationships", "communication", "trust"],
    author: "Dr. Sarah Johnson",
    difficulty: "intermediate",
    estimatedReadTime: 8,
  },
  {
    title: "Mindfulness Meditation for Beginners",
    description: "Introduction to mindfulness meditation and its benefits.",
    content:
      "Mindfulness meditation is a practice that involves focusing on the present moment...",
    type: "exercise",
    category: "Mindfulness",
    tags: ["mindfulness", "meditation", "mental-health"],
    author: "Dr. Michael Brown",
    difficulty: "beginner",
    estimatedReadTime: 10,
    isFeatured: true,
  },
];

// Main function to create test data
async function createTestData() {
  try {
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Clear existing data
    console.log("🧹 Clearing existing test data...");
    await User.deleteMany({ email: { $in: testUsers.map((u) => u.email) } });
    await Counselor.deleteMany({});
    await Chatroom.deleteMany({});
    await Resource.deleteMany({});

    // Create users
    console.log("👥 Creating test users...");
    const hashedPassword = await bcrypt.hash("password123", 10);

    const createdUsers = [];
    for (const userData of testUsers) {
      const user = new User({
        ...userData,
        password: hashedPassword,
      });
      const savedUser = await user.save();
      createdUsers.push(savedUser);
      console.log(
        `✅ Created user: ${userData.firstName} ${userData.lastName} (${userData.email})`
      );
    }

    // Create counselors
    console.log("👨‍⚕️ Creating test counselors...");
    const counselorUsers = createdUsers.filter((u) => u.role === "counselor");

    for (
      let i = 0;
      i < counselorUsers.length && i < testCounselors.length;
      i++
    ) {
      const counselor = new Counselor({
        ...testCounselors[i],
        userId: counselorUsers[i]._id,
      });
      await counselor.save();
      console.log(
        `✅ Created counselor profile for: ${counselorUsers[i].firstName} ${counselorUsers[i].lastName}`
      );
    }

    // Create chatrooms
    console.log("💬 Creating test chatrooms...");
    const adminUser = createdUsers.find((u) => u.role === "admin");

    for (const roomData of testChatrooms) {
      const chatroom = new Chatroom({
        ...roomData,
        createdBy: adminUser._id,
        moderators: [adminUser._id],
      });
      await chatroom.save();
      console.log(`✅ Created chatroom: ${roomData.name}`);
    }

    // Create resources
    console.log("📚 Creating test resources...");
    for (const resourceData of testResources) {
      const resource = new Resource({
        ...resourceData,
        createdBy: adminUser._id,
      });
      await resource.save();
      console.log(`✅ Created resource: ${resourceData.title}`);
    }

    console.log("\n🎉 Test data creation completed successfully!");
    console.log("\n📋 Test Accounts Created:");
    console.log("================================");

    testUsers.forEach((user) => {
      console.log(`${user.role.toUpperCase()}: ${user.email} / password123`);
    });

    console.log("\n🚀 You can now start testing the application!");
    console.log("Frontend: http://localhost:3000");
    console.log("Backend: http://localhost:5000");
  } catch (error) {
    console.error("❌ Error creating test data:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run the script
if (require.main === module) {
  createTestData();
}

module.exports = { createTestData };

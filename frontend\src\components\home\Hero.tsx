import Link from "next/link";
import { ArrowR<PERSON>, Heart, Users, Shield, Eye } from "lucide-react";

export default function Hero() {
  return (
    <section className="relative bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 text-white">
      <div className="absolute inset-0 bg-black opacity-10"></div>
      <div className="relative container mx-auto px-4 py-20 lg:py-32">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
            Your Mental Health
            <span className="block text-purple-200">Matters</span>
          </h1>
          <p className="text-xl lg:text-2xl mb-8 text-purple-100 leading-relaxed">
            Connect with licensed counselors, join supportive communities, and
            take control of your well-being with Theramea.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link
              href="/auth/signup"
              className="bg-white text-purple-800 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-50 transition-colors inline-flex items-center justify-center"
            >
              Get Started Today
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="/guest/start"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-800 transition-colors inline-flex items-center justify-center"
            >
              <Eye className="mr-2 h-5 w-5" />
              Continue as Guest
            </Link>
          </div>

          <div className="flex justify-center mb-12">
            <Link
              href="/auth/login"
              className="text-purple-200 hover:text-white transition-colors inline-flex items-center text-sm"
            >
              Already have an account? Sign in
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div className="flex flex-col items-center">
              <Heart className="h-12 w-12 text-purple-200 mb-3" />
              <div className="text-3xl font-bold">1000+</div>
              <div className="text-purple-200">Lives Improved</div>
            </div>
            <div className="flex flex-col items-center">
              <Users className="h-12 w-12 text-purple-200 mb-3" />
              <div className="text-3xl font-bold">50+</div>
              <div className="text-purple-200">Licensed Counselors</div>
            </div>
            <div className="flex flex-col items-center">
              <Shield className="h-12 w-12 text-purple-200 mb-3" />
              <div className="text-3xl font-bold">100%</div>
              <div className="text-purple-200">Confidential</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

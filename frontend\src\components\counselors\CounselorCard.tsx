"use client";

import { useState } from "react";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import SignupPromptModal from "@/components/modals/SignupPromptModal";
import ShareModal from "@/components/modals/ShareModal";
import { ApiCounselor } from "@/types/counselor";

interface CounselorCardProps {
  counselor: ApiCounselor;
  onLike?: (counselorId: string) => void;
  isLiked?: boolean;
}

export default function CounselorCard({
  counselor,
  onLike,
  isLiked = false,
}: CounselorCardProps) {
  const { isAuthenticated, isGuest } = useAuthStore();
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);

  const handleLikeClick = (e: React.MouseEvent) => {
    e.preventDefault();
    // Only fully registered and authenticated users can like counselors
    // Show signup modal for: guests, unauthenticated users, or partially registered users
    if (isGuest ||!isAuthenticated) {
      setShowSignupModal(true);
      return;
    }
    onLike?.(counselor._id);
  };

  const handleShareClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowShareModal(true);
  };

  const shareUrl = `${
    typeof window !== "undefined" ? window.location.origin : ""
  }/counselors/${counselor._id || "unknown"}`;

  const formatPrice = (rate: number, currency: string) => {
    const symbol = currency === "NGN" ? "₦" : "$";
    return `${symbol}${rate.toLocaleString()}`;
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div className="p-6">
          <div className="flex items-start space-x-4">
            {/* Avatar */}
            <div className="relative">
              <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                <svg
                  className="w-10 h-10 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              {/* You can add isOnline status if needed */}
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 border-2 border-white rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>

            {/* Main Info */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {counselor.userId.firstName} {counselor.userId.lastName}
                  </h3>
                  <p className="text-purple-600 font-medium">
                    {counselor.profile.title}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatPrice(
                      (counselor.pricing?.ratePerMinute || 0) *
                        (counselor.pricing?.minimumSessionDuration || 15),
                      counselor.pricing?.currency || "NGN"
                    )}
                  </div>
                  <div className="text-sm text-gray-500">per session</div>
                </div>
              </div>

              {/* Rating and Experience */}
              <div className="flex items-center space-x-4 mb-3">
                <div className="flex items-center space-x-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={`w-4 h-4 ${
                          i < 4 ? "text-yellow-400" : "text-gray-300"
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {counselor.statistics.averageRating.toFixed(1)} (
                    {counselor.statistics.totalReviews} reviews)
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {counselor.experience.years || 0} years experience
                </div>
              </div>

              {/* Bio */}
              <p className="text-gray-600 mb-4">
                {counselor.bio ||
                  "Experienced professional counselor dedicated to helping clients achieve their mental health goals through evidence-based therapeutic approaches."}
              </p>

              {/* Specializations */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {(counselor.specializations || []).map((spec) => (
                    <span
                      key={spec}
                      className="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full"
                    >
                      {spec.charAt(0).toUpperCase() + spec.slice(1)}
                    </span>
                  ))}
                </div>
              </div>

              {/* Languages and Credentials */}
              <div className="grid md:grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Languages: </span>
                  <span className="text-gray-600">English, Spanish</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">
                    Next Available:{" "}
                  </span>
                  <span className="text-gray-600">Available for booking</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Link
                    href={`/counselors/${counselor.id || "unknown"}/book`}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    Book Session
                  </Link>
                  <Link
                    href={`/counselors/${counselor.id || "unknown"}`}
                    className="border border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    View Profile
                  </Link>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleLikeClick}
                    className={`p-2 transition-colors ${
                      isLiked
                        ? "text-red-500"
                        : "text-gray-400 hover:text-red-500"
                    }`}
                  >
                    <svg
                      className="w-5 h-5"
                      fill={isLiked ? "currentColor" : "none"}
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={handleShareClick}
                    className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Signup Modal */}
      <SignupPromptModal
        isOpen={showSignupModal}
        onClose={() => setShowSignupModal(false)}
        feature="like"
      />

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        shareUrl={shareUrl}
        title={`${counselor.userId.firstName} ${counselor.userId.lastName} - ${counselor.profile.title}`}
        description={counselor.bio}
      />
    </>
  );
}

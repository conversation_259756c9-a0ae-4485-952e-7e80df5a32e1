const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

// MongoDB connection URI from environment variables or default
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

async function resetUserPassword() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB successfully");

    // The user who owns the session
    const userId = "687fe3b739da96aef19699d3";
    const newPassword = "password123"; // Common test password

    // Hash the new password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update the user's password
    const result = await mongoose.connection.db.collection("users").updateOne(
      { _id: new mongoose.Types.ObjectId(userId) },
      {
        $set: {
          password: hashedPassword,
          // Also ensure the account is active and email is verified for testing
          isActive: true,
          isEmailVerified: true,
        },
      }
    );

    if (result.modifiedCount > 0) {
      console.log("✅ Password reset successful!");
      console.log(`User: Victor Okoroji (<EMAIL>)`);
      console.log(`New password: ${newPassword}`);
      console.log(`You can now log in and make the payment.`);
    } else {
      console.log(
        "❌ Password reset failed - user not found or no changes made"
      );
    }
  } catch (error) {
    console.error("❌ Error resetting password:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

resetUserPassword();

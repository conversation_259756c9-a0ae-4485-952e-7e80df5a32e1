const axios = require("axios");

const BASE_URL = "http://localhost:5000/api";

async function testSessionCancellation() {
  try {
    console.log("🧪 Testing Session Cancellation API...\n");

    // First, let's try to authenticate to get a token
    console.log("1. Authenticating user...");
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: "<EMAIL>", // Using the user from your database
      password: "password123", // You may need to adjust this
    });

    if (!loginResponse.data.success) {
      console.log("❌ Authentication failed. Please check credentials.");
      console.log("Response:", JSON.stringify(loginResponse.data, null, 2));
      return;
    }

    console.log(
      "Login response structure:",
      JSON.stringify(loginResponse.data, null, 2)
    );

    // Extract token from the correct location
    const authToken =
      loginResponse.data.data.tokens?.accessToken ||
      loginResponse.data.data.token;
    console.log("✅ Authentication successful");

    // Use a valid session ID from your database
    const sessionId = "689e5af766e30bf744d47aeb"; // First session from your list

    console.log(`\n2. Testing session cancellation for session: ${sessionId}`);

    const cancelResponse = await axios.put(
      `${BASE_URL}/sessions/${sessionId}/cancel`,
      {
        reason: "i am now occupied",
      },
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (cancelResponse.data.success) {
      console.log("✅ Session cancellation successful!");
      console.log("Response:", JSON.stringify(cancelResponse.data, null, 2));
    } else {
      console.log("❌ Session cancellation failed");
      console.log("Response:", JSON.stringify(cancelResponse.data, null, 2));
    }
  } catch (error) {
    console.log("❌ Error occurred:");
    if (error.response) {
      console.log("Status:", error.response.status);
      console.log("Data:", JSON.stringify(error.response.data, null, 2));
    } else {
      console.log("Error:", error.message);
    }
  }
}

// Alternative test with different session ID if first one fails
async function testWithDifferentSession() {
  try {
    console.log("\n🔄 Trying with different session ID...");

    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: "<EMAIL>",
      password: "password123",
    });

    const authToken =
      loginResponse.data.data.tokens?.accessToken ||
      loginResponse.data.data.token;
    const sessionId = "689e8ac358caf361f78ce110"; // Different session ID

    const cancelResponse = await axios.put(
      `${BASE_URL}/sessions/${sessionId}/cancel`,
      {
        reason: "testing cancellation with different session",
      },
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ Alternative session cancellation test:");
    console.log("Response:", JSON.stringify(cancelResponse.data, null, 2));
  } catch (error) {
    console.log("❌ Alternative test also failed:");
    if (error.response) {
      console.log("Status:", error.response.status);
      console.log("Data:", JSON.stringify(error.response.data, null, 2));
    } else {
      console.log("Error:", error.message);
    }
  }
}

// Run the tests
async function runTests() {
  await testSessionCancellation();
  await testWithDifferentSession();
}

runTests();

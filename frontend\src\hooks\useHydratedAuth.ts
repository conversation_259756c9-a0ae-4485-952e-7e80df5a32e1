"use client";

import { useAuthStore } from "@/store/authStore";

export function useHydratedAuth() {
  const hasHydrated = useAuthStore((state) => state.hasHydrated);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isGuest = useAuthStore((state) => state.isGuest);
  const user = useAuthStore((state) => state.user);
  const error = useAuthStore((state) => state.error);
  const isLoading = useAuthStore((state) => state.isLoading);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  return {
    hasHydrated,
    isAuthenticated: hasHydrated ? isAuthenticated : false,
    isGuest: hasHydrated ? isGuest : false,
    user: hasHydrated ? user : null,
    error: hasHydrated ? error : null,
    isLoading: hasHydrated ? isLoading : true,
    isReady: hasHydrated && !isLoading,
    checkAuth,
  };
}

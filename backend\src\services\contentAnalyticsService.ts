import { Resource } from "@/models/Resource";
import { logger } from "@/utils/logger";

export interface ContentAnalytics {
  overview: {
    totalContent: number;
    publishedContent: number;
    totalViews: number;
    totalLikes: number;
    totalBookmarks: number;
    averageRating: number;
  };
  contentByType: {
    type: string;
    count: number;
    views: number;
    engagement: number;
  }[];
  contentByCategory: {
    category: string;
    count: number;
    views: number;
    averageRating: number;
  }[];
  topContent: {
    mostViewed: any[];
    mostLiked: any[];
    mostBookmarked: any[];
    highestRated: any[];
  };
  userEngagement: {
    totalUsers: number;
    activeUsers: number;
    engagementRate: number;
    averageSessionTime: number;
  };
  trends: {
    viewsTrend: { date: string; views: number }[];
    engagementTrend: { date: string; engagement: number }[];
    contentCreationTrend: { date: string; created: number }[];
  };
}

export interface AuthorAnalytics {
  authorId: string;
  overview: {
    totalContent: number;
    publishedContent: number;
    totalViews: number;
    totalLikes: number;
    totalBookmarks: number;
    averageRating: number;
  };
  contentPerformance: {
    contentId: string;
    title: string;
    type: string;
    views: number;
    likes: number;
    bookmarks: number;
    rating: number;
    publishedAt: Date;
  }[];
  audienceInsights: {
    topCategories: string[];
    popularTags: string[];
    engagementByType: { type: string; engagement: number }[];
  };
}

export interface UserAnalytics {
  userId: string;
  contentConsumption: {
    totalViews: number;
    totalBookmarks: number;
    totalLikes: number;
    totalRatings: number;
    favoriteCategories: string[];
    favoriteTypes: string[];
    readingTime: number;
  };
  engagementPattern: {
    dailyActivity: { hour: number; activity: number }[];
    weeklyActivity: { day: string; activity: number }[];
    monthlyActivity: { month: string; activity: number }[];
  };
  recommendations: {
    suggestedContent: any[];
    suggestedCategories: string[];
    suggestedAuthors: <AUTHORS>
  };
}

export class ContentAnalyticsService {
  /**
   * Get comprehensive content analytics
   */
  static async getContentAnalytics(dateRange?: {
    from: Date;
    to: Date;
  }): Promise<ContentAnalytics> {
    try {
      const matchStage: any = {};

      if (dateRange) {
        matchStage.publishedAt = {
          $gte: dateRange.from,
          $lte: dateRange.to,
        };
      }

      // Get overview statistics
      const overview = await this.getOverviewStats(matchStage);

      // Get content by type
      const contentByType = await this.getContentByType(matchStage);

      // Get content by category
      const contentByCategory = await this.getContentByCategory(matchStage);

      // Get top performing content
      const topContent = await this.getTopContent(matchStage);

      // Get user engagement metrics
      const userEngagement = await this.getUserEngagementMetrics(dateRange);

      // Get trends data
      const trends = await this.getTrendsData(dateRange);

      return {
        overview,
        contentByType,
        contentByCategory,
        topContent,
        userEngagement,
        trends,
      };
    } catch (error) {
      logger.error("Get content analytics error:", error);
      throw error;
    }
  }

  /**
   * Get analytics for a specific author
   */
  static async getAuthorAnalytics(
    authorId: string,
    dateRange?: { from: Date; to: Date }
  ): Promise<AuthorAnalytics> {
    try {
      const matchStage: any = { author: authorId };

      if (dateRange) {
        matchStage.publishedAt = {
          $gte: dateRange.from,
          $lte: dateRange.to,
        };
      }

      // Get author overview
      const overview = await this.getOverviewStats(matchStage);

      // Get content performance
      const contentPerformance = await Resource.find(matchStage)
        .select("title type statistics publishedAt")
        .sort({ "statistics.views": -1 })
        .limit(20)
        .lean();

      const formattedPerformance = contentPerformance.map((content) => ({
        contentId: content._id.toString(),
        title: content.title,
        type: content.type,
        views: content.statistics.views,
        likes: content.statistics.likes,
        bookmarks: content.statistics.bookmarks,
        rating: content.statistics.averageRating,
        publishedAt: content.publishedAt || new Date(),
      }));

      // Get audience insights
      const audienceInsights = await this.getAudienceInsights(authorId);

      return {
        authorId,
        overview,
        contentPerformance: formattedPerformance,
        audienceInsights,
      };
    } catch (error) {
      logger.error("Get author analytics error:", error);
      throw error;
    }
  }

  /**
   * Get analytics for a specific user
   */
  static async getUserAnalytics(
    userId: string,
    dateRange?: { from: Date; to: Date }
  ): Promise<UserAnalytics> {
    try {
      const dateFilter = dateRange
        ? {
            "interactions.timestamp": {
              $gte: dateRange.from,
              $lte: dateRange.to,
            },
          }
        : {};

      // Get user's content consumption
      const contentConsumption = await this.getUserContentConsumption(
        userId,
        dateFilter
      );

      // Get engagement patterns
      const engagementPattern = await this.getUserEngagementPattern(
        userId,
        dateFilter
      );

      // Get recommendations
      const recommendations = await this.getUserRecommendations(userId);

      return {
        userId,
        contentConsumption,
        engagementPattern,
        recommendations,
      };
    } catch (error) {
      logger.error("Get user analytics error:", error);
      throw error;
    }
  }

  /**
   * Track content interaction for analytics
   */
  static async trackContentInteraction(
    contentId: string,
    userId: string,
    action: string,
    metadata?: any
  ): Promise<void> {
    try {
      // This would typically be stored in a separate analytics collection
      // For now, we'll just log it
      logger.debug(
        `Analytics: ${action} on content ${contentId} by user ${userId}`,
        metadata
      );

      // TODO: Implement detailed analytics tracking
      // This could include:
      // - Time spent on content
      // - Scroll depth
      // - Click tracking
      // - Session information
    } catch (error) {
      logger.error("Track content interaction error:", error);
    }
  }

  /**
   * Get content performance report
   */
  static async getContentPerformanceReport(contentId: string): Promise<any> {
    try {
      const content = await Resource.findById(contentId)
        .populate("author", "firstName lastName")
        .lean();

      if (!content) {
        throw new Error("Content not found");
      }

      // Calculate performance metrics
      const totalInteractions =
        content.statistics.views +
        content.statistics.likes +
        content.statistics.bookmarks +
        content.statistics.shares;

      const engagementRate =
        content.statistics.views > 0
          ? ((content.statistics.likes + content.statistics.bookmarks) /
              content.statistics.views) *
            100
          : 0;

      // Get interaction timeline
      const interactionTimeline = content.interactions
        .sort(
          (a: any, b: any) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
        .slice(0, 50); // Last 50 interactions

      return {
        content: {
          id: content._id,
          title: content.title,
          type: content.type,
          category: content.category,
          publishedAt: content.publishedAt,
          author: content.author,
        },
        metrics: {
          views: content.statistics.views,
          likes: content.statistics.likes,
          bookmarks: content.statistics.bookmarks,
          shares: content.statistics.shares,
          downloads: content.statistics.downloads,
          completions: content.statistics.completions,
          averageRating: content.statistics.averageRating,
          totalRatings: content.statistics.totalRatings,
          totalInteractions,
          engagementRate,
        },
        interactionTimeline,
        ratings: content.ratings.slice(-10), // Last 10 ratings
      };
    } catch (error) {
      logger.error("Get content performance report error:", error);
      throw error;
    }
  }

  /**
   * Get overview statistics
   */
  private static async getOverviewStats(matchStage: any): Promise<any> {
    const stats = await Resource.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalContent: { $sum: 1 },
          publishedContent: {
            $sum: { $cond: [{ $eq: ["$isPublished", true] }, 1, 0] },
          },
          totalViews: { $sum: "$statistics.views" },
          totalLikes: { $sum: "$statistics.likes" },
          totalBookmarks: { $sum: "$statistics.bookmarks" },
          averageRating: { $avg: "$statistics.averageRating" },
        },
      },
    ]);

    return (
      stats[0] || {
        totalContent: 0,
        publishedContent: 0,
        totalViews: 0,
        totalLikes: 0,
        totalBookmarks: 0,
        averageRating: 0,
      }
    );
  }

  /**
   * Get content statistics by type
   */
  private static async getContentByType(matchStage: any): Promise<any[]> {
    return await Resource.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          views: { $sum: "$statistics.views" },
          likes: { $sum: "$statistics.likes" },
          bookmarks: { $sum: "$statistics.bookmarks" },
        },
      },
      {
        $addFields: {
          engagement: { $add: ["$likes", "$bookmarks"] },
        },
      },
      {
        $project: {
          type: "$_id",
          count: 1,
          views: 1,
          engagement: 1,
          _id: 0,
        },
      },
      { $sort: { count: -1 } },
    ]);
  }

  /**
   * Get content statistics by category
   */
  private static async getContentByCategory(matchStage: any): Promise<any[]> {
    return await Resource.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: "$category",
          count: { $sum: 1 },
          views: { $sum: "$statistics.views" },
          averageRating: { $avg: "$statistics.averageRating" },
        },
      },
      {
        $project: {
          category: "$_id",
          count: 1,
          views: 1,
          averageRating: 1,
          _id: 0,
        },
      },
      { $sort: { count: -1 } },
    ]);
  }

  /**
   * Get top performing content
   */
  private static async getTopContent(matchStage: any): Promise<any> {
    const [mostViewed, mostLiked, mostBookmarked, highestRated] =
      await Promise.all([
        Resource.find(matchStage)
          .populate("author", "firstName lastName")
          .sort({ "statistics.views": -1 })
          .limit(5)
          .select("title type statistics author publishedAt"),

        Resource.find(matchStage)
          .populate("author", "firstName lastName")
          .sort({ "statistics.likes": -1 })
          .limit(5)
          .select("title type statistics author publishedAt"),

        Resource.find(matchStage)
          .populate("author", "firstName lastName")
          .sort({ "statistics.bookmarks": -1 })
          .limit(5)
          .select("title type statistics author publishedAt"),

        Resource.find(matchStage)
          .populate("author", "firstName lastName")
          .sort({ "statistics.averageRating": -1 })
          .limit(5)
          .select("title type statistics author publishedAt"),
      ]);

    return {
      mostViewed,
      mostLiked,
      mostBookmarked,
      highestRated,
    };
  }

  /**
   * Get user engagement metrics
   */
  private static async getUserEngagementMetrics(dateRange?: {
    from: Date;
    to: Date;
  }): Promise<any> {
    // TODO: Implement user engagement tracking
    // This would require tracking user sessions and interactions
    return {
      totalUsers: 0,
      activeUsers: 0,
      engagementRate: 0,
      averageSessionTime: 0,
    };
  }

  /**
   * Get trends data
   */
  private static async getTrendsData(dateRange?: {
    from: Date;
    to: Date;
  }): Promise<any> {
    // TODO: Implement trends tracking
    // This would require time-series data collection
    return {
      viewsTrend: [],
      engagementTrend: [],
      contentCreationTrend: [],
    };
  }

  /**
   * Get audience insights for an author
   */
  private static async getAudienceInsights(authorId: string): Promise<any> {
    const insights = await Resource.aggregate([
      { $match: { author: authorId } },
      {
        $group: {
          _id: null,
          categories: { $push: "$category" },
          allTags: { $push: "$tags" },
          types: { $push: "$type" },
        },
      },
    ]);

    const data = insights[0] || { categories: [], allTags: [], types: [] };

    return {
      topCategories: [...new Set(data.categories)].slice(0, 5),
      popularTags: [...new Set(data.allTags.flat())].slice(0, 10),
      engagementByType: [...new Set(data.types)].map((type) => ({
        type,
        engagement: Math.floor(Math.random() * 100), // TODO: Calculate real engagement
      })),
    };
  }

  /**
   * Get user content consumption data
   */
  private static async getUserContentConsumption(
    userId: string,
    dateFilter: any
  ): Promise<any> {
    const userInteractions = await Resource.find({
      "interactions.userId": userId,
      ...dateFilter,
    }).select("category type interactions");

    const consumption = {
      totalViews: 0,
      totalBookmarks: 0,
      totalLikes: 0,
      totalRatings: 0,
      favoriteCategories: [] as string[],
      favoriteTypes: [] as string[],
      readingTime: 0,
    };

    const categories = new Set<string>();
    const types = new Set<string>();

    userInteractions.forEach((content) => {
      content.interactions.forEach((interaction: any) => {
        if (interaction.userId.toString() === userId) {
          switch (interaction.action) {
            case "view":
              consumption.totalViews++;
              break;
            case "bookmark":
              consumption.totalBookmarks++;
              break;
            case "like":
              consumption.totalLikes++;
              break;
          }
        }
      });

      if (content.category) categories.add(content.category);
      if (content.type) types.add(content.type);
    });

    consumption.favoriteCategories = Array.from(categories).slice(0, 5);
    consumption.favoriteTypes = Array.from(types).slice(0, 3);

    return consumption;
  }

  /**
   * Get user engagement pattern
   */
  private static async getUserEngagementPattern(
    userId: string,
    dateFilter: any
  ): Promise<any> {
    // TODO: Implement detailed engagement pattern analysis
    return {
      dailyActivity: [],
      weeklyActivity: [],
      monthlyActivity: [],
    };
  }

  /**
   * Get user recommendations
   */
  private static async getUserRecommendations(userId: string): Promise<any> {
    // TODO: Implement recommendation engine
    return {
      suggestedContent: [],
      suggestedCategories: [],
      suggestedAuthors: <AUTHORS>
    };
  }
}

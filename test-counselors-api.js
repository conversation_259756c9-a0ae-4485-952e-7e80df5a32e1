const axios = require("axios");

async function testCounselorsAPI() {
  try {
    console.log("🧪 Testing Counselors API...");

    const response = await axios.get(
      "http://localhost:5000/api/counselors?limit=50"
    );

    console.log("✅ API Response:");
    console.log("Status:", response.status);
    console.log("Success:", response.data.success);
    console.log("Total counselors:", response.data.data.counselors.length);
    console.log("Pagination:", response.data.data.pagination);

    if (response.data.data.counselors.length > 0) {
      console.log("\n👩‍⚕️ Counselors found:");
      response.data.data.counselors.forEach((counselor, i) => {
        console.log(
          `${i + 1}. ${counselor.userId.firstName} ${counselor.userId.lastName}`
        );
        console.log(
          `   Specializations: ${counselor.specializations.join(", ")}`
        );
        console.log(`   Rating: ${counselor.statistics.averageRating}/5.0`);
        console.log(
          `   Rate: ${counselor.pricing.currency} ${counselor.pricing.ratePerMinute}/min`
        );
        console.log(`   Status: ${counselor.verification.status}`);
        console.log("");
      });
    }
  } catch (error) {
    console.error("❌ API Test Failed:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    } else {
      console.error("Error:", error.message);
    }
  }
}

testCounselorsAPI();

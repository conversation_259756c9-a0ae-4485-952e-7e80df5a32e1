/**
 * General utility functions for the application
 */

/**
 * Formats status text by converting underscores to spaces and capitalizing each word
 * @param status - The status string to format
 * @returns Formatted status string
 *
 * @example
 * formatStatusText('pending_approval') => 'Pending Approval'
 * formatStatusText('in_progress') => 'In Progress'
 * formatStatusText('completed') => 'Completed'
 */
export function formatStatusText(status: string): string {
  if (!status) return "";

  // Convert underscores to spaces and properly capitalize each word
  return status
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Formats a name or title by capitalizing each word
 * @param text - The text to format
 * @returns Formatted text with each word capitalized
 */
export function formatTitle(text: string): string {
  if (!text) return "";

  return text
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Converts a camelCase or PascalCase string to readable text
 * @param text - The camelCase/PascalCase text
 * @returns Formatted readable text
 *
 * @example
 * camelCaseToReadable('firstName') => 'First Name'
 * camelCaseToReadable('isOnlineStatus') => 'Is Online Status'
 */
export function camelCaseToReadable(text: string): string {
  if (!text) return "";

  // Insert space before uppercase letters and capitalize first letter
  return text
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

/**
 * Truncates text to a specified length and adds ellipsis
 * @param text - The text to truncate
 * @param maxLength - Maximum length before truncation
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;

  return text.substring(0, maxLength).trim() + "...";
}

/**
 * Validates if a string is a valid email address
 * @param email - The email string to validate
 * @returns True if valid email, false otherwise
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generates a random string of specified length
 * @param length - Length of the random string
 * @returns Random string
 */
export function generateRandomString(length: number): string {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

/**
 * Safely parses JSON string, returns null if invalid
 * @param jsonString - The JSON string to parse
 * @returns Parsed object or null if invalid
 */
export function safeJsonParse(jsonString: string): any {
  try {
    return JSON.parse(jsonString);
  } catch {
    return null;
  }
}

/**
 * Debounces a function call
 * @param func - The function to debounce
 * @param wait - The number of milliseconds to delay
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Deep clones an object
 * @param obj - The object to clone
 * @returns Deep cloned object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array)
    return obj.map((item) => deepClone(item)) as unknown as T;
  if (typeof obj === "object") {
    const clonedObj = {} as { [key: string]: any };
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj as T;
  }
  return obj;
}

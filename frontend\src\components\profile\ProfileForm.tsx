"use client";

import { useState } from "react";
import { User } from "@/types/auth";
import { AREAS_OF_INTEREST } from "@/types/auth";
import { useAuthStore } from "@/store/authStore";
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

interface ProfileFormProps {
  user: User;
}

export default function ProfileForm({ user }: ProfileFormProps) {
  const { checkAuth, tokens } = useAuthStore();
  const [formData, setFormData] = useState({
    firstName: user.firstName || "",
    lastName: user.lastName || "",
    username: user.username || "",
    areasOfInterest: user.areasOfInterest || [],
    location: {
      country: user.location?.country || "",
      city: user.location?.city || "",
    },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error" | "info";
    text: string;
  } | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      errors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!formData.username.trim()) {
      errors.username = "Username is required";
    } else if (formData.username.length < 3) {
      errors.username = "Username must be at least 3 characters";
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      errors.username =
        "Username can only contain letters, numbers, and underscores";
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors((prev) => ({ ...prev, [name]: "" }));
    }

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as any),
          [child]: value,
        },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleAreasOfInterestChange = (area: string) => {
    setFormData((prev) => ({
      ...prev,
      areasOfInterest: prev.areasOfInterest.includes(area)
        ? prev.areasOfInterest.filter((a) => a !== area)
        : [...prev.areasOfInterest, area],
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      setMessage({
        type: "error",
        text: "Please fix the errors below before submitting.",
      });
      return;
    }

    setIsSubmitting(true);
    setMessage(null);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/profile`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${tokens?.accessToken}`,
          },
          body: JSON.stringify(formData),
        }
      );

      const data = await response.json();

      if (response.ok) {
        // Refresh user data
        await checkAuth(true);
        setMessage({
          type: "success",
          text: "Your profile has been updated successfully!",
        });

        // Clear message after 5 seconds
        setTimeout(() => setMessage(null), 5000);
      } else {
        if (response.status === 409 && data.field === "username") {
          setFieldErrors((prev) => ({
            ...prev,
            username: "Username is already taken",
          }));
          setMessage({
            type: "error",
            text: "Username is already taken. Please choose a different one.",
          });
        } else {
          setMessage({
            type: "error",
            text: data.message || "Failed to update profile. Please try again.",
          });
        }
      }
    } catch (error) {
      console.error("Profile update error:", error);
      setMessage({
        type: "error",
        text: "Network error. Please check your connection and try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const MessageAlert = ({
    message,
  }: {
    message: { type: "success" | "error" | "info"; text: string };
  }) => {
    const icons = {
      success: CheckCircleIcon,
      error: ExclamationCircleIcon,
      info: InformationCircleIcon,
    };

    const colors = {
      success: "bg-green-50 text-green-800 border-green-200",
      error: "bg-red-50 text-red-800 border-red-200",
      info: "bg-blue-50 text-blue-800 border-blue-200",
    };

    const Icon = icons[message.type];

    return (
      <div
        className={`rounded-lg p-4 border ${
          colors[message.type]
        } flex items-start space-x-3`}
      >
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        <div>
          <p className="text-sm font-medium">{message.text}</p>
        </div>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {message && <MessageAlert message={message} />}

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label
            htmlFor="firstName"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            First Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="firstName"
            id="firstName"
            value={formData.firstName}
            onChange={handleChange}
            className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm transition-colors ${
              fieldErrors.firstName
                ? "border-red-300 bg-red-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            required
            aria-describedby={
              fieldErrors.firstName ? "firstName-error" : undefined
            }
          />
          {fieldErrors.firstName && (
            <p
              id="firstName-error"
              className="mt-1 text-sm text-red-600 flex items-center"
            >
              <ExclamationCircleIcon className="h-4 w-4 mr-1" />
              {fieldErrors.firstName}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="lastName"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Last Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="lastName"
            id="lastName"
            value={formData.lastName}
            onChange={handleChange}
            className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm transition-colors ${
              fieldErrors.lastName
                ? "border-red-300 bg-red-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            required
            aria-describedby={
              fieldErrors.lastName ? "lastName-error" : undefined
            }
          />
          {fieldErrors.lastName && (
            <p
              id="lastName-error"
              className="mt-1 text-sm text-red-600 flex items-center"
            >
              <ExclamationCircleIcon className="h-4 w-4 mr-1" />
              {fieldErrors.lastName}
            </p>
          )}
        </div>
      </div>

      <div>
        <label
          htmlFor="username"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Username <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <span className="absolute left-3 top-2 text-gray-500 text-sm">@</span>
          <input
            type="text"
            name="username"
            id="username"
            value={formData.username}
            onChange={handleChange}
            className={`block w-full pl-8 pr-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm transition-colors ${
              fieldErrors.username
                ? "border-red-300 bg-red-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            required
            aria-describedby={
              fieldErrors.username ? "username-error" : "username-help"
            }
          />
        </div>
        {fieldErrors.username ? (
          <p
            id="username-error"
            className="mt-1 text-sm text-red-600 flex items-center"
          >
            <ExclamationCircleIcon className="h-4 w-4 mr-1" />
            {fieldErrors.username}
          </p>
        ) : (
          <p id="username-help" className="mt-1 text-sm text-gray-500">
            Letters, numbers, and underscores only. Minimum 3 characters.
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label
            htmlFor="location.country"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Country
          </label>
          <input
            type="text"
            name="location.country"
            id="location.country"
            value={formData.location.country}
            onChange={handleChange}
            placeholder="e.g., United States"
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm hover:border-gray-400 transition-colors"
          />
        </div>

        <div>
          <label
            htmlFor="location.city"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            City
          </label>
          <input
            type="text"
            name="location.city"
            id="location.city"
            value={formData.location.city}
            onChange={handleChange}
            placeholder="e.g., New York"
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm hover:border-gray-400 transition-colors"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Areas of Interest
        </label>
        <p className="text-sm text-gray-500 mb-4">
          Select topics that interest you to get personalized content
          recommendations.
        </p>
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {AREAS_OF_INTEREST.map((area) => (
            <label
              key={area.value}
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <input
                type="checkbox"
                checked={formData.areasOfInterest.includes(area.value)}
                onChange={() => handleAreasOfInterestChange(area.value)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <span className="ml-3 text-sm text-gray-700 font-medium">
                {area.label}
              </span>
            </label>
          ))}
        </div>
        {formData.areasOfInterest.length > 0 && (
          <p className="mt-2 text-sm text-purple-600">
            {formData.areasOfInterest.length} area
            {formData.areasOfInterest.length !== 1 ? "s" : ""} selected
          </p>
        )}
      </div>

      <div className="flex justify-end pt-6 border-t border-gray-200">
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isSubmitting ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </button>
      </div>
    </form>
  );
}

import { NextRequest, NextResponse } from "next/server";
import { decrypt } from "@/lib/session";

const protectedRoutes = ["/profile"];

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  const isProtectedRoute = protectedRoutes.some(
    (route) => path === route || path.startsWith(route + "/")
  );

  const cookie = req.cookies.get("session")?.value;
  const session = await decrypt(cookie);

  const authToken = req.cookies.get("accessToken")?.value;

  if (isProtectedRoute) {
    if (!session?.userId && !authToken) {
      const loginUrl = new URL("/auth/login", req.nextUrl);
      loginUrl.searchParams.set("redirect", path);
      return NextResponse.redirect(loginUrl);
    }

    if (authToken && !session?.userId) {
      try {
        if (
          typeof authToken !== "string" ||
          authToken.split(".").length !== 3
        ) {
          const loginUrl = new URL("/auth/login", req.nextUrl);
          loginUrl.searchParams.set("redirect", path);
          return NextResponse.redirect(loginUrl);
        }

        const payload = JSON.parse(atob(authToken.split(".")[1]));
        if (payload.isGuest) {
          const loginUrl = new URL("/auth/login", req.nextUrl);
          loginUrl.searchParams.set("redirect", path);
          loginUrl.searchParams.set(
            "message",
            "Please register or login to access your profile"
          );
          return NextResponse.redirect(loginUrl);
        }
      } catch (error) {
        const loginUrl = new URL("/auth/login", req.nextUrl);
        loginUrl.searchParams.set("redirect", path);
        return NextResponse.redirect(loginUrl);
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)",
  ],
};

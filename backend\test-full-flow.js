const http = require('http');

async function testFullFlow() {
  console.log('🧪 Testing End-to-End Counselors Flow');
  console.log('=====================================\n');
  
  // Test 1: API Backend
  console.log('1. Testing Backend API...');
  try {
    const backendResponse = await new Promise((resolve, reject) => {
      const req = http.request('http://localhost:5000/api/counselors', (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => resolve({ status: res.statusCode, data: JSON.parse(data) }));
      });
      req.on('error', reject);
      req.end();
    });
    
    console.log(`   ✅ Backend API Status: ${backendResponse.status}`);
    console.log(`   ✅ Counselors found: ${backendResponse.data.data.counselors.length}`);
    console.log(`   ✅ Sample counselor: ${backendResponse.data.data.counselors[0]?.userId?.firstName || 'N/A'}`);
  } catch (error) {
    console.log(`   ❌ Backend API Error: ${error.message}`);
  }
  
  // Test 2: Frontend Accessibility
  console.log('\n2. Testing Frontend Accessibility...');
  try {
    const frontendResponse = await new Promise((resolve, reject) => {
      const req = http.request('http://localhost:3000/counselors', (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => resolve({ status: res.statusCode, headers: res.headers }));
      });
      req.on('error', reject);
      req.end();
    });
    
    console.log(`   ✅ Frontend Status: ${frontendResponse.status}`);
    console.log(`   ✅ Content-Type: ${frontendResponse.headers['content-type']}`);
  } catch (error) {
    console.log(`   ❌ Frontend Error: ${error.message}`);
  }
  
  console.log('\n🎉 Test Complete! The counselors should be visible on http://localhost:3000/counselors');
}

testFullFlow();

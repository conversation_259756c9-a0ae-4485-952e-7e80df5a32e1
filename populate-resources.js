const mongoose = require("mongoose");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// Resource Schema (simplified for population)
const resourceSchema = new mongoose.Schema({
  title: String,
  description: String,
  content: String,
  type: {
    type: String,
    enum: ["article", "video", "audio", "tool", "worksheet", "guide"],
  },
  category: String,
  subcategory: String,
  tags: [String],
  difficulty: { type: String, enum: ["beginner", "intermediate", "advanced"] },
  estimatedReadTime: Number,
  estimatedDuration: Number,
  media: {
    thumbnailUrl: String,
    videoUrl: String,
    audioUrl: String,
    downloadUrl: String,
    fileSize: Number,
  },
  author: {
    name: String,
    credentials: String,
    bio: String,
    profilePicture: String,
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
  },
  seoData: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
  },
  accessibility: {
    hasTranscript: { type: Boolean, default: false },
    hasClosedCaptions: { type: Boolean, default: false },
    isScreenReaderFriendly: { type: Boolean, default: true },
    alternativeFormats: [String],
  },
  engagement: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
  },
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  publishedAt: Date,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Resource = mongoose.model("Resource", resourceSchema);

const testResources = [
  {
    title: "Understanding Anxiety: A Complete Guide",
    description:
      "Learn about anxiety disorders, their symptoms, and evidence-based treatment approaches.",
    content:
      "Anxiety is a normal human emotion that everyone experiences from time to time. However, when anxiety becomes persistent, excessive, and interferes with daily activities, it may be classified as an anxiety disorder...",
    type: "article",
    category: "anxiety-management",
    tags: ["anxiety", "mental-health", "coping-strategies"],
    difficulty: "beginner",
    estimatedReadTime: 10,
    author: {
      name: "Dr. Sarah Johnson",
      credentials: "Ph.D., Licensed Clinical Psychologist",
      bio: "Specializing in anxiety and mood disorders",
    },
    seo: {
      metaTitle: "Understanding Anxiety: Complete Guide | Theramea",
      metaDescription:
        "Learn about anxiety disorders, symptoms, and treatments.",
      keywords: ["anxiety", "mental health", "therapy"],
      slug: "understanding-anxiety-guide",
    },
    seoData: {
      metaTitle: "Understanding Anxiety: Complete Guide | Theramea",
      metaDescription:
        "Learn about anxiety disorders, symptoms, and treatments.",
      keywords: ["anxiety", "mental health", "therapy"],
      slug: "understanding-anxiety-guide",
    },
    engagement: {
      views: 1247,
      likes: 89,
      bookmarks: 45,
      averageRating: 4.8,
      totalRatings: 32,
    },
    isFeatured: true,
    publishedAt: new Date("2024-01-15"),
  },
  {
    title: "5-Minute Daily Mindfulness Practice",
    description:
      "A simple guided meditation to help you start your day with clarity and calm.",
    content:
      "Mindfulness is the practice of being fully present and engaged in the current moment...",
    type: "audio",
    category: "mindfulness-meditation",
    tags: ["mindfulness", "meditation", "daily-practice"],
    difficulty: "beginner",
    estimatedDuration: 5,
    author: {
      name: "Michael Chen",
      credentials: "LMFT, Mindfulness Instructor",
      bio: "Certified mindfulness teacher with 10+ years experience",
    },
    seo: {
      metaTitle: "5-Minute Daily Mindfulness Practice | Theramea",
      metaDescription:
        "Start your day with this simple 5-minute mindfulness meditation.",
      keywords: ["mindfulness", "meditation", "daily practice"],
      slug: "5-minute-mindfulness",
    },
    seoData: {
      metaTitle: "5-Minute Daily Mindfulness Practice | Theramea",
      metaDescription:
        "Start your day with this simple 5-minute mindfulness meditation.",
      keywords: ["mindfulness", "meditation", "daily practice"],
      slug: "5-minute-mindfulness",
    },
    engagement: {
      views: 892,
      likes: 156,
      bookmarks: 78,
      averageRating: 4.9,
      totalRatings: 67,
    },
    publishedAt: new Date("2024-01-20"),
  },
  {
    title: "CBT Thought Record Worksheet",
    description:
      "Practical CBT exercises to help identify and challenge negative thought patterns.",
    content:
      "Cognitive Behavioral Therapy (CBT) thought records are powerful tools for understanding the connection between thoughts, feelings, and behaviors...",
    type: "worksheet",
    category: "depression-support",
    tags: ["cbt", "worksheets", "thought-patterns", "depression"],
    difficulty: "intermediate",
    estimatedReadTime: 15,
    author: {
      name: "Dr. Emily Rodriguez",
      credentials: "Ph.D., CBT Specialist",
      bio: "Expert in cognitive behavioral therapy techniques",
    },
    seo: {
      metaTitle: "CBT Thought Record Worksheet | Theramea",
      metaDescription:
        "Download this CBT worksheet to track and challenge negative thoughts.",
      keywords: ["cbt", "worksheet", "thought record", "depression"],
      slug: "cbt-thought-record",
    },
    seoData: {
      metaTitle: "CBT Thought Record Worksheet | Theramea",
      metaDescription:
        "Download this CBT worksheet to track and challenge negative thoughts.",
      keywords: ["cbt", "worksheet", "thought record", "depression"],
      slug: "cbt-thought-record",
    },
    engagement: {
      views: 634,
      likes: 45,
      bookmarks: 89,
      averageRating: 4.7,
      totalRatings: 28,
    },
    publishedAt: new Date("2024-01-25"),
  },
  {
    title: "Building Healthy Relationships",
    description:
      "Video series on communication skills, boundaries, and maintaining healthy connections.",
    content:
      "Healthy relationships are the foundation of emotional well-being. This comprehensive guide covers essential skills for building and maintaining strong connections...",
    type: "video",
    category: "relationship-skills",
    tags: ["relationships", "communication", "boundaries"],
    difficulty: "intermediate",
    estimatedDuration: 25,
    author: {
      name: "James Thompson",
      credentials: "LPC, Couples Therapist",
      bio: "Specializing in relationship counseling and family therapy",
    },
    seo: {
      metaTitle: "Building Healthy Relationships | Theramea",
      metaDescription:
        "Learn essential skills for healthy relationships and communication.",
      keywords: ["relationships", "communication", "therapy"],
      slug: "healthy-relationships",
    },
    seoData: {
      metaTitle: "Building Healthy Relationships | Theramea",
      metaDescription:
        "Learn essential skills for healthy relationships and communication.",
      keywords: ["relationships", "communication", "therapy"],
      slug: "healthy-relationships",
    },
    engagement: {
      views: 1456,
      likes: 112,
      bookmarks: 67,
      averageRating: 4.6,
      totalRatings: 45,
    },
    publishedAt: new Date("2024-02-01"),
  },
  {
    title: "Self-Care Checklist for Mental Health",
    description:
      "A comprehensive guide to creating and maintaining effective self-care routines.",
    content:
      "Self-care is not selfish—it's essential. This comprehensive checklist will help you develop sustainable self-care practices...",
    type: "guide",
    category: "self-care",
    tags: ["self-care", "routine", "mental-health", "wellness"],
    difficulty: "beginner",
    estimatedReadTime: 8,
    author: {
      name: "Lisa Park",
      credentials: "MSW, Wellness Coach",
      bio: "Licensed social worker specializing in holistic wellness approaches",
    },
    seo: {
      metaTitle: "Self-Care Checklist for Mental Health | Theramea",
      metaDescription:
        "Complete guide to building effective self-care routines for better mental health.",
      keywords: ["self-care", "mental health", "wellness", "routine"],
      slug: "self-care-checklist",
    },
    seoData: {
      metaTitle: "Self-Care Checklist for Mental Health | Theramea",
      metaDescription:
        "Complete guide to building effective self-care routines for better mental health.",
      keywords: ["self-care", "mental health", "wellness", "routine"],
      slug: "self-care-checklist",
    },
    engagement: {
      views: 2134,
      likes: 189,
      bookmarks: 145,
      averageRating: 4.9,
      totalRatings: 78,
    },
    isFeatured: true,
    publishedAt: new Date("2024-02-05"),
  },
];

async function populateResources() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Clear existing resources
    await Resource.deleteMany({});
    console.log("🗑️ Cleared existing resources");

    // Create a dummy ObjectId for createdBy if no users exist
    const dummyUserId = new mongoose.Types.ObjectId();

    // Add createdBy to each resource
    const resourcesWithCreator = testResources.map((resource) => ({
      ...resource,
      createdBy: dummyUserId,
    }));

    // Create test resources
    console.log("📚 Creating test resources...");
    const createdResources = await Resource.insertMany(resourcesWithCreator);

    console.log(`✅ Created ${createdResources.length} resources:`);
    createdResources.forEach((resource, index) => {
      console.log(
        `  ${index + 1}. "${resource.title}" (ID: ${resource._id}, Slug: ${
          resource.seo.slug
        })`
      );
    });

    console.log("\n📋 Test URLs:");
    console.log(
      "- http://localhost:5000/api/resources/" + createdResources[0]._id
    );
    console.log(
      "- http://localhost:5000/api/resources/understanding-anxiety-guide"
    );
    console.log("- http://localhost:5000/api/resources/5-minute-mindfulness");
    console.log("- http://localhost:5000/api/resources/cbt-thought-record");
    console.log("- http://localhost:5000/api/resources/healthy-relationships");
    console.log("- http://localhost:5000/api/resources/self-care-checklist");
  } catch (error) {
    console.error("❌ Error populating resources:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

populateResources();

// Export all models from a central location
export { User, IUser } from "./User";
export { Counselor, ICounselor } from "./Counselor";
export { Session, ISession } from "./Session";
export { ChatRoom, IChatRoom } from "./ChatRoom";
export { Message, IMessage } from "./Message";
export { Resource, IResource } from "./Resource";
export { MoodEntry, IMoodEntry } from "./MoodEntry";
export {
  AdminAction,
  SystemSettings,
  Report,
  IAdminAction,
  ISystemSettings,
  IReport,
} from "./Admin";

// Model initialization function
import mongoose from "mongoose";
import { User } from "./User";
import { Counselor } from "./Counselor";
import { Session } from "./Session";
import { ChatRoom } from "./ChatRoom";
import { Message } from "./Message";
import { Resource } from "./Resource";
import { MoodEntry } from "./MoodEntry";
import { AdminAction, SystemSettings, Report } from "./Admin";

export const initializeModels = () => {
  // This function ensures all models are registered with mongoose
  // It's useful for ensuring proper model initialization order

  console.log("Initializing Theramea database models...");

  // Register all models
  const models = {
    User,
    Counselor,
    Session,
    ChatRoom,
    Message,
    Resource,
    MoodEntry,
    AdminAction,
    SystemSettings,
    Report,
  };

  // Verify all models are properly registered
  Object.entries(models).forEach(([name, model]) => {
    if (mongoose.models[name]) {
      console.log(`✓ Model ${name} already registered`);
    } else {
      console.log(`✓ Model ${name} registered successfully`);
    }
  });

  console.log("All models initialized successfully");
  return models;
};

// Database seeding utilities
export const seedDefaultData = async () => {
  try {
    console.log("Seeding default system data...");

    // Default system settings
    const defaultSettings = [
      {
        key: "platform_fee_percentage",
        value: 10,
        type: "number",
        description: "Platform fee percentage for sessions",
        category: "payment",
        isPublic: false,
      },
      {
        key: "max_session_duration",
        value: 180,
        type: "number",
        description: "Maximum session duration in minutes",
        category: "session",
        isPublic: true,
      },
      {
        key: "min_session_duration",
        value: 15,
        type: "number",
        description: "Minimum session duration in minutes",
        category: "session",
        isPublic: true,
      },
      {
        key: "chat_message_retention_days",
        value: 30,
        type: "number",
        description: "Number of days to retain chat messages",
        category: "chat",
        isPublic: false,
      },
      {
        key: "max_chat_participants",
        value: 50,
        type: "number",
        description: "Maximum participants per chat room",
        category: "chat",
        isPublic: true,
      },
      {
        key: "email_verification_required",
        value: true,
        type: "boolean",
        description: "Whether email verification is required for new users",
        category: "security",
        isPublic: true,
      },
      {
        key: "counselor_auto_approval",
        value: false,
        type: "boolean",
        description: "Whether counselor applications are auto-approved",
        category: "general",
        isPublic: false,
      },
      {
        key: "maintenance_mode",
        value: false,
        type: "boolean",
        description: "Whether the platform is in maintenance mode",
        category: "general",
        isPublic: true,
      },
    ];

    // Create super admin user if it doesn't exist
    const superAdminEmail =
      process.env.SUPER_ADMIN_EMAIL || "<EMAIL>";
    const existingSuperAdmin = await User.findOne({ email: superAdminEmail });

    if (!existingSuperAdmin) {
      const superAdmin = new User({
        firstName: "Super",
        lastName: "Admin",
        username: process.env.SUPER_ADMIN_USERNAME || "superadmin",
        email: superAdminEmail,
        password: process.env.SUPER_ADMIN_PASSWORD || "change-this-password",
        role: "superadmin",
        isEmailVerified: true,
        isActive: true,
      });

      await superAdmin.save();
      console.log("✓ Super admin user created");
    }

    // Seed system settings
    for (const setting of defaultSettings) {
      const existingSetting = await SystemSettings.findOne({
        key: setting.key,
      });
      if (!existingSetting) {
        const newSetting = new SystemSettings({
          ...setting,
          lastModifiedBy: (await User.findOne({ role: "superadmin" }))?._id,
        });
        await newSetting.save();
        console.log(`✓ System setting '${setting.key}' created`);
      }
    }

    // Create default chat rooms
    const defaultChatRooms = [
      {
        name: "General Support",
        description:
          "A safe space for general mental health support and encouragement",
        topic: "general-support",
        category: "support-groups",
        rules: [
          "Be respectful and kind to all participants",
          "No personal attacks or harassment",
          "Keep conversations supportive and constructive",
          "Respect privacy - no sharing personal information",
          "Follow community guidelines",
        ],
        tags: ["support", "community", "general"],
      },
      {
        name: "Anxiety & Depression Support",
        description:
          "Connect with others who understand anxiety and depression",
        topic: "anxiety-depression",
        category: "support-groups",
        rules: [
          "Share experiences and coping strategies",
          "Be supportive and non-judgmental",
          "No medical advice - seek professional help when needed",
          "Respect different experiences and perspectives",
        ],
        tags: ["anxiety", "depression", "mental-health"],
      },
      {
        name: "Student Life Support",
        description:
          "Support for students dealing with academic and social pressures",
        topic: "student-life",
        category: "support-groups",
        rules: [
          "Share study tips and stress management techniques",
          "Support each other through academic challenges",
          "Maintain academic integrity in discussions",
          "Be encouraging and positive",
        ],
        tags: ["students", "academic", "stress"],
      },
    ];

    const superAdmin = await User.findOne({ role: "superadmin" });
    if (superAdmin) {
      for (const roomData of defaultChatRooms) {
        const existingRoom = await ChatRoom.findOne({ name: roomData.name });
        if (!existingRoom) {
          const chatRoom = new ChatRoom({
            ...roomData,
            createdBy: superAdmin._id,
            moderators: [superAdmin._id],
          });
          await chatRoom.save();
          console.log(`✓ Default chat room '${roomData.name}' created`);
        }
      }
    }

    console.log("Default data seeding completed successfully");
  } catch (error) {
    console.error("Error seeding default data:", error);
    throw error;
  }
};

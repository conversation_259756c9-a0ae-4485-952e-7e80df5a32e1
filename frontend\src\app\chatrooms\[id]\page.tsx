"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import { chatAPI } from "@/lib/chat";
import { ChatRoom as ChatRoomType, CHAT_CATEGORIES } from "@/types/chat";
import Header from "@/components/layout/Header";
import AuthChoiceModal from "@/components/modals/AuthChoiceModal";
import ChatRoom from "@/components/chat/ChatRoom";

export default function ChatRoomPage() {
  const router = useRouter();
  const params = useParams();
  const roomId = params.id as string;

  const { isAuthenticated, isGuest, checkAuth, isLoading, tokens, guestToken } =
    useAuthStore();
  const [room, setRoom] = useState<ChatRoomType | null>(null);
  const [roomLoading, setRoomLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasJoined, setHasJoined] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);

  const token = tokens?.accessToken || guestToken;

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    // Redirect authenticated users to the chat interface
    if (isAuthenticated && !isLoading) {
      router.push("/chat");
      return;
    }

    // Enable preview mode for unauthenticated users
    if (!isAuthenticated && !isGuest && !isLoading) {
      setPreviewMode(true);
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  useEffect(() => {
    if (roomId && token) {
      fetchRoomData();
    }
  }, [roomId, token]);

  const fetchRoomData = async () => {
    try {
      setRoomLoading(true);
      const response = await chatAPI.getChatRoom(roomId, token ?? undefined);
      setRoom(response.data.chatRoom);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load room");
    } finally {
      setRoomLoading(false);
    }
  };

  const handleJoinRoom = async () => {
    if (!room) return;

    if (previewMode) {
      setShowAuthModal(true);
      return;
    }

    try {
      let displayName;

      if (isGuest && token) {
        // Extract guest display name from token
        try {
          const tokenPayload = JSON.parse(atob(token.split(".")[1]));
          displayName =
            tokenPayload.displayName || tokenPayload.guestDisplayName;
        } catch (e) {
          console.warn("Failed to decode guest token for display name:", e);
          displayName = ""; // Fallback to backend generation
        }
      } else {
        // Authenticated user
        const authUser = useAuthStore.getState().user;
        displayName = authUser
          ? `${authUser.firstName} ${authUser.lastName}`
          : "";
      }

      await chatAPI.joinChatRoom(roomId, displayName || "", token ?? undefined);
      setHasJoined(true);

      // Redirect to the chat interface
      router.push(`/chat/${roomId}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to join room");
    }
  };

  const handleAuthChoice = (choice: "guest" | "signup" | "login") => {
    setShowAuthModal(false);
    switch (choice) {
      case "guest":
        router.push("/guest/start");
        break;
      case "signup":
        router.push("/auth/signup");
        break;
      case "login":
        router.push("/auth/login");
        break;
    }
  };

  if (isLoading || roomLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated && !isGuest) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <svg
              className="w-12 h-12 text-red-400 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Error</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/chatrooms"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Chat Rooms
            </Link>
          </div>
        </main>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Room not found
            </h1>
            <p className="text-gray-600 mb-4">
              This chat room may have been removed or you don't have access.
            </p>
            <Link
              href="/chatrooms"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Chat Rooms
            </Link>
          </div>
        </main>
      </div>
    );
  }

  const categoryInfo = CHAT_CATEGORIES.find((c) => c.value === room.category);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link
                href="/chatrooms"
                className="text-gray-500 hover:text-gray-700"
              >
                Chat Rooms
              </Link>
            </li>
            <li>
              <svg
                className="flex-shrink-0 h-5 w-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </li>
            <li>
              <span className="text-gray-900 font-medium">{room.name}</span>
            </li>
          </ol>
        </nav>

        {/* Preview Mode Banner */}
        {previewMode && (
          <div className="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-200 rounded-full flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-purple-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-purple-900">
                    Preview Mode
                  </h3>
                  <p className="text-sm text-purple-700">
                    Join the conversation to participate in this chatroom!
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleAuthChoice("guest")}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Continue as Guest
                </button>
                <button
                  onClick={() => handleAuthChoice("signup")}
                  className="bg-white border border-purple-300 hover:bg-purple-50 text-purple-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Sign Up
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Room Info Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">
                  {room.name}
                </h1>
                {categoryInfo && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    {categoryInfo.icon} {categoryInfo.label}
                  </span>
                )}
              </div>

              <p className="text-gray-600 mb-4">{room.description}</p>

              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <span className="flex items-center">
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                    />
                  </svg>
                  {room.currentParticipants}/{room.maxParticipants} participants
                </span>

                {room.isModerated && (
                  <span className="flex items-center text-green-600">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Moderated
                  </span>
                )}

                {room.settings.allowAnonymous && (
                  <span className="flex items-center text-blue-600">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                    Guest Friendly
                  </span>
                )}
              </div>

              {/* Room Rules */}
              {room.rules.length > 0 && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h3 className="text-sm font-medium text-yellow-800 mb-2">
                    Room Rules:
                  </h3>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {room.rules.map((rule, index) => (
                      <li key={index}>• {rule}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Guest Notice */}
        {isGuest && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <svg
                className="h-5 w-5 text-blue-600 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="text-blue-800">
                You're joining as a guest.{" "}
                <Link href="/auth/signup" className="underline">
                  Create an account
                </Link>{" "}
                to save your chat history and access additional features.
              </p>
            </div>
          </div>
        )}

        {/* Chat Interface */}
        <div className="h-[600px]">
          {!hasJoined ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {previewMode
                    ? "Join to participate"
                    : "Join the conversation"}
                </h2>
                <p className="text-gray-600 mb-6">
                  {previewMode
                    ? "Sign up or continue as guest to participate in this chat room."
                    : "Click the button below to join this chat room and start participating."}
                </p>
                <button
                  onClick={handleJoinRoom}
                  className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 font-medium"
                >
                  {previewMode ? "Join Conversation" : "Join Chat Room"}
                </button>
              </div>
            </div>
          ) : (
            <ChatRoom roomId={roomId} />
          )}
        </div>
      </main>

      {/* Auth Choice Modal */}
      <AuthChoiceModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onChoice={handleAuthChoice}
      />
    </div>
  );
}

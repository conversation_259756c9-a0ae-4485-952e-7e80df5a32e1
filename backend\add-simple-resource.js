const mongoose = require("mongoose");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// Resource Schema (simplified for population)
const resourceSchema = new mongoose.Schema({
  title: String,
  description: String,
  content: String,
  type: {
    type: String,
    enum: ["article", "video", "audio", "tool", "worksheet", "guide"],
  },
  category: String,
  subcategory: String,
  tags: [String],
  difficulty: { type: String, enum: ["beginner", "intermediate", "advanced"] },
  estimatedReadTime: Number,
  estimatedDuration: Number,
  media: {
    thumbnailUrl: String,
    videoUrl: String,
    audioUrl: String,
    downloadUrl: String,
    fileSize: Number,
  },
  author: {
    name: String,
    credentials: String,
    bio: String,
    profilePicture: String,
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
  },
  seoData: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
  },
  accessibility: {
    hasTranscript: { type: Boolean, default: false },
    hasClosedCaptions: { type: Boolean, default: false },
    isScreenReaderFriendly: { type: Boolean, default: true },
    alternativeFormats: [String],
  },
  engagement: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
  },
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  publishedAt: Date,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Resource = mongoose.model("Resource", resourceSchema);

const simpleResource = {
  title: "Self-Care Checklist for Mental Health",
  description:
    "A comprehensive guide to creating and maintaining effective self-care routines.",
  content:
    "Self-care is not selfish. It's essential for maintaining good mental health and overall well-being...",
  type: "guide",
  category: "self-care",
  tags: ["self-care", "routine", "mental-health"],
  difficulty: "beginner",
  estimatedReadTime: 10,
  author: {
    name: "Lisa Park",
    credentials: "MSW, Wellness Coach",
    bio: "Licensed social worker specializing in holistic wellness approaches",
  },
  seo: {
    metaTitle: "Self-Care Checklist for Mental Health | Theramea",
    metaDescription:
      "Complete guide to building effective self-care routines for better mental health.",
    keywords: ["self-care", "mental health", "wellness", "routine"],
    slug: "5",
  },
  seoData: {
    metaTitle: "Self-Care Checklist for Mental Health | Theramea",
    metaDescription:
      "Complete guide to building effective self-care routines for better mental health.",
    keywords: ["self-care", "mental health", "wellness", "routine"],
    slug: "5",
  },
  engagement: {
    views: 3421,
    likes: 189,
    bookmarks: 145,
    averageRating: 4.9,
    totalRatings: 78,
  },
  isFeatured: true,
  publishedAt: new Date("2024-01-01"),
};

async function addSimpleResource() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Create a dummy ObjectId for createdBy if no users exist
    const dummyUserId = new mongoose.Types.ObjectId();

    // Add createdBy to the resource
    const resourceWithCreator = {
      ...simpleResource,
      createdBy: dummyUserId,
    };

    // Create the resource with slug "5"
    const createdResource = await Resource.create(resourceWithCreator);

    console.log(`✅ Created resource with slug "5":`);
    console.log(`  Title: "${createdResource.title}"`);
    console.log(`  ID: ${createdResource._id}`);
    console.log(`  Slug: ${createdResource.seo.slug}`);
    console.log(`  Creator: ${createdResource.createdBy}`);

    console.log("\n📋 Test URLs:");
    console.log("- http://localhost:5000/api/resources/5");
    console.log("- http://localhost:5000/api/resources/" + createdResource._id);
  } catch (error) {
    console.error("❌ Error adding resource:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

addSimpleResource();

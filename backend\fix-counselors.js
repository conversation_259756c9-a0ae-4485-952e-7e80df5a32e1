const mongoose = require("mongoose");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// User Schema
const userSchema = new mongoose.Schema({}, { strict: false });
const User = mongoose.model("User", userSchema, "users");

// Counselor Schema
const counselorSchema = new mongoose.Schema({}, { strict: false });
const Counselor = mongoose.model("Counselor", counselorSchema, "counselors");

// Default counselor profile data
const defaultCounselorProfiles = [
  {
    bio: "Experienced clinical psychologist specializing in anxiety and depression treatment with over 8 years of practice.",
    specializations: ["anxiety", "depression", "stress-management"],
    experience: {
      years: 8,
      description:
        "Clinical psychology practice with focus on cognitive behavioral therapy and mindfulness-based interventions.",
    },
    qualifications: [
      {
        degree: "Ph.D. in Clinical Psychology",
        institution: "University of Lagos",
        year: 2015,
      },
      {
        degree: "M.Sc. Psychology",
        institution: "University of Ibadan",
        year: 2012,
      },
    ],
    licenses: [
      {
        type: "Licensed Clinical Psychologist",
        number: "LCP2015001",
        issuingAuthority: "Nigeria Psychological Society",
        expiryDate: new Date("2025-12-31"),
      },
    ],
    profile: {
      languages: ["English", "Yoruba"],
      approachDescription:
        "I use evidence-based therapeutic approaches including CBT and mindfulness to help clients develop coping strategies.",
      sessionTypes: ["individual", "couples"],
    },
    pricing: {
      currency: "NGN",
      ratePerMinute: 150,
      minimumSessionDuration: 45,
    },
    verification: {
      status: "approved",
      submittedAt: new Date(),
      reviewedAt: new Date(),
      reviewedBy: "<EMAIL>",
    },
    settings: {
      acceptingNewClients: true,
      maxSessionsPerDay: 8,
      bufferTimeBetweenSessions: 15,
    },
    statistics: {
      totalSessions: 156,
      totalEarnings: 234000,
      averageRating: 4.8,
      totalReviews: 89,
      completionRate: 0.96,
      responseTime: 15,
    },
    isActive: true,
  },
  {
    bio: "Specializing in trauma therapy and family counseling with a focus on healing and resilience building.",
    specializations: ["trauma", "family-therapy", "PTSD"],
    experience: {
      years: 6,
      description:
        "Extensive experience in trauma-informed care and family systems therapy.",
    },
    qualifications: [
      {
        degree: "M.A. in Clinical Psychology",
        institution: "Ahmadu Bello University",
        year: 2018,
      },
    ],
    licenses: [
      {
        type: "Licensed Marriage and Family Therapist",
        number: "LMFT2018002",
        issuingAuthority: "Nigeria Psychological Society",
        expiryDate: new Date("2025-12-31"),
      },
    ],
    profile: {
      languages: ["English", "Hausa"],
      approachDescription:
        "I provide trauma-informed care using EMDR and narrative therapy techniques.",
      sessionTypes: ["individual", "family", "group"],
    },
    pricing: {
      currency: "NGN",
      ratePerMinute: 140,
      minimumSessionDuration: 50,
    },
    verification: {
      status: "approved",
      submittedAt: new Date(),
      reviewedAt: new Date(),
      reviewedBy: "<EMAIL>",
    },
    settings: {
      acceptingNewClients: true,
      maxSessionsPerDay: 6,
      bufferTimeBetweenSessions: 20,
    },
    statistics: {
      totalSessions: 98,
      totalEarnings: 137200,
      averageRating: 4.7,
      totalReviews: 56,
      completionRate: 0.94,
      responseTime: 12,
    },
    isActive: true,
  },
  {
    bio: "Child and adolescent psychologist with expertise in developmental and behavioral issues.",
    specializations: [
      "child-psychology",
      "adolescent-therapy",
      "behavioral-issues",
    ],
    experience: {
      years: 5,
      description:
        "Specialized training in child development and play therapy techniques.",
    },
    qualifications: [
      {
        degree: "M.Sc. in Child Psychology",
        institution: "University of Port Harcourt",
        year: 2019,
      },
    ],
    licenses: [
      {
        type: "Licensed Child Psychologist",
        number: "LCP2019003",
        issuingAuthority: "Nigeria Psychological Society",
        expiryDate: new Date("2025-12-31"),
      },
    ],
    profile: {
      languages: ["English", "Igbo"],
      approachDescription:
        "I work with children and teens using play therapy and family-based interventions.",
      sessionTypes: ["individual", "family"],
    },
    pricing: {
      currency: "NGN",
      ratePerMinute: 130,
      minimumSessionDuration: 45,
    },
    verification: {
      status: "approved",
      submittedAt: new Date(),
      reviewedAt: new Date(),
      reviewedBy: "<EMAIL>",
    },
    settings: {
      acceptingNewClients: true,
      maxSessionsPerDay: 7,
      bufferTimeBetweenSessions: 15,
    },
    statistics: {
      totalSessions: 72,
      totalEarnings: 93600,
      averageRating: 4.9,
      totalReviews: 41,
      completionRate: 0.97,
      responseTime: 18,
    },
    isActive: true,
  },
];

async function createCounselorProfiles() {
  try {
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Get all users with counselor role
    const counselorUsers = await User.find({ role: "counselor" }).lean();
    console.log(`\n👥 Found ${counselorUsers.length} counselor users`);

    if (counselorUsers.length === 0) {
      console.log("❌ No counselor users found. Please create users first.");
      return;
    }

    // Check existing counselor profiles
    const existingProfiles = await Counselor.countDocuments();
    console.log(`📋 Existing counselor profiles: ${existingProfiles}`);

    let created = 0;
    for (let i = 0; i < counselorUsers.length; i++) {
      const user = counselorUsers[i];

      // Check if profile already exists
      const existingProfile = await Counselor.findOne({ userId: user._id });
      if (existingProfile) {
        console.log(
          `⚠️  Profile already exists for: ${user.firstName} ${user.lastName}`
        );
        continue;
      }

      // Create profile with default data
      const profileData =
        defaultCounselorProfiles[i % defaultCounselorProfiles.length];
      const counselorProfile = new Counselor({
        ...profileData,
        userId: user._id,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await counselorProfile.save();
      console.log(`✅ Created profile for: ${user.firstName} ${user.lastName}`);
      created++;
    }

    // Verify results
    const totalProfiles = await Counselor.countDocuments();
    console.log(`\n🎉 Profile creation completed!`);
    console.log(`📊 Created: ${created} new profiles`);
    console.log(`📊 Total profiles: ${totalProfiles}`);

    // Test the API endpoint
    console.log(`\n🧪 Testing counselors API...`);
    const approvedProfiles = await Counselor.countDocuments({
      "verification.status": "approved",
      isActive: true,
    });
    console.log(`✅ Approved active profiles: ${approvedProfiles}`);
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

createCounselorProfiles();

"use client";

import { useEffect, useState } from "react";
import { useHydratedAuth } from "@/hooks/useHydratedAuth";
import TestingChecklist from "@/components/testing/TestingChecklist";
import Header from "@/components/layout/Header";

export default function TestingPage() {
  const { user, checkAuth, isReady } = useHydratedAuth();
  const [showChecklist, setShowChecklist] = useState(false);

  useEffect(() => {
    if (isReady) {
      checkAuth();
    }
  }, [checkAuth, isReady]);

  // Only show to admin users or in development
  const canAccessTesting =
    user?.role === "admin" || process.env.NODE_ENV === "development";

  if (!isReady) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (!canAccessTesting) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <svg
              className="w-16 h-16 text-gray-400 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Access Restricted
            </h1>
            <p className="text-gray-600">
              This testing page is only available to administrators.
            </p>
          </div>
        </main>
      </div>
    );
  }

  if (!showChecklist) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-purple-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                🧪 Theramea Testing Suite
              </h1>
              <p className="text-gray-600">
                Comprehensive testing tools for the Theramea platform
              </p>
            </div>

            {/* System Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <svg
                      className="w-4 h-4 text-green-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-green-900">Frontend</p>
                    <p className="text-sm text-green-700">
                      Running on localhost:3000
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <svg
                      className="w-4 h-4 text-green-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-green-900">Backend</p>
                    <p className="text-sm text-green-700">API responding</p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg
                      className="w-4 h-4 text-blue-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-blue-900">Database</p>
                    <p className="text-sm text-blue-700">MongoDB connected</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Testing Tools */}
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Available Testing Tools
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <button
                    onClick={() => setShowChecklist(true)}
                    className="p-6 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors text-left"
                  >
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg
                          className="w-5 h-5 text-purple-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <h3 className="font-medium text-gray-900">
                        Testing Checklist
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600">
                      Interactive checklist to track testing progress across all
                      platform features
                    </p>
                  </button>

                  <div className="p-6 border border-gray-200 rounded-lg bg-gray-50">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <svg
                          className="w-5 h-5 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 10V3L4 14h7v7l9-11h-7z"
                          />
                        </svg>
                      </div>
                      <h3 className="font-medium text-gray-500">
                        Automated Tests
                      </h3>
                    </div>
                    <p className="text-sm text-gray-500">
                      Coming soon: Unit tests, integration tests, and E2E
                      testing suite
                    </p>
                  </div>
                </div>
              </div>

              {/* Test Accounts */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Test Accounts
                </h2>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Regular User
                      </h4>
                      <p className="text-gray-600">Email: <EMAIL></p>
                      <p className="text-gray-600">Password: password123</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Counselor
                      </h4>
                      <p className="text-gray-600">Email: <EMAIL></p>
                      <p className="text-gray-600">Password: password123</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Admin</h4>
                      <p className="text-gray-600">Email: <EMAIL></p>
                      <p className="text-gray-600">Password: password123</p>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-800">
                      <strong>Note:</strong> Run the test data creation script
                      first if these accounts don't exist.
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Quick Links
                </h2>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <a
                    href="/auth/login"
                    className="p-3 text-center border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                  >
                    <div className="text-sm font-medium text-gray-900">
                      Login
                    </div>
                  </a>

                  <a
                    href="/counselors"
                    className="p-3 text-center border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                  >
                    <div className="text-sm font-medium text-gray-900">
                      Counselors
                    </div>
                  </a>

                  <a
                    href="/chatrooms"
                    className="p-3 text-center border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                  >
                    <div className="text-sm font-medium text-gray-900">
                      Chatrooms
                    </div>
                  </a>

                  <a
                    href="/resources"
                    className="p-3 text-center border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                  >
                    <div className="text-sm font-medium text-gray-900">
                      Resources
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main>
        <TestingChecklist />
      </main>
    </div>
  );
}

const mongoose = require("mongoose");

// MongoDB connection URI from environment variables or default
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "****************************************************************";

async function checkUsers() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB successfully");

    // Get the user who owns the session
    const sessionUserId = "687fe3b739da96aef19699d3";
    const sessionUser = await mongoose.connection.db
      .collection("users")
      .findOne({
        _id: new mongoose.Types.ObjectId(sessionUserId),
      });

    if (sessionUser) {
      console.log("\n=== Session Owner Details ===");
      console.log(`Name: ${sessionUser.firstName} ${sessionUser.lastName}`);
      console.log(`Email: ${sessionUser.email}`);
      console.log(`Role: ${sessionUser.role}`);
      console.log(
        `Account Status: ${sessionUser.isActive ? "Active" : "Inactive"}`
      );
      console.log(
        `Email Verified: ${sessionUser.isEmailVerified ? "Yes" : "No"}`
      );
      console.log(`Password Hash: ${sessionUser.password ? "Set" : "Not set"}`);
    } else {
      console.log("❌ Session owner not found");
    }

    // Get all users to see who we can test with
    console.log("\n=== All Users in Database ===");
    const users = await mongoose.connection.db
      .collection("users")
      .find({})
      .toArray();
    users.forEach((user, index) => {
      console.log(
        `${index + 1}. ${user.firstName} ${user.lastName} (${user.email})`
      );
      console.log(`   ID: ${user._id}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Active: ${user.isActive ? "Yes" : "No"}`);
      console.log(`   Password: ${user.password ? "Set" : "Not set"}`);
      console.log("");
    });
  } catch (error) {
    console.error("❌ Database connection error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

checkUsers();

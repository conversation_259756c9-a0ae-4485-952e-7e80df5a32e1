"use client";

import React, { useState } from "react";
import { Session } from "@/types/booking";
import { PaymentUtils } from "@/lib/payment";

interface BookingPaymentSummaryProps {
  session: Session | null;
  loading?: boolean;
}

export default function BookingPaymentSummary({
  session,
  loading = false,
}: BookingPaymentSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <p className="text-gray-500 text-center">No session data available</p>
      </div>
    );
  }

  const formatCurrency = (
    amount: number | undefined,
    currency: string = "USD"
  ) => {
    // Handle undefined or invalid amounts
    if (amount === undefined || amount === null || isNaN(amount)) {
      return "Amount not available";
    }

    try {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency || "USD",
      }).format(amount);
    } catch (error) {
      console.error("Currency formatting error:", error);
      return `${currency || "USD"} ${amount || 0}`;
    }
  };

  const formatDateTime = (dateString: string | undefined) => {
    if (!dateString) {
      return "Date not available";
    }

    try {
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return "Date not available";
      }

      return date.toLocaleString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Date formatting error:", error, "for input:", dateString);
      return "Date not available";
    }
  };

  const getSessionDateDisplay = () => {
    if (session.scheduledAt) {
      return {
        date: formatDateTime(session.scheduledAt),
        label: "Session Date & Time:",
      };
    } else if (session.createdAt) {
      return {
        date: formatDateTime(session.createdAt),
        label: "Booking Created:",
      };
    } else {
      return {
        date: "Date not available",
        label: "Session Date & Time:",
      };
    }
  };

  // Helper function to get session base amount with fallback
  const getSessionBaseAmount = () => {
    // Try to get baseAmount first
    if (session.pricing.baseAmount && !isNaN(session.pricing.baseAmount)) {
      return session.pricing.baseAmount;
    }

    // Fallback: calculate from total amount minus platform fee
    if (session.pricing.totalAmount && session.pricing.platformFee) {
      const calculatedBase =
        session.pricing.totalAmount - session.pricing.platformFee;
      if (!isNaN(calculatedBase) && calculatedBase > 0) {
        return calculatedBase;
      }
    }

    // Last fallback: use totalAmount if available
    if (session.pricing.totalAmount && !isNaN(session.pricing.totalAmount)) {
      return session.pricing.totalAmount;
    }

    return 0;
  };

  const sessionBaseAmount = getSessionBaseAmount();
  const urgentFee =
    session.isUrgent && sessionBaseAmount > 0 ? sessionBaseAmount * 0.2 : 0;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Payment Summary</h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-sm text-purple-600 hover:text-purple-700"
        >
          {isExpanded ? "Show Less" : "Show Details"}
        </button>
      </div>

      {/* Session Details */}
      <div className="space-y-3 mb-6">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">{getSessionDateDisplay().label}</span>
          <span className="text-gray-900">
            {getSessionDateDisplay().date}
            {!session.scheduledAt && session.createdAt && (
              <span className="text-xs text-gray-500 block">
                (Session time to be scheduled)
              </span>
            )}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Duration:</span>
          <span className="text-gray-900">{session.duration} minutes</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Session Type:</span>
          <span className="text-gray-900 capitalize">{session.type}</span>
        </div>
        {session.counselor?.user && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Counselor:</span>
            <span className="text-gray-900">
              {session.counselor.user.firstName}{" "}
              {session.counselor.user.lastName}
            </span>
          </div>
        )}
      </div>

      {/* Pricing Breakdown */}
      <div className="space-y-3 pb-4 border-b border-gray-200">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Session Fee:</span>
          <span className="text-gray-900">
            {formatCurrency(sessionBaseAmount, session.pricing.currency)}
          </span>
        </div>

        {session.isUrgent && urgentFee > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Urgent Booking Fee (20%):</span>
            <span className="text-gray-900">
              {formatCurrency(urgentFee, session.pricing.currency)}
            </span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Platform Fee:</span>
          <span className="text-gray-900">
            {formatCurrency(
              session.pricing.platformFee,
              session.pricing.currency
            )}
          </span>
        </div>

        {isExpanded && (
          <>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal:</span>
              <span className="text-gray-900">
                {formatCurrency(
                  sessionBaseAmount + urgentFee,
                  session.pricing.currency
                )}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Processing Fee:</span>
              <span className="text-gray-900">
                {formatCurrency(
                  session.pricing.platformFee,
                  session.pricing.currency
                )}
              </span>
            </div>
          </>
        )}
      </div>

      {/* Total */}
      <div className="pt-4">
        <div className="flex justify-between text-lg font-semibold">
          <span className="text-gray-900">Total:</span>
          <span className="text-purple-600">
            {formatCurrency(
              session.pricing.totalAmount,
              session.pricing.currency
            )}
          </span>
        </div>
      </div>

      {/* Additional Info */}
      {isExpanded && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 space-y-1">
            <p>• Payment will be processed securely</p>
            <p>• You will receive a confirmation email</p>
            <p>• Cancellation policy applies</p>
            {session.isUrgent && (
              <p>• Urgent booking fees are non-refundable</p>
            )}
          </div>
        </div>
      )}

      {/* Session Status */}
      {session.status && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${
                session.status === "completed"
                  ? "bg-green-100 text-green-800"
                  : session.status === "scheduled"
                  ? "bg-blue-100 text-blue-800"
                  : session.status === "cancelled"
                  ? "bg-red-100 text-red-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {PaymentUtils.formatStatusText(session.status)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

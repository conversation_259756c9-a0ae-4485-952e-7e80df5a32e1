"use client";

import { useState, useEffect } from "react";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  PaintBrushIcon,
  Cog6ToothIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";

export default function SettingsPage() {
  const { user } = useAuthStore();
  const [activeTheme, setActiveTheme] = useState<"light" | "dark" | "system">(
    "system"
  );
  const [accentColor, setAccentColor] = useState<string>("purple");
  const [fontSize, setFontSize] = useState<"small" | "medium" | "large">(
    "medium"
  );
  const [reducedMotion, setReducedMotion] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  // Load saved settings from localStorage
  useEffect(() => {
    const savedTheme =
      (localStorage.getItem("theme") as "light" | "dark" | "system") ||
      "system";
    const savedAccentColor = localStorage.getItem("accentColor") || "purple";
    const savedFontSize =
      (localStorage.getItem("fontSize") as "small" | "medium" | "large") ||
      "medium";
    const savedReducedMotion = localStorage.getItem("reducedMotion") === "true";

    setActiveTheme(savedTheme);
    setAccentColor(savedAccentColor);
    setFontSize(savedFontSize);
    setReducedMotion(savedReducedMotion);

    // Apply theme
    applyTheme(savedTheme);
    applyFontSize(savedFontSize);
    applyReducedMotion(savedReducedMotion);
  }, []);

  const applyTheme = (theme: "light" | "dark" | "system") => {
    const root = document.documentElement;

    if (theme === "system") {
      // Remove any manually set theme, let CSS handle it
      root.classList.remove("light", "dark");
      root.style.colorScheme = "";
    } else if (theme === "dark") {
      root.classList.remove("light");
      root.classList.add("dark");
      root.style.colorScheme = "dark";
    } else {
      root.classList.remove("dark");
      root.classList.add("light");
      root.style.colorScheme = "light";
    }
  };

  const applyFontSize = (size: "small" | "medium" | "large") => {
    const root = document.documentElement;
    root.classList.remove("text-sm", "text-base", "text-lg");

    switch (size) {
      case "small":
        root.classList.add("text-sm");
        break;
      case "large":
        root.classList.add("text-lg");
        break;
      default:
        root.classList.add("text-base");
    }
  };

  const applyReducedMotion = (reduced: boolean) => {
    const root = document.documentElement;
    if (reduced) {
      root.style.setProperty("--tw-animate-duration", "0s");
      root.style.setProperty("--tw-transition-duration", "0s");
    } else {
      root.style.removeProperty("--tw-animate-duration");
      root.style.removeProperty("--tw-transition-duration");
    }
  };

  const handleThemeChange = (theme: "light" | "dark" | "system") => {
    setActiveTheme(theme);
    localStorage.setItem("theme", theme);
    applyTheme(theme);
  };

  const handleAccentColorChange = (color: string) => {
    setAccentColor(color);
    localStorage.setItem("accentColor", color);
    // You can implement accent color changes here
  };

  const handleFontSizeChange = (size: "small" | "medium" | "large") => {
    setFontSize(size);
    localStorage.setItem("fontSize", size);
    applyFontSize(size);
  };

  const handleReducedMotionChange = () => {
    const newValue = !reducedMotion;
    setReducedMotion(newValue);
    localStorage.setItem("reducedMotion", newValue.toString());
    applyReducedMotion(newValue);
  };

  const saveSettings = async () => {
    setIsSubmitting(true);
    setMessage(null);

    try {
      // Save to backend if user is logged in
      if (user) {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/users/app-settings`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
            },
            body: JSON.stringify({
              theme: activeTheme,
              accentColor,
              fontSize,
              reducedMotion,
            }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to save settings");
        }
      }

      setMessage({ type: "success", text: "Settings saved successfully!" });
    } catch (error) {
      setMessage({
        type: "error",
        text: "Failed to save settings. Changes are saved locally.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetToDefaults = () => {
    if (!confirm("Reset all settings to default values?")) return;

    // Reset to defaults
    setActiveTheme("system");
    setAccentColor("purple");
    setFontSize("medium");
    setReducedMotion(false);

    // Clear localStorage
    localStorage.removeItem("theme");
    localStorage.removeItem("accentColor");
    localStorage.removeItem("fontSize");
    localStorage.removeItem("reducedMotion");

    // Apply defaults
    applyTheme("system");
    applyFontSize("medium");
    applyReducedMotion(false);

    setMessage({ type: "success", text: "Settings reset to defaults!" });
  };

  const themeOptions = [
    {
      id: "light",
      name: "Light",
      description: "Light theme for daytime use",
      icon: SunIcon,
    },
    {
      id: "dark",
      name: "Dark",
      description: "Dark theme for nighttime use",
      icon: MoonIcon,
    },
    {
      id: "system",
      name: "System",
      description: "Follow your device settings",
      icon: ComputerDesktopIcon,
    },
  ];

  const accentColors = [
    { id: "purple", name: "Purple", color: "bg-purple-500" },
    { id: "blue", name: "Blue", color: "bg-blue-500" },
    { id: "green", name: "Green", color: "bg-green-500" },
    { id: "pink", name: "Pink", color: "bg-pink-500" },
    { id: "indigo", name: "Indigo", color: "bg-indigo-500" },
  ];

  const fontSizes = [
    {
      id: "small",
      name: "Small",
      description: "Compact text for more content",
    },
    {
      id: "medium",
      name: "Medium",
      description: "Standard comfortable reading",
    },
    {
      id: "large",
      name: "Large",
      description: "Larger text for better accessibility",
    },
  ];

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
            <p className="text-gray-600">Loading settings...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
      <Header />

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Cog6ToothIcon className="h-8 w-8 text-purple-600" />
            <h1 className="text-4xl font-bold text-gray-900">Settings</h1>
          </div>
          <p className="text-lg text-gray-600">
            Customize your application experience and preferences
          </p>
        </div>

        {/* Message */}
        {message && (
          <div
            className={`rounded-md p-4 mb-6 ${
              message.type === "success"
                ? "bg-green-50 text-green-700 border border-green-200"
                : "bg-red-50 text-red-700 border border-red-200"
            }`}
          >
            {message.text}
          </div>
        )}

        <div className="space-y-8">
          {/* Theme Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <PaintBrushIcon className="h-6 w-6 text-purple-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Appearance
              </h2>
            </div>

            {/* Theme Selection */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Theme</h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {themeOptions.map((theme) => {
                  const Icon = theme.icon;
                  return (
                    <button
                      key={theme.id}
                      onClick={() => handleThemeChange(theme.id as any)}
                      className={`relative p-4 rounded-lg border-2 transition-colors ${
                        activeTheme === theme.id
                          ? "border-purple-500 bg-purple-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      {activeTheme === theme.id && (
                        <CheckIcon className="absolute top-2 right-2 h-5 w-5 text-purple-600" />
                      )}
                      <Icon className="h-8 w-8 text-gray-600 mb-2" />
                      <div className="text-left">
                        <div className="font-medium text-gray-900">
                          {theme.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {theme.description}
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Accent Color */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Accent Color
              </h3>
              <div className="flex space-x-3">
                {accentColors.map((color) => (
                  <button
                    key={color.id}
                    onClick={() => handleAccentColorChange(color.id)}
                    className={`relative w-12 h-12 rounded-full ${
                      color.color
                    } transition-transform ${
                      accentColor === color.id
                        ? "ring-2 ring-offset-2 ring-gray-400 scale-110"
                        : "hover:scale-105"
                    }`}
                    title={color.name}
                  >
                    {accentColor === color.id && (
                      <CheckIcon className="absolute inset-0 m-auto h-6 w-6 text-white" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Font Size */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Font Size
              </h3>
              <div className="space-y-2">
                {fontSizes.map((size) => (
                  <label
                    key={size.id}
                    className="flex items-center space-x-3 cursor-pointer"
                  >
                    <input
                      type="radio"
                      name="fontSize"
                      value={size.id}
                      checked={fontSize === size.id}
                      onChange={() => handleFontSizeChange(size.id as any)}
                      className="w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"
                    />
                    <div>
                      <div className="font-medium text-gray-900">
                        {size.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {size.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Accessibility */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Accessibility
              </h3>
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Reduce Motion
                  </label>
                  <p className="text-sm text-gray-500">
                    Minimize animations and transitions
                  </p>
                </div>
                <button
                  type="button"
                  onClick={handleReducedMotionChange}
                  className={`${
                    reducedMotion ? "bg-purple-600" : "bg-gray-200"
                  } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
                >
                  <span
                    className={`${
                      reducedMotion ? "translate-x-5" : "translate-x-0"
                    } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <button
              onClick={resetToDefaults}
              disabled={isSubmitting}
              className="text-red-600 hover:text-red-700 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Reset to Defaults
            </button>
            <button
              onClick={saveSettings}
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Saving..." : "Save Settings"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

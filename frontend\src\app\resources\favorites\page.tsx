"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Header from "@/components/layout/Header";
import ResourceCard from "@/components/resources/ResourceCard";
import SignupPromptModal from "@/components/modals/SignupPromptModal";
import { useAuthStore } from "@/store/authStore";
import { resourcesAPI } from "@/lib/resources";
import { Resource } from "@/types/resources";

export default function FavoritesPage() {
  const {
    tokens,
    isAuthenticated,
    isGuest,
    isLoading: authLoading,
  } = useAuthStore();
  const [favorites, setFavorites] = useState<Resource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalFavorites, setTotalFavorites] = useState(0);
  const router = useRouter();

  useEffect(() => {
    // Wait for auth to be determined
    if (authLoading) {
      setIsLoading(true);
      return;
    }

    // Check authentication status
    if (!isAuthenticated || isGuest) {
      setIsLoading(false);
      setShowSignupModal(true);
      return;
    }

    // User is authenticated, fetch favorites
    fetchFavorites();
  }, [isAuthenticated, isGuest, authLoading, currentPage]);

  const fetchFavorites = async () => {
    if (!tokens?.accessToken) return;

    try {
      setIsLoading(true);
      const response = await resourcesAPI.getUserFavorites(
        tokens.accessToken,
        currentPage,
        12 // 12 per page for nice grid layout
      );

      if (response.success) {
        setFavorites(response.data.content);
        setTotalPages(response.data.pagination.pages);
        setTotalFavorites(response.data.pagination.total);
      }
    } catch (error) {
      console.error("Error fetching favorites:", error);
      setFavorites([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveFavorite = async (resourceId: string) => {
    if (!tokens?.accessToken) return;

    try {
      await resourcesAPI.unbookmarkResource(resourceId, tokens.accessToken);
      // Remove from local state
      setFavorites((prev) => prev.filter((fav) => fav._id !== resourceId));
      setTotalFavorites((prev) => prev - 1);
    } catch (error) {
      console.error("Error removing favorite:", error);
    }
  };

  const handleSignupModalClose = () => {
    setShowSignupModal(false);
    router.push("/resources"); // Redirect to main resources page
  };

  // Show signup modal for unauthenticated users
  if (!authLoading && (!isAuthenticated || isGuest)) {
    return (
      <>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="text-6xl mb-4">🔒</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Authentication Required
              </h2>
              <p className="text-gray-600 mb-4">
                Please sign up or log in to view your favorites.
              </p>
            </div>
          </div>
        </div>

        <SignupPromptModal
          isOpen={showSignupModal}
          onClose={handleSignupModalClose}
          feature="bookmark"
        />
      </>
    );
  }

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
            >
              <svg
                className="w-5 h-5 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Favorites</h1>
              <p className="text-gray-600">
                Resources you've saved for easy access
              </p>
            </div>
          </div>

          {/* Stats */}
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <span className="flex items-center space-x-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span>{totalFavorites} favorites</span>
            </span>
            <span>•</span>
            <span>Updated {new Date().toLocaleDateString()}</span>
          </div>
        </div>

        {/* Content */}
        {favorites.length > 0 ? (
          <>
            {/* Resources Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {favorites.map((resource) => (
                <ResourceCard
                  key={resource._id}
                  resource={resource}
                  onBookmark={(resourceId, isBookmarked) => {
                    if (!isBookmarked) {
                      handleRemoveFavorite(resourceId);
                    }
                  }}
                  onLike={(resourceId, isLiked) => {
                    console.log(
                      `Resource ${resourceId} ${isLiked ? "liked" : "unliked"}`
                    );
                  }}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>

                <span className="px-4 py-2 text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            )}
          </>
        ) : (
          /* Empty State */
          <div className="text-center py-16">
            <div className="text-6xl mb-4">💔</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No favorites yet
            </h3>
            <p className="text-gray-600 mb-6">
              Start exploring resources and save your favorites for easy access
              later.
            </p>
            <button
              onClick={() => router.push("/resources")}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Browse Resources
            </button>
          </div>
        )}
      </main>
    </div>
  );
}

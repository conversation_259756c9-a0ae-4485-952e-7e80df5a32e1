"use client";

import React, { useState } from "react";
import { formatStatusText } from "@/lib/utils";
import { AdminCounselor } from "@/types/admin";

interface CounselorCardProps {
  counselor: AdminCounselor;
  onAction: (counselorId: string, action: string, reason?: string) => void;
}

export default function CounselorCard({
  counselor,
  onAction,
}: CounselorCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 font-medium text-lg">
                {counselor.user?.firstName?.[0]}
                {counselor.user?.lastName?.[0]}
              </span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {counselor.user?.firstName} {counselor.user?.lastName}
              </h3>
              <p className="text-sm text-gray-500">{counselor.user?.email}</p>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>📚 {counselor.specializations?.join(", ") || "N/A"}</span>
              <span>
                ⭐ {counselor.statistics?.averageRating?.toFixed(1) || "N/A"}
              </span>
              <span>� {counselor.statistics?.totalSessions || 0} sessions</span>
            </div>
          </div>

          {isExpanded && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <p className="text-sm text-gray-700 mb-3">{counselor.bio}</p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Total Sessions:</span>{" "}
                  {counselor.statistics?.totalSessions || 0}
                </div>
                <div>
                  <span className="font-medium">Earnings:</span> $
                  {counselor.statistics?.totalEarnings || 0}
                </div>
                <div>
                  <span className="font-medium">Completion Rate:</span>{" "}
                  {counselor.statistics?.completionRate?.toFixed(1) || "N/A"}%
                </div>
                <div>
                  <span className="font-medium">Reviews:</span>{" "}
                  {counselor.statistics?.totalReviews || 0}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col items-end space-y-3">
          <span
            className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
              counselor.verification.status
            )}`}
          >
            {formatStatusText(counselor.verification.status)}
          </span>

          <div className="flex space-x-2">
            {counselor.verification.status === "pending" && (
              <>
                <button
                  onClick={() => onAction(counselor._id, "approve")}
                  className="px-3 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  Approve
                </button>
                <button
                  onClick={() => onAction(counselor._id, "reject")}
                  className="px-3 py-1 text-xs bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Reject
                </button>
              </>
            )}

            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              {isExpanded ? "Less" : "More"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

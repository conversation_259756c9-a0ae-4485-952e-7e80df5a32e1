import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import {
  Shield,
  Lock,
  FileText,
  Users,
  Check,
  AlertCircle,
  Heart,
  Clock,
} from "lucide-react";

const complianceFeatures = [
  {
    icon: Lock,
    title: "End-to-End Encryption",
    description:
      "All communications are encrypted in transit and at rest using industry-standard AES-256 encryption.",
  },
  {
    icon: Shield,
    title: "Access Controls",
    description:
      "Role-based access controls ensure only authorized personnel can access your information.",
  },
  {
    icon: FileText,
    title: "Audit Trails",
    description:
      "Complete audit logs of all data access and modifications are maintained and monitored.",
  },
  {
    icon: Users,
    title: "Staff Training",
    description:
      "All employees receive comprehensive HIPAA training and sign confidentiality agreements.",
  },
];

const patientRights = [
  "Right to access your protected health information",
  "Right to request corrections to your health information",
  "Right to receive a notice of privacy practices",
  "Right to request restrictions on use and disclosure",
  "Right to request confidential communications",
  "Right to file a complaint if you believe your privacy rights have been violated",
];

const securityMeasures = [
  "Multi-factor authentication for all accounts",
  "Regular security assessments and penetration testing",
  "Encrypted data storage in HIPAA-compliant data centers",
  "Automatic session timeouts for inactive users",
  "Secure video conferencing with end-to-end encryption",
  "Regular software updates and security patches",
];

export default function HIPAAPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      {/* Hero Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <Shield className="h-16 w-16 text-green-600 mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            HIPAA Compliance
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Your health information is protected under the highest standards of
            healthcare privacy and security regulations.
          </p>
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
            <Check className="h-5 w-5" />
            <span className="font-semibold">HIPAA Compliant Platform</span>
          </div>
        </div>
      </section>

      {/* What is HIPAA Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Understanding HIPAA Protection
            </h2>

            <div className="bg-white rounded-lg shadow-md p-8 mb-12">
              <div className="flex items-start gap-4 mb-6">
                <FileText className="h-8 w-8 text-blue-600 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-2xl font-bold mb-4 text-gray-800">
                    What is HIPAA?
                  </h3>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    The Health Insurance Portability and Accountability Act
                    (HIPAA) is a federal law that protects your protected health
                    information (PHI). It ensures that your medical and mental
                    health information remains private and secure.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                <div>
                  <h4 className="text-xl font-semibold mb-4 text-gray-800">
                    Protected Health Information Includes:
                  </h4>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Therapy session notes and recordings</li>
                    <li>• Mental health assessments and diagnoses</li>
                    <li>• Treatment plans and progress notes</li>
                    <li>• Prescription and medication information</li>
                    <li>• Appointment and scheduling details</li>
                    <li>• Payment and billing information</li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-xl font-semibold mb-4 text-gray-800">
                    Our HIPAA Obligations:
                  </h4>
                  <ul className="space-y-2 text-gray-600">
                    <li>
                      • Protect your health information from unauthorized access
                    </li>
                    <li>
                      • Use information only for treatment, payment, and
                      operations
                    </li>
                    <li>• Obtain your consent before sharing information</li>
                    <li>• Provide you with access to your own information</li>
                    <li>• Maintain comprehensive audit trails</li>
                    <li>• Report any potential data breaches</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance Features */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Our HIPAA Compliance Measures
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {complianceFeatures.map((feature, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <feature.icon className="h-12 w-12 text-green-600 mb-4" />
                  <h3 className="text-xl font-bold mb-3 text-gray-800">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Patient Rights */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Your HIPAA Rights
            </h2>

            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="flex items-center gap-3 mb-6">
                <Heart className="h-8 w-8 text-red-500" />
                <h3 className="text-2xl font-bold text-gray-800">
                  Your Protected Health Information Rights
                </h3>
              </div>

              <p className="text-gray-600 mb-6 leading-relaxed">
                Under HIPAA, you have specific rights regarding your protected
                health information. These rights are designed to give you
                control over your personal health data.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {patientRights.map((right, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Check className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">{right}</span>
                  </div>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <h4 className="text-lg font-semibold mb-3 text-gray-800">
                  How to Exercise Your Rights
                </h4>
                <p className="text-gray-600 mb-4">
                  To request access to your information, make corrections, or
                  file a complaint, contact our Privacy Officer:
                </p>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700">
                    <strong>Email:</strong> <EMAIL>
                    <br />
                    <strong>Phone:</strong> 1-800-THERAPY (**************)
                    <br />
                    <strong>Mail:</strong> Privacy Officer, Theramea Inc., 123
                    Wellness Way, Suite 400, New York, NY 10001
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Security Measures */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Technical Security Safeguards
            </h2>

            <div className="bg-gray-50 rounded-lg p-8">
              <div className="flex items-center gap-3 mb-6">
                <Lock className="h-8 w-8 text-blue-600" />
                <h3 className="text-2xl font-bold text-gray-800">
                  Protecting Your Information
                </h3>
              </div>

              <p className="text-gray-600 mb-6 leading-relaxed">
                We implement comprehensive technical, administrative, and
                physical safeguards to protect your protected health information
                from unauthorized access, use, or disclosure.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {securityMeasures.map((measure, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">{measure}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Associate Agreements */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Business Associate Compliance
            </h2>

            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="flex items-center gap-3 mb-6">
                <Users className="h-8 w-8 text-purple-600" />
                <h3 className="text-2xl font-bold text-gray-800">
                  Third-Party Vendor Management
                </h3>
              </div>

              <p className="text-gray-600 mb-6 leading-relaxed">
                Any third-party vendors who may have access to protected health
                information are required to sign Business Associate Agreements
                (BAAs) and comply with HIPAA requirements.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold mb-3 text-gray-800">
                    Our Business Associates Include:
                  </h4>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Cloud hosting providers</li>
                    <li>• Payment processing services</li>
                    <li>• Video conferencing platforms</li>
                    <li>• Email service providers</li>
                    <li>• Data backup and recovery services</li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-lg font-semibold mb-3 text-gray-800">
                    BAA Requirements:
                  </h4>
                  <ul className="space-y-2 text-gray-600">
                    <li>• HIPAA compliance certification</li>
                    <li>• Regular security assessments</li>
                    <li>• Data breach notification procedures</li>
                    <li>• Secure data handling protocols</li>
                    <li>• Return or destruction of PHI when contract ends</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Breach Notification */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Data Breach Prevention & Response
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <AlertCircle className="h-8 w-8 text-red-600 mb-4" />
                <h3 className="text-xl font-bold mb-4 text-red-800">
                  Prevention Measures
                </h3>
                <ul className="space-y-2 text-red-700">
                  <li>• 24/7 security monitoring</li>
                  <li>• Intrusion detection systems</li>
                  <li>• Regular vulnerability assessments</li>
                  <li>• Employee security training</li>
                  <li>• Incident response planning</li>
                </ul>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <Clock className="h-8 w-8 text-blue-600 mb-4" />
                <h3 className="text-xl font-bold mb-4 text-blue-800">
                  Breach Response Protocol
                </h3>
                <ul className="space-y-2 text-blue-700">
                  <li>• Immediate containment and assessment</li>
                  <li>• Notification within 60 days if required</li>
                  <li>• Collaboration with law enforcement</li>
                  <li>• Remediation and prevention measures</li>
                  <li>• Transparent communication with affected users</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance Certifications */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-gray-800">
              Compliance & Certifications
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-md">
                <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2 text-gray-800">
                  HIPAA Compliant
                </h3>
                <p className="text-gray-600 text-sm">
                  Fully compliant with HIPAA Privacy and Security Rules
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-md">
                <Lock className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2 text-gray-800">
                  SOC 2 Type II
                </h3>
                <p className="text-gray-600 text-sm">
                  Independently audited for security and availability
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-md">
                <FileText className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold mb-2 text-gray-800">
                  Annual Assessments
                </h3>
                <p className="text-gray-600 text-sm">
                  Regular third-party security and compliance audits
                </p>
              </div>
            </div>

            <div className="mt-12 bg-white rounded-lg p-8 shadow-md">
              <h3 className="text-xl font-bold mb-4 text-gray-800">
                Questions About HIPAA Compliance?
              </h3>
              <p className="text-gray-600 mb-6">
                Our Privacy Officer is available to answer any questions about
                our HIPAA compliance or your privacy rights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Contact Privacy Officer
                </a>
                <a
                  href="/privacy"
                  className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  View Privacy Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
}

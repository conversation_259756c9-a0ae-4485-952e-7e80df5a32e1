"use client";

import { useState } from "react";
import { ChatMessage } from "@/types/chat";

import {
  Reply,
  Edit3,
  <PERSON>,
  Flag,
  MoreH<PERSON>zontal,
  Check,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";

interface MessageItemProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  onReply?: (message: ChatMessage) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, emoji: string) => void;
  onReport?: (messageId: string) => void;
  allMessages?: ChatMessage[]; // Add this to find replied messages
}

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isOwnMessage,
  onReply,
  onEdit,
  onDelete,
  onReaction,
  onReport,
  allMessages = [],
}) => {
  const [showActions, setShowActions] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content.text || "");
  const isSystemMessage = message.content.type === "system";

  // Find the replied message
  const repliedMessage = message.replyTo
    ? allMessages.find((msg) => msg._id === message.replyTo)
    : null;

  const formatTime = (dateString: string | Date) => {
    if (!dateString) return "Invalid Date";

    // Handle various date formats
    let date: Date;
    if (dateString instanceof Date) {
      date = dateString;
    } else if (typeof dateString === "string") {
      // Try parsing as ISO string first, then as a regular date
      date = new Date(dateString);

      // If that fails, try parsing as a timestamp
      if (isNaN(date.getTime()) && !isNaN(Number(dateString))) {
        date = new Date(Number(dateString));
      }
    } else {
      return "Invalid Date";
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.warn("Invalid date received:", dateString);
      return "Invalid Date";
    }

    const now = new Date();
    const diffInHours =
      Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);

    // If more than 24 hours ago, show date
    if (diffInHours > 24) {
      return date.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
      });
    }

    // Otherwise show time
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const getMessageContent = () => {
    switch (message.content.type) {
      case "text":
        return message.content.text;
      case "image":
        return (
          <div className="mt-2">
            <img
              src={message.content.fileUrl}
              alt={message.content.fileName || "Shared image"}
              className="max-w-sm rounded-lg shadow-sm"
            />
          </div>
        );
      case "file":
        return (
          <div className="mt-2 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center space-x-2">
              <svg
                className="w-5 h-5 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {message.content.fileName}
                </p>
                <p className="text-xs text-gray-500">
                  {message.content.fileSize
                    ? `${(message.content.fileSize / 1024).toFixed(1)} KB`
                    : ""}
                </p>
              </div>
            </div>
          </div>
        );
      case "system":
        return (
          <div className="text-center py-2">
            <span className="text-sm text-gray-500 italic">
              {message.content.text}
            </span>
          </div>
        );
      default:
        return message.content.text;
    }
  };

  const commonReactions = ["👍", "❤️", "😊", "😢", "😮", "😡"];

  if (isSystemMessage) {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gray-100 rounded-full px-4 py-2">
          <span className="text-sm text-gray-600">{message.content.text}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`group relative flex ${
        isOwnMessage ? "justify-end" : "justify-start"
      } mb-4`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div
        className={`max-w-xs lg:max-w-md ${
          isOwnMessage ? "order-2" : "order-1"
        }`}
      >
        {/* WhatsApp-Style Message Header */}
        <div
          className={`flex items-center space-x-2 mb-1 ${
            isOwnMessage ? "justify-end" : "justify-start"
          }`}
        >
          {!isOwnMessage && (
            <span className="text-sm font-medium text-gray-900">
              {message.senderDisplayName}
            </span>
          )}
          <span className="text-xs text-gray-500">
            {formatTime(message.createdAt)}
          </span>
          {message.isEdited && (
            <span className="text-xs text-gray-400">(edited)</span>
          )}
          {/* WhatsApp-Style Status Indicators for Own Messages */}
          {isOwnMessage && (
            <div className="flex items-center space-x-1">
              {message.status === "sending" && (
                <Clock className="w-3 h-3 text-gray-400" />
              )}
              {message.status === "sent" && (
                <Check className="w-3 h-3 text-gray-400" />
              )}
              {message.status === "delivered" && (
                <CheckCheck className="w-3 h-3 text-gray-400" />
              )}
              {message.status === "read" && (
                <CheckCheck className="w-3 h-3 text-blue-500" />
              )}
            </div>
          )}
        </div>

        {/* Reply Context */}
        {message.replyTo && (
          <div className="mb-2 p-2 bg-gray-50 rounded border-l-2 border-gray-300">
            {repliedMessage ? (
              <div>
                <p className="text-xs text-gray-600 mb-1">
                  Replying to {repliedMessage.senderDisplayName}:
                </p>
                <p className="text-sm text-gray-700 truncate">
                  {repliedMessage.content.text || "Media message"}
                </p>
              </div>
            ) : (
              <p className="text-xs text-gray-600">Replying to a message</p>
            )}
          </div>
        )}

        {/* WhatsApp-Style Message Bubble */}
        <div
          className={`relative px-3 py-2 break-words overflow-wrap-anywhere shadow-sm ${
            isOwnMessage
              ? "bg-primary-500 text-white rounded-l-2xl rounded-tr-2xl rounded-br-md"
              : "bg-white text-gray-900 rounded-r-2xl rounded-tl-2xl rounded-bl-md border border-gray-200"
          }`}
        >
          {isEditing ? (
            <div className="space-y-2">
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500"
                rows={3}
              />
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    if (
                      editContent.trim() &&
                      editContent !== message.content.text
                    ) {
                      onEdit?.(message._id, editContent.trim());
                    }
                    setIsEditing(false);
                  }}
                  className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                >
                  Save
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditContent(message.content.text || "");
                  }}
                  className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            getMessageContent()
          )}

          {/* Floating Action Buttons */}
          {showActions && !isSystemMessage && (
            <div
              className={`absolute top-0 ${
                isOwnMessage ? "-left-20" : "-right-20"
              } flex items-center space-x-1 bg-white border border-gray-200 rounded-lg shadow-md px-2 py-1`}
            >
              <button
                onClick={() => setShowReactions(!showReactions)}
                className="text-gray-400 hover:text-yellow-500 p-1 rounded transition-colors"
                title="Add reaction"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </button>

              <button
                onClick={() => onReply?.(message)}
                className="text-gray-400 hover:text-blue-500 p-1 rounded transition-colors"
                title="Reply"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                  />
                </svg>
              </button>

              {isOwnMessage && (
                <>
                  <button
                    onClick={() => {
                      setIsEditing(true);
                      setEditContent(message.content.text || "");
                    }}
                    className="text-gray-400 hover:text-green-500 p-1 rounded transition-colors"
                    title="Edit message"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() => {
                      if (
                        window.confirm(
                          "Are you sure you want to delete this message?"
                        )
                      ) {
                        onDelete?.(message._id);
                      }
                    }}
                    className="text-gray-400 hover:text-red-500 p-1 rounded transition-colors"
                    title="Delete message"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </>
              )}

              {!isOwnMessage && (
                <button
                  onClick={() => onReport?.(message._id)}
                  className="text-gray-400 hover:text-red-500 p-1.5 rounded-lg hover:bg-red-50 transition-all duration-200 hover:scale-110"
                  title="Report message"
                >
                  <Flag className="w-4 h-4" />
                </button>
              )}

              <button
                className="text-gray-400 hover:text-gray-600 p-1.5 rounded-lg hover:bg-gray-50 transition-all duration-200 hover:scale-110"
                title="More options"
              >
                <MoreHorizontal className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {/* Modern Reactions Display */}
        {(message.reactions || []).length > 0 && (
          <div className="flex flex-wrap gap-1.5 mt-3 px-1">
            {(message.reactions || []).map((reaction) => (
              <button
                key={reaction.emoji}
                onClick={() => onReaction?.(message._id, reaction.emoji)}
                className="inline-flex items-center space-x-1.5 bg-white border border-gray-200 hover:border-blue-300 hover:bg-blue-50 rounded-full px-3 py-1.5 text-sm transition-all duration-200 hover:scale-105 shadow-sm"
              >
                <span className="text-base">{reaction.emoji}</span>
                <span className="text-gray-600 font-medium">
                  {reaction.count}
                </span>
              </button>
            ))}
          </div>
        )}

        {/* Modern Reaction Picker */}
        {showReactions && (
          <div className="absolute z-20 mt-2 p-3 bg-white/95 backdrop-blur-sm border border-gray-200/50 rounded-2xl shadow-xl animate-fade-in-up">
            <div className="flex space-x-2">
              {commonReactions.map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => {
                    onReaction?.(message._id, emoji);
                    setShowReactions(false);
                  }}
                  className="hover:bg-gray-100 p-2 rounded-xl text-xl transition-all duration-200 hover:scale-125 active:scale-110"
                  title={`React with ${emoji}`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Quick Action Buttons (Mobile-friendly) */}
      {showActions && !isSystemMessage && (
        <div
          className={`flex items-center space-x-1 mt-2 ${
            isOwnMessage ? "justify-end" : "justify-start"
          } md:hidden`}
        >
          <button
            onClick={() => setShowReactions(!showReactions)}
            className="p-2 text-gray-400 hover:text-yellow-500 hover:bg-yellow-50 rounded-full transition-all duration-200"
          >
            <Smile className="w-4 h-4" />
          </button>
          <button
            onClick={() => onReply?.(message)}
            className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-full transition-all duration-200"
          >
            <Reply className="w-4 h-4" />
          </button>
          {isOwnMessage && (
            <button
              onClick={() => setIsEditing(true)}
              className="p-2 text-gray-400 hover:text-indigo-500 hover:bg-indigo-50 rounded-full transition-all duration-200"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}
        </div>
      )}
    </div>
  );
};

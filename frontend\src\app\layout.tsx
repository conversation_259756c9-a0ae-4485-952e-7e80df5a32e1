import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import HydrationProvider from "@/components/providers/HydrationProvider";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Theramea - Digital Counseling Platform",
  description:
    "Access professional counseling without pressure or judgment. Join safe group chats, explore self-help resources, and book private sessions with certified counselors.",
  keywords: ["counseling", "therapy", "mental health", "support", "wellness"],
  authors: [{ name: "Theramea Team" }],
  creator: "Theram<PERSON>",
  publisher: "Theramea",
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  ),
  openGraph: {
    title: "Theramea - Digital Counseling Platform",
    description: "Access professional counseling without pressure or judgment",
    url: "/",
    siteName: "Theramea",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Theramea - Digital Counseling Platform",
    description: "Access professional counseling without pressure or judgment",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className="font-sans antialiased">
        <HydrationProvider>
          <div id="root">{children}</div>
        </HydrationProvider>
      </body>
    </html>
  );
}

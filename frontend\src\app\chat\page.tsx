"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { chatroomService, ChatroomData } from "@/lib/chatroomService";
import Header from "@/components/layout/Header";
import ChatRoomCard from "@/components/chat/ChatRoomCard";
import ChatFilters from "@/components/chat/ChatFilters";
import CreateRoomModal from "@/components/chat/CreateRoomModal";
import {
  ChatBubbleLeftRightIcon,
  PlusIcon,
  UserGroupIcon,
  ClockIcon,
  FireIcon,
} from "@heroicons/react/24/outline";

interface ChatPageFilters {
  category?: string;
  topic?: string;
  search?: string;
  sortBy?: "recent" | "popular" | "participants";
}

export default function ChatPage() {
  const router = useRouter();
  const { isAuthenticated, isGuest, user, tokens, guestToken, isLoading } =
    useAuthStore();
  const [chatRooms, setChatRooms] = useState<ChatroomData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [filters, setFilters] = useState<ChatPageFilters>({
    sortBy: "recent",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const token = tokens?.accessToken || guestToken;

  useEffect(() => {
    // Redirect unauthenticated users to public chatrooms page
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push("/chatrooms");
      return;
    }

    if (!isLoading) {
      fetchChatRooms();
    }
  }, [isLoading, filters, currentPage, isAuthenticated, isGuest, router]);

  const fetchChatRooms = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the unified chatroom service
      const rooms = await chatroomService.getChatrooms(
        token || undefined,
        {
          category: filters.category,
          topic: filters.topic,
          search: filters.search,
        },
        {
          page: currentPage,
          limit: 12,
          sortBy:
            filters.sortBy === "recent"
              ? "statistics.lastActivityAt"
              : filters.sortBy === "popular"
              ? "statistics.totalMessages"
              : filters.sortBy === "participants"
              ? "currentParticipants"
              : "statistics.lastActivityAt",
          sortOrder: "desc",
        }
      );

      setChatRooms(rooms);
      setTotalPages(Math.ceil(rooms.length / 12)); // Simple pagination for now
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load chat rooms"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters: ChatPageFilters) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  };

  const handleJoinRoom = (roomId: string) => {
    if (!isAuthenticated && !isGuest) {
      router.push("/auth/login");
      return;
    }
    router.push(`/chat/${roomId}`);
  };
  const handleCreateRoom = async () => {
    try {
      // For now, just refresh the list since creation is not implemented in the service yet
      setShowCreateModal(false);
      fetchChatRooms(); // Refresh the list
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to create chat room"
      );
    }
  };

  const canCreateRoom = isAuthenticated && user?.role === "counselor";

  const categories = [
    { value: "support-groups", label: "Support Groups", icon: "🤝" },
    { value: "educational", label: "Educational", icon: "📚" },
    { value: "peer-chat", label: "Peer Chat", icon: "💬" },
    { value: "crisis-support", label: "Crisis Support", icon: "🆘" },
    { value: "special-topics", label: "Special Topics", icon: "🎯" },
  ];

  // Show loading while redirecting unauthenticated users
  if (!isAuthenticated && !isGuest && !isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Redirecting to public chatrooms...</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <ChatBubbleLeftRightIcon className="h-8 w-8 text-purple-600 mr-3" />
                Community Chat
              </h1>
              <div className="mt-2 flex items-center space-x-4">
                <p className="text-gray-600">
                  Connect with others in supportive group conversations
                </p>
                {isGuest && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Guest User
                  </span>
                )}
              </div>
            </div>

            {canCreateRoom && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-medium transition-colors"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                Create Room
              </button>
            )}
          </div>
        </div>

        {/* Guest Notice */}
        {isGuest && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <div className="text-blue-600">ℹ️</div>
              <div>
                <h3 className="text-sm font-medium text-blue-900">
                  Chatting as Guest
                </h3>
                <p className="text-sm text-blue-700">
                  You're participating anonymously.
                  <button
                    onClick={() => router.push("/auth/signup")}
                    className="ml-1 text-blue-800 hover:text-blue-900 underline"
                  >
                    Create an account
                  </button>{" "}
                  to access more features.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <ChatFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            categories={categories}
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Active Rooms
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {chatRooms.filter((room) => room.isActive).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Online Now</p>
                <p className="text-2xl font-bold text-gray-900">
                  {chatRooms.reduce((sum, room) => sum + room.activeUsers, 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FireIcon className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Most Popular
                </p>
                <p className="text-lg font-bold text-gray-900">
                  {chatRooms.length > 0
                    ? chatRooms
                        .reduce((max, room) =>
                          room.activeUsers > max.activeUsers ? room : max
                        )
                        .name.slice(0, 15) +
                      (chatRooms.reduce((max, room) =>
                        room.activeUsers > max.activeUsers ? room : max
                      ).name.length > 15
                        ? "..."
                        : "")
                    : "N/A"}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Chat Rooms Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse"
              >
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : chatRooms.length === 0 ? (
          <div className="text-center py-12">
            <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No chat rooms found
            </h3>
            <p className="text-gray-600 mb-4">
              {filters.search || filters.category || filters.topic
                ? "Try adjusting your filters to find more rooms."
                : "Be the first to start a conversation!"}
            </p>
            {canCreateRoom && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium"
              >
                Create First Room
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {chatRooms.map((room) => (
              <ChatRoomCard
                key={room.id}
                room={room}
                onJoin={() => handleJoinRoom(room.id)}
                isGuest={isGuest}
              />
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <span className="px-4 py-2 text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>

            <button
              onClick={() =>
                setCurrentPage((prev) => Math.min(totalPages, prev + 1))
              }
              disabled={currentPage === totalPages}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
      </div>

      {/* Create Room Modal */}
      {showCreateModal && (
        <CreateRoomModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateRoom}
          categories={categories}
        />
      )}
    </div>
  );
}

"use client";

import { useState, useEffect, useRef } from "react";
import { useAuthStore } from "@/store/authStore";
import { useSocket } from "@/hooks/useSocket";
import { chatAPI } from "@/lib/chat";
import {
  ChatRoom as ChatRoomType,
  ChatMessage,
  TypingUser,
} from "@/types/chat";
import { MessageItem } from "./MessageItem";
import MessageInput from "./MessageInput";
import ReportModal from "../modals/ReportModal";
import DeleteConfirmationModal from "../modals/DeleteConfirmationModal";
import { Shield, ChevronRight } from "lucide-react";

interface ChatRoomProps {
  roomId: string;
}

export default function ChatRoom({ roomId }: ChatRoomProps) {
  const { tokens, guestToken, user, isGuest } = useAuthStore();
  const [room, setRoom] = useState<ChatRoomType | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [showParticipants, setShowParticipants] = useState(false);
  const [isReadonly, setIsReadonly] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportingMessageId, setReportingMessageId] = useState<string | null>(
    null
  );
  const [isReporting, setIsReporting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingMessageId, setDeletingMessageId] = useState<string | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const token = tokens?.accessToken || guestToken;

  // Initialize socket connection
  const {
    socket,
    isConnected,
    joinRoom,
    leaveRoom,
    sendMessage,
    editMessage,
    deleteMessage,
    startTyping,
    stopTyping,
  } = useSocket({
    roomId,
    onNewMessage: (message: ChatMessage) => {
      setMessages((prev) => [...prev, message]);
      scrollToBottom();
    },
    onMessageEdited: (data) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg._id === data.messageId
            ? {
                ...msg,
                content: { ...msg.content, text: data.newContent },
                isEdited: true,
              }
            : msg
        )
      );
    },
    onMessageDeleted: (data) => {
      setMessages((prev) => prev.filter((msg) => msg._id !== data.messageId));
    },
    onTypingStart: (user: TypingUser) => {
      setTypingUsers((prev) => {
        const exists = prev.some(
          (u) =>
            (u.userId && u.userId === user.userId) ||
            (u.anonymousId && u.anonymousId === user.anonymousId)
        );
        return exists ? prev : [...prev, user];
      });
    },
    onTypingStop: (user: TypingUser) => {
      setTypingUsers((prev) =>
        prev.filter(
          (u) =>
            !(
              (u.userId && u.userId === user.userId) ||
              (u.anonymousId && u.anonymousId === user.anonymousId)
            )
        )
      );
    },
    onError: (error: string) => {
      setError(error);
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    fetchRoomData();
    fetchMessages();
  }, [roomId, token]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchRoomData = async () => {
    try {
      const response = await chatAPI.getChatRoom(roomId, token || undefined);
      setRoom(response.data.chatRoom);

      // Check if current user is readonly
      if (response.data.chatRoom.participants) {
        let currentUserId = null;

        if (isGuest && guestToken) {
          try {
            const tokenPayload = JSON.parse(atob(guestToken.split(".")[1]));
            currentUserId = tokenPayload.userId || tokenPayload.guestId;
          } catch (e) {
            console.warn("Failed to decode guest token:", e);
          }
        } else if (user) {
          currentUserId = user._id;
        }

        const currentParticipant = response.data.chatRoom.participants.find(
          (p: any) =>
            (user && p.userId === user._id) ||
            (isGuest && currentUserId && p.anonymousId === currentUserId)
        );

        // Debug logging to help identify the issue
        console.log("DEBUG - Chat Room Data:", {
          isGuest,
          user: user ? { _id: user._id, role: user.role } : null,
          currentUserId,
          currentParticipant: currentParticipant
            ? {
                userId: currentParticipant.userId,
                anonymousId: currentParticipant.anonymousId,
                role: currentParticipant.role,
                displayName: currentParticipant.displayName,
              }
            : null,
          participants: response.data.chatRoom.participants.map((p: any) => ({
            userId: p.userId,
            anonymousId: p.anonymousId,
            role: p.role,
            displayName: p.displayName,
          })),
        });

        // Determine readonly status with safeguards for registered users
        let shouldBeReadonly = false;

        if (currentParticipant) {
          // If this is a registered user (not a guest), they should never be readonly
          if (!isGuest && user && user._id) {
            shouldBeReadonly = false; // Force non-readonly for registered users
            console.log(
              "DEBUG - Registered user detected, forcing participant access"
            );
          } else if (isGuest) {
            // Guest users should be readonly
            shouldBeReadonly = true;
            console.log("DEBUG - Guest user detected, setting readonly access");
          } else {
            // Fallback to the role from the server
            shouldBeReadonly = currentParticipant.role === "readonly";
            console.log(
              "DEBUG - Using server-assigned role:",
              currentParticipant.role
            );
          }
        } else {
          // No participant record found - this shouldn't happen but handle gracefully
          shouldBeReadonly = isGuest;
          console.log(
            "DEBUG - No participant record found, defaulting based on guest status:",
            isGuest
          );
        }

        setIsReadonly(shouldBeReadonly);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load room");
    }
  };

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getChatMessages(
        roomId,
        1,
        50,
        token || undefined
      );
      setMessages(response.data.messages.reverse()); // Reverse to show oldest first

      // Mark messages as read after loading
      await markMessagesAsRead();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load messages");
    } finally {
      setLoading(false);
    }
  };

  const markMessagesAsRead = async () => {
    try {
      await chatAPI.markMessagesAsRead(roomId, token || undefined);
    } catch (err) {
      console.error("Failed to mark messages as read:", err);
    }
  };

  const handleSendMessage = (content: {
    text?: string;
    type: "text" | "file" | "image";
    fileUrl?: string;
    fileName?: string;
  }) => {
    if (!isConnected) {
      setError("Not connected to chat server");
      return;
    }

    if (content.type === "text" && !content.text?.trim()) return;
    if (
      (content.type === "file" || content.type === "image") &&
      !content.fileUrl
    )
      return;

    // Send the full content object with type
    if (content.type === "text" && content.text) {
      sendMessage(roomId, { text: content.text, type: "text" }, replyTo?._id);
    } else if (content.fileUrl) {
      // Handle file/image messages
      sendMessage(
        roomId,
        {
          text: content.text || content.fileName || "File attachment",
          type: content.type,
          fileUrl: content.fileUrl,
          fileName: content.fileName,
        },
        replyTo?._id
      );
    }

    setReplyTo(null);
  };

  const handleReply = (message: ChatMessage) => {
    setReplyTo(message);
  };

  const handleReport = (messageId: string) => {
    setReportingMessageId(messageId);
    setShowReportModal(true);
  };

  const handleReportSubmit = async (reason: string, category: string) => {
    if (!reportingMessageId) return;

    setIsReporting(true);
    try {
      await chatAPI.reportMessage(
        reportingMessageId,
        category as
          | "spam"
          | "harassment"
          | "inappropriate"
          | "off-topic"
          | "other",
        reason,
        token || undefined
      );

      // Close modal and reset state
      setShowReportModal(false);
      setReportingMessageId(null);

      // Show success message (you could replace this with a toast notification)
      alert(
        "Message reported successfully. Thank you for helping keep our community safe."
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to report message");
    } finally {
      setIsReporting(false);
    }
  };

  const handleReportClose = () => {
    if (!isReporting) {
      setShowReportModal(false);
      setReportingMessageId(null);
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    try {
      // Send reaction through socket if available
      if (socket && isConnected) {
        socket.emit("message:reaction", {
          messageId,
          emoji,
          roomId,
        });
      } else {
        // Fallback to API call
        const response = await fetch(`/api/messages/${messageId}/reaction`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ emoji }),
        });

        if (!response.ok) {
          throw new Error("Failed to add reaction");
        }

        // Update local state optimistically
        setMessages((prev) =>
          prev.map((msg) => {
            if (msg._id === messageId) {
              const existingReaction = msg.reactions?.find(
                (r) => r.emoji === emoji
              );
              if (existingReaction) {
                // Increment count and add user
                return {
                  ...msg,
                  reactions: msg.reactions?.map((r) =>
                    r.emoji === emoji
                      ? {
                          ...r,
                          count: r.count + 1,
                          users: [
                            ...r.users,
                            {
                              userId: user?._id,
                              anonymousId: isGuest
                                ? guestToken || undefined
                                : undefined,
                              addedAt: new Date().toISOString(),
                            },
                          ],
                        }
                      : r
                  ),
                };
              } else {
                // Add new reaction
                return {
                  ...msg,
                  reactions: [
                    ...(msg.reactions || []),
                    {
                      emoji,
                      count: 1,
                      users: [
                        {
                          userId: user?._id,
                          anonymousId: isGuest
                            ? guestToken || undefined
                            : undefined,
                          addedAt: new Date().toISOString(),
                        },
                      ],
                    },
                  ],
                };
              }
            }
            return msg;
          })
        );
      }
    } catch (err) {
      console.error("Failed to add reaction:", err);
      setError("Failed to add reaction");
    }
  };

  const handleDelete = (messageId: string) => {
    setDeletingMessageId(messageId);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingMessageId) return;

    setIsDeleting(true);
    try {
      // Use socket for real-time deletion
      deleteMessage(deletingMessageId);
      // Also call API as fallback
      await chatAPI.deleteMessage(deletingMessageId, token || undefined);

      // Close modal and reset state
      setShowDeleteModal(false);
      setDeletingMessageId(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete message");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteClose = () => {
    if (!isDeleting) {
      setShowDeleteModal(false);
      setDeletingMessageId(null);
    }
  };

  const handleEdit = async (messageId: string, newContent: string) => {
    try {
      // Use socket for real-time editing
      editMessage(messageId, newContent);
      // Also call API as fallback
      await chatAPI.editMessage(messageId, newContent, token || undefined);
      // Update the message in local state
      setMessages((prev) =>
        prev.map((msg) =>
          msg._id === messageId
            ? {
                ...msg,
                content: { ...msg.content, text: newContent },
                isEdited: true,
              }
            : msg
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to edit message");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <svg
            className="w-12 h-12 text-red-400 mx-auto mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Room not found
          </h3>
          <p className="text-gray-600">
            This chat room may have been removed or you don't have access.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col h-full">
        {/* Fixed Messages Area - Scrollable */}
        <div
          ref={messagesContainerRef}
          className="flex-1 overflow-y-auto px-6 py-4 space-y-4 bg-gradient-to-b from-gray-50/30 to-white"
          style={{ height: "calc(100vh - 200px)" }}
        >
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
                <svg
                  className="w-10 h-10 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                Welcome to {room?.name}!
              </h3>
              <p className="text-gray-500 max-w-md">
                No messages yet. Be the first to start the conversation and
                connect with others in this room.
              </p>
            </div>
          ) : (
            <>
              {messages.map((message, index) => (
                <div
                  key={message._id}
                  className={`transform transition-all duration-300 ${
                    index === messages.length - 1 ? "animate-fade-in-up" : ""
                  }`}
                >
                  <MessageItem
                    message={message}
                    isOwnMessage={
                      isGuest
                        ? (() => {
                            // For guest users, check anonymousSenderId against the current guest's ID
                            if (guestToken) {
                              try {
                                const tokenPayload = JSON.parse(
                                  atob(guestToken.split(".")[1])
                                );
                                return (
                                  message.anonymousSenderId ===
                                  tokenPayload.userId
                                );
                              } catch (error) {
                                console.error(
                                  "Error decoding guest token:",
                                  error
                                );
                                return false;
                              }
                            }
                            return false;
                          })()
                        : message.senderId === user?._id
                    }
                    onReply={handleReply}
                    onReport={handleReport}
                    onReaction={handleReaction}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    allMessages={messages}
                  />
                </div>
              ))}

              {/* WhatsApp-Style Typing Indicator */}
              {typingUsers.length > 0 && (
                <div className="flex items-start space-x-3 px-6 py-2">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-xs text-gray-600">
                      {typingUsers[0]?.displayName?.charAt(0)?.toUpperCase() ||
                        "?"}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-100 rounded-2xl px-4 py-2 max-w-xs">
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "0.4s" }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1 ml-1">
                      {typingUsers.length === 1
                        ? `${typingUsers[0].displayName} is typing...`
                        : `${typingUsers.length} people are typing...`}
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Fixed Message Input Area at Bottom */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 shadow-lg">
          {!isReadonly ? (
            <MessageInput
              onSendMessage={handleSendMessage}
              onTypingStart={() => startTyping(roomId)}
              onTypingStop={() => stopTyping(roomId)}
              replyTo={replyTo}
              onCancelReply={() => setReplyTo(null)}
              disabled={!isConnected}
              placeholder={
                isConnected ? "Type your message..." : "Connecting..."
              }
            />
          ) : (
            <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-t border-amber-200 px-6 py-4">
              <div className="flex items-center space-x-3 text-amber-800">
                <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
                  <Shield className="w-5 h-5 text-amber-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Read-Only Access</p>
                  <p className="text-xs text-amber-700">
                    You can view messages but cannot participate as a guest
                    user.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modern Participants Sidebar */}
      {showParticipants && (
        <div className="w-80 bg-white/90 backdrop-blur-xl border-l border-white/20 shadow-xl">
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Participants
              </h3>
              <button
                onClick={() => setShowParticipants(false)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-3 max-h-96 overflow-y-auto">
            {room?.participants?.map((participant, index) => (
              <div
                key={participant.userId || participant.anonymousId || index}
                className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 transition-colors"
              >
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-sm">
                    {participant.displayName?.charAt(0)?.toUpperCase() || "U"}
                  </span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">
                    {participant.displayName}
                  </p>
                  <p className="text-sm text-gray-500 capitalize">
                    {participant.role}
                  </p>
                </div>
                {participant.userId === user?._id && (
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    You
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Report Modal */}
      <ReportModal
        isOpen={showReportModal}
        onClose={handleReportClose}
        onSubmit={handleReportSubmit}
        messageId={reportingMessageId || ""}
        isLoading={isReporting}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteClose}
        onConfirm={handleDeleteConfirm}
        title="Delete Message"
        message="Are you sure you want to delete this message? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />
    </div>
  );
}

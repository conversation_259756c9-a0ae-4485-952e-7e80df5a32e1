"use client";

import { useState, useEffect } from "react";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";
import Link from "next/link";
import CounselorCard from "@/components/counselors/CounselorCard";
import CounselorFilters from "@/components/counselors/CounselorFilters";
import { ApiCounselor } from "@/types/counselor";

export default function CounselorsPage() {
  const { isAuthenticated, isGuest, tokens } = useAuthStore();
  const [counselors, setCounselors] = useState<ApiCounselor[]>([]);
  const [selectedSpecialization, setSelectedSpecialization] = useState("all");
  const [priceRange, setPriceRange] = useState([0, 15000]); // Adjusted for NGN currency
  const [sortBy, setSortBy] = useState("rating");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [likedCounselors, setLikedCounselors] = useState<Set<string>>(
    new Set()
  );

  // Clear filters function
  const handleClearFilters = () => {
    setSelectedSpecialization("all");
    setPriceRange([0, 15000]);
  };

  // Function to fetch counselors from API
  const fetchCounselors = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const apiUrl = `${
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
      }/counselors?limit=50`;
      console.log("Fetching counselors from:", apiUrl);

      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      console.log("API Response status:", response.status);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch counselors: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      console.log("API Response data:", data);

      if (data.success && data.data && data.data.counselors) {
        console.log("Found counselors:", data.data.counselors.length);
        setCounselors(data.data.counselors);
      } else {
        console.log("No counselors found in API response");
        throw new Error("No counselors found in API response");
      }
    } catch (error) {
      console.error("Error fetching counselors:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load counselors"
      );
      setCounselors([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch user's liked counselors
  const fetchLikedCounselors = async () => {
    if (!isAuthenticated || !tokens?.accessToken) return;

    try {
      const apiUrl = `${
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
      }/counselors/user/liked`;

      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${tokens.accessToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data?.likedCounselors) {
          const likedIds = new Set(
            data.data.likedCounselors.map(
              (counselor: any) => counselor._id || counselor.id
            ) as string[]
          );
          setLikedCounselors(likedIds);
        }
      }
    } catch (error) {
      console.error("Error fetching liked counselors:", error);
    }
  };

  // Function to handle liking/unliking a counselor
  const handleLikeCounselor = async (counselorId: string) => {
    if (!isAuthenticated || !tokens?.accessToken) return;

    try {
      const isCurrentlyLiked = likedCounselors.has(counselorId);
      const method = isCurrentlyLiked ? "DELETE" : "POST";

      const apiUrl = `${
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
      }/counselors/${counselorId}/like`;

      const response = await fetch(apiUrl, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${tokens.accessToken}`,
        },
      });

      if (response.ok) {
        // Update the liked counselors state
        const updatedLiked = new Set(likedCounselors);
        if (isCurrentlyLiked) {
          updatedLiked.delete(counselorId);
        } else {
          updatedLiked.add(counselorId);
        }
        setLikedCounselors(updatedLiked);

        console.log(
          `${isCurrentlyLiked ? "Unliked" : "Liked"} counselor:`,
          counselorId
        );
      } else {
        console.error("Failed to update like status");
      }
    } catch (error) {
      console.error("Error updating like status:", error);
    }
  };

  useEffect(() => {
    fetchCounselors();
    if (isAuthenticated) {
      fetchLikedCounselors();
    }
  }, [isAuthenticated]);

  const filteredCounselors = counselors.filter((counselor) => {
    const matchesSpecialization =
      selectedSpecialization === "all" ||
      counselor.specializations.includes(selectedSpecialization);
    const hourlyRate = Math.round(counselor.pricing.ratePerMinute * 60);
    const matchesPrice =
      hourlyRate >= priceRange[0] && hourlyRate <= priceRange[1];

    // Debug logging
    console.log(
      `Counselor: ${counselor.userId.firstName} ${counselor.userId.lastName}`
    );
    console.log(`  - Hourly rate: ₦${hourlyRate.toLocaleString()}`);
    console.log(
      `  - Price range: ₦${priceRange[0].toLocaleString()} - ₦${priceRange[1].toLocaleString()}`
    );
    console.log(`  - Matches price: ${matchesPrice}`);
    console.log(`  - Specializations: ${counselor.specializations.join(", ")}`);
    console.log(`  - Selected spec: ${selectedSpecialization}`);
    console.log(`  - Matches specialization: ${matchesSpecialization}`);
    console.log(`  - Overall match: ${matchesSpecialization && matchesPrice}`);

    return matchesSpecialization && matchesPrice;
  });

  const sortedCounselors = [...filteredCounselors].sort((a, b) => {
    switch (sortBy) {
      case "rating":
        return b.statistics.averageRating - a.statistics.averageRating;
      case "price-low":
        return a.pricing.ratePerMinute * 60 - b.pricing.ratePerMinute * 60;
      case "price-high":
        return b.pricing.ratePerMinute * 60 - a.pricing.ratePerMinute * 60;
      case "experience":
        return b.experience.years - a.experience.years;
      default:
        return 0;
    }
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading counselors...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Unable to Load Counselors
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={fetchCounselors}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Find Your Counselor
          </h1>
          <p className="text-gray-600">
            Connect with licensed mental health professionals who understand
            your needs.
          </p>
        </div>

        {/* Guest Notice */}
        {(isGuest || !isAuthenticated) && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-8">
            <div className="flex items-center space-x-3">
              <svg
                className="w-6 h-6 text-amber-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <div>
                <h3 className="font-medium text-amber-900">
                  Account Required for Booking
                </h3>
                <p className="text-sm text-amber-700">
                  You can browse counselor profiles, but you'll need to{" "}
                  <Link
                    href="/auth/signup"
                    className="underline hover:no-underline"
                  >
                    create an account
                  </Link>{" "}
                  or{" "}
                  <Link href="/auth/login" className="underline hover:no-underline">
                    login
                  </Link>{" "}
                  to book sessions.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <CounselorFilters
              counselors={counselors}
              selectedSpecialization={selectedSpecialization}
              setSelectedSpecialization={setSelectedSpecialization}
              priceRange={priceRange}
              setPriceRange={setPriceRange}
              onClearFilters={handleClearFilters}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Sort and Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {sortedCounselors.length} Counselor
                  {sortedCounselors.length !== 1 ? "s" : ""} Available
                </h2>
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="rating">Highest Rated</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="experience">Most Experience</option>
                </select>
              </div>
            </div>

            {/* Counselors Grid */}
            <div className="space-y-6">
              {sortedCounselors.map((counselor) => (
                <CounselorCard
                  key={counselor._id}
                  counselor={counselor}
                  onLike={handleLikeCounselor}
                  isLiked={likedCounselors.has(counselor._id)}
                />
              ))}
            </div>

            {sortedCounselors.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No counselors found
                </h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your filters to see more results.
                </p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

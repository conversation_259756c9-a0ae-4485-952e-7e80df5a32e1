import { Router } from "express";
import { Chat<PERSON>ontroller } from "@/controllers/chatController";
import {
  chatValidations,
  reportValidations,
  paramValidations,
} from "@/utils/validation";
import {
  authenticate,
  optionalAuth,
  requireAdmin,
  requireCounselor,
} from "@/middleware/auth";
import { uploadChatFile, handleUploadError } from "@/middleware/upload";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// Public routes
router.get("/rooms", optionalAuth, ChatController.getChatRooms);

router.get("/rooms/:roomId", ChatController.getChatRoom);

router.post(
  "/rooms/:roomId/join",
  optionalAuth,
  paramValidations.objectId("roomId"),
  chatValidations.joinRoom,
  validateRequest,
  ChatController.joinChatRoom
);

router.get("/rooms/:roomId/messages", ChatController.getMessages);

router.post(
  "/anonymous-credentials",
  ChatController.generateAnonymousCredentials
);

// Chat room management (admin/moderator only)
router.post(
  "/rooms",
  authenticate,
  requireCounselor,
  chatValidations.createRoom,
  validateRequest,
  ChatController.createChatRoom
);

// File upload
router.post(
  "/upload",
  authenticate,
  uploadChatFile,
  handleUploadError,
  ChatController.uploadChatFile
);

// Reporting and moderation
router.post(
  "/report",
  authenticate,
  reportValidations.create,
  validateRequest,
  ChatController.reportContent
);

// Moderator actions
router.put(
  "/messages/:messageId/hide",
  authenticate,
  requireCounselor,
  paramValidations.objectId("messageId"),
  validateRequest,
  ChatController.hideMessage
);

router.delete(
  "/messages/:messageId",
  authenticate,
  requireCounselor,
  paramValidations.objectId("messageId"),
  validateRequest,
  ChatController.deleteMessage
);

// User can edit their own messages
router.put(
  "/messages/:messageId",
  optionalAuth,
  paramValidations.objectId("messageId"),
  validateRequest,
  ChatController.editOwnMessage
);

// User can delete their own messages
router.delete(
  "/messages/:messageId/own",
  optionalAuth,
  paramValidations.objectId("messageId"),
  validateRequest,
  ChatController.deleteOwnMessage
);

router.put(
  "/rooms/:roomId/users/:userId/mute",
  authenticate,
  requireCounselor,
  paramValidations.objectId("roomId"),
  paramValidations.objectId("userId"),
  validateRequest,
  ChatController.muteUser
);

router.put(
  "/rooms/:roomId/users/:userId/unmute",
  authenticate,
  requireCounselor,
  paramValidations.objectId("roomId"),
  paramValidations.objectId("userId"),
  validateRequest,
  ChatController.unmuteUser
);

// Admin routes
router.get(
  "/moderation/queue",
  authenticate,
  requireAdmin,
  ChatController.getModerationQueue
);

router.put(
  "/moderation/reports/:reportId/resolve",
  authenticate,
  requireAdmin,
  paramValidations.objectId("reportId"),
  validateRequest,
  ChatController.resolveReport
);

router.get(
  "/moderation/users/:userId/history",
  authenticate,
  requireAdmin,
  paramValidations.objectId("userId"),
  validateRequest,
  ChatController.getUserModerationHistory
);

// Message read status routes
router.post(
  "/rooms/:roomId/mark-read",
  optionalAuth,
  paramValidations.objectId("roomId"),
  validateRequest,
  ChatController.markMessagesAsRead
);

router.get(
  "/rooms/:roomId/unread-count",
  optionalAuth,
  paramValidations.objectId("roomId"),
  validateRequest,
  ChatController.getUnreadMessageCount
);

export default router;

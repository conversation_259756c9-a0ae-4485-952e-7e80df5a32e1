"use client";

import { ChatRoom } from "@/types/chat";
import { ChatroomData } from "@/lib/chatroomService";
import {
  UserGroupIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  LockClosedIcon,
  ShieldCheckIcon,
} from "@heroicons/react/24/outline";

interface ChatRoomCardProps {
  room: ChatRoom | ChatroomData;
  onJoin: () => void;
  isGuest?: boolean;
}

// Type guard to check if room is ChatroomData
function isChatroomData(room: ChatRoom | ChatroomData): room is ChatroomData {
  return "activeUsers" in room;
}

export default function ChatRoomCard({
  room,
  onJoin,
  isGuest,
}: ChatRoomCardProps) {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "support-groups":
      case "stress":
        return "🤝";
      case "educational":
      case "student":
        return "📚";
      case "peer-chat":
        return "💬";
      case "crisis-support":
        return "🆘";
      case "special-topics":
        return "🎯";
      default:
        return "💭";
    }
  };

  const formatLastActivity = (date: string) => {
    // Handle both formatted strings and ISO dates
    if (date.includes("ago")) return date;

    const now = new Date();
    const lastActivity = new Date(date);
    const diffInMinutes = Math.floor(
      (now.getTime() - lastActivity.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  // Check if this is ChatroomData type or ChatRoom type
  const isSimpleRoom = isChatroomData(room);

  // Extract values with fallbacks for missing properties
  const activeUsers = isSimpleRoom
    ? room.activeUsers
    : room.currentParticipants;
  const maxParticipants = isSimpleRoom ? 50 : room.maxParticipants; // Default for simple rooms
  const totalMessages = isSimpleRoom
    ? room.totalMessages
    : room.statistics?.totalMessages || 0;
  const lastActivity = isSimpleRoom
    ? room.lastActivity
    : room.statistics?.lastActivityAt || new Date().toISOString();
  const isModerated = isSimpleRoom ? false : room.isModerated;
  const allowAnonymous = isSimpleRoom
    ? true
    : room.settings?.allowAnonymous || false;
  const topic = isSimpleRoom ? "" : room.topic || "";

  const isRoomFull = activeUsers >= maxParticipants;
  const canJoin = room.isActive && !isRoomFull && (allowAnonymous || !isGuest);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{getCategoryIcon(room.category)}</span>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                {room.name}
              </h3>
              <p className="text-sm text-gray-600 capitalize">
                {room.category.replace("-", " ")}
                {topic && ` • ${topic}`}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            {isModerated && (
              <ShieldCheckIcon
                className="h-4 w-4 text-blue-600"
                title="Moderated"
              />
            )}
            {!allowAnonymous && (
              <LockClosedIcon
                className="h-4 w-4 text-orange-600"
                title="Registered users only"
              />
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-700 text-sm mb-4 line-clamp-2">
          {room.description}
        </p>

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <UserGroupIcon className="h-4 w-4" />
              <span>
                {activeUsers}/{maxParticipants}
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <ChatBubbleLeftRightIcon className="h-4 w-4" />
              <span>{totalMessages}</span>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <ClockIcon className="h-4 w-4" />
            <span>{formatLastActivity(lastActivity)}</span>
          </div>
        </div>

        {/* Tags */}
        {room.tags && room.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {room.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
              >
                {tag}
              </span>
            ))}
            {room.tags.length > 3 && (
              <span className="text-xs text-gray-500">
                +{room.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Status and Join Button */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                room.isActive
                  ? "bg-primary-100 text-primary-800"
                  : "bg-red-100 text-red-800"
              }`}
            >
              {room.isActive ? "Active" : "Inactive"}
            </span>

            {isRoomFull && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                Full
              </span>
            )}
          </div>

          <button
            onClick={onJoin}
            disabled={!canJoin}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              canJoin
                ? "bg-purple-600 hover:bg-purple-700 text-white"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }`}
          >
            {!room.isActive
              ? "Inactive"
              : isRoomFull
              ? "Full"
              : !allowAnonymous && isGuest
              ? "Login Required"
              : "Join"}
          </button>
        </div>

        {/* Guest Warning */}
        {isGuest && !allowAnonymous && (
          <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-xs text-yellow-800">
              This room requires registration to join.
            </p>
          </div>
        )}

        {/* Guest Readonly Notice */}
        {isGuest && allowAnonymous && (
          <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-xs text-amber-800">
              <svg
                className="w-3 h-3 inline mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              Guest access is read-only - you can view but not send messages.
            </p>
          </div>
        )}

        {/* Schedule Info - only for full ChatRoom objects */}
        {!isSimpleRoom && room.schedule?.isScheduled && (
          <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-xs text-blue-800">
              <ClockIcon className="h-3 w-3 inline mr-1" />
              Scheduled:{" "}
              {new Date(room.schedule.startTime!).toLocaleDateString()}
              {room.schedule.recurringPattern &&
                ` (${room.schedule.recurringPattern})`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

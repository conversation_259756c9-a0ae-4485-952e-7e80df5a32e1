const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

export interface MoodEntry {
  _id: string;
  userId: string;
  mood: number; // 1-5 scale
  note?: string;
  tags: string[];
  triggers?: string[];
  activities?: string[];
  energy: number; // 1-5 scale
  anxiety: number; // 1-5 scale
  sleep?: {
    hours: number;
    quality: number; // 1-5 scale
  };
  weather?: {
    condition:
      | "sunny"
      | "cloudy"
      | "rainy"
      | "snowy"
      | "stormy"
      | "foggy"
      | "windy";
    temperature?: number;
  };
  location?: {
    type: "home" | "work" | "school" | "outdoors" | "social" | "other";
    description?: string;
  };
  socialInteraction?: {
    level: number; // 1-5 scale
    quality: number; // 1-5 scale
  };
  medications?: {
    taken: boolean;
    names?: string[];
    notes?: string;
  };
  symptoms?: string[];
  gratitude?: string[];
  goals?: {
    achieved: string[];
    working_on: string[];
  };
  isPrivate: boolean;
  metadata: {
    source: "manual" | "reminder" | "onboarding" | "session_followup";
    deviceType?: "mobile" | "desktop" | "tablet";
    timeZone?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateMoodEntryData {
  mood: number;
  note?: string;
  tags?: string[];
  triggers?: string[];
  activities?: string[];
  energy?: number;
  anxiety?: number;
  sleep?: {
    hours?: number;
    quality?: number;
  };
  weather?: {
    condition?: string;
    temperature?: number;
  };
  location?: {
    type?: string;
    description?: string;
  };
  socialInteraction?: {
    level?: number;
    quality?: number;
  };
  medications?: {
    taken?: boolean;
    names?: string[];
    notes?: string;
  };
  symptoms?: string[];
  gratitude?: string[];
  goals?: {
    achieved?: string[];
    working_on?: string[];
  };
  isPrivate?: boolean;
  metadata?: {
    source?: string;
    deviceType?: string;
    timeZone?: string;
  };
}

export interface MoodAnalytics {
  summary: {
    averageMood: number;
    averageEnergy: number;
    averageAnxiety: number;
    totalEntries: number;
    period: string;
  };
  trends: Array<{
    _id: string; // date
    avgMood: number;
    avgEnergy: number;
    avgAnxiety: number;
    entryCount: number;
  }>;
  insights: {
    topTags: Array<[string, number]>;
    topTriggers: Array<[string, number]>;
    topActivities: Array<[string, number]>;
  };
}

export interface MoodEntriesResponse {
  entries: MoodEntry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface MoodQueryParams {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  mood?: number;
  tags?: string | string[];
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

class MoodAPI {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/mood`;
  }

  /**
   * Create a new mood entry
   */
  async createMoodEntry(
    data: CreateMoodEntryData,
    token: string
  ): Promise<MoodEntry> {
    const response = await fetch(this.baseUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to create mood entry");
    }

    return result.data;
  }

  /**
   * Get mood entries with filtering and pagination
   */
  async getMoodEntries(
    params: MoodQueryParams = {},
    token: string
  ): Promise<MoodEntriesResponse> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((v) => queryParams.append(key, v.toString()));
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    const url = queryParams.toString()
      ? `${this.baseUrl}?${queryParams}`
      : this.baseUrl;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to fetch mood entries");
    }

    return result.data;
  }

  /**
   * Get a specific mood entry
   */
  async getMoodEntry(entryId: string, token: string): Promise<MoodEntry> {
    const response = await fetch(`${this.baseUrl}/${entryId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to fetch mood entry");
    }

    return result.data;
  }

  /**
   * Update a mood entry
   */
  async updateMoodEntry(
    entryId: string,
    data: Partial<CreateMoodEntryData>,
    token: string
  ): Promise<MoodEntry> {
    const response = await fetch(`${this.baseUrl}/${entryId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to update mood entry");
    }

    return result.data;
  }

  /**
   * Delete a mood entry
   */
  async deleteMoodEntry(entryId: string, token: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${entryId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to delete mood entry");
    }
  }

  /**
   * Get mood analytics and insights
   */
  async getMoodAnalytics(
    period: "7d" | "30d" | "90d" | "1y" = "30d",
    token: string
  ): Promise<MoodAnalytics> {
    const response = await fetch(`${this.baseUrl}/analytics?period=${period}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || "Failed to fetch mood analytics");
    }

    return result.data;
  }

  /**
   * Get mood options for UI
   */
  getMoodOptions() {
    return [
      { value: 1, emoji: "😔", label: "Very Sad", color: "text-blue-600" },
      { value: 2, emoji: "😟", label: "Sad", color: "text-blue-500" },
      { value: 3, emoji: "😐", label: "Neutral", color: "text-gray-500" },
      { value: 4, emoji: "🙂", label: "Happy", color: "text-green-500" },
      { value: 5, emoji: "😄", label: "Very Happy", color: "text-green-600" },
    ];
  }

  /**
   * Get energy level options for UI
   */
  getEnergyOptions() {
    return [
      { value: 1, emoji: "🔋", label: "Very Low", color: "text-red-600" },
      { value: 2, emoji: "🔋", label: "Low", color: "text-orange-500" },
      { value: 3, emoji: "🔋", label: "Moderate", color: "text-yellow-500" },
      { value: 4, emoji: "🔋", label: "High", color: "text-green-500" },
      { value: 5, emoji: "⚡", label: "Very High", color: "text-green-600" },
    ];
  }

  /**
   * Get anxiety level options for UI
   */
  getAnxietyOptions() {
    return [
      { value: 1, emoji: "😌", label: "Very Calm", color: "text-green-600" },
      { value: 2, emoji: "🙂", label: "Calm", color: "text-green-500" },
      { value: 3, emoji: "😐", label: "Neutral", color: "text-yellow-500" },
      { value: 4, emoji: "😰", label: "Anxious", color: "text-orange-500" },
      { value: 5, emoji: "😱", label: "Very Anxious", color: "text-red-600" },
    ];
  }

  /**
   * Get common mood tags
   */
  getCommonTags() {
    return [
      "work",
      "family",
      "friends",
      "health",
      "exercise",
      "sleep",
      "stress",
      "relaxation",
      "social",
      "alone",
      "creative",
      "productive",
      "tired",
      "energetic",
      "grateful",
      "worried",
      "excited",
      "calm",
      "overwhelmed",
    ];
  }

  /**
   * Get common triggers
   */
  getCommonTriggers() {
    return [
      "work stress",
      "relationship issues",
      "health concerns",
      "financial worry",
      "lack of sleep",
      "social situations",
      "weather",
      "news",
      "deadlines",
      "conflict",
      "change",
      "uncertainty",
      "isolation",
      "overwhelm",
    ];
  }

  /**
   * Get common activities
   */
  getCommonActivities() {
    return [
      "exercise",
      "meditation",
      "reading",
      "music",
      "socializing",
      "work",
      "cooking",
      "walking",
      "therapy",
      "journaling",
      "art",
      "gaming",
      "watching TV",
      "shopping",
      "cleaning",
      "studying",
      "volunteering",
    ];
  }

  /**
   * Get personalized feedback based on mood entry and user history
   */
  async getPersonalizedFeedback(
    moodEntry: CreateMoodEntryData,
    token: string
  ): Promise<string> {
    const response = await fetch(`${this.baseUrl}/feedback`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(moodEntry),
    });

    if (!response.ok) {
      throw new Error("Failed to get personalized feedback");
    }

    const data = await response.json();
    return data.data.feedback;
  }

  /**
   * Get resource suggestions based on mood entry
   */
  async getResourceSuggestions(
    moodEntry: CreateMoodEntryData,
    token: string
  ): Promise<any[]> {
    const response = await fetch(`${this.baseUrl}/suggestions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(moodEntry),
    });

    if (!response.ok) {
      throw new Error("Failed to get resource suggestions");
    }

    const data = await response.json();
    return data.data.suggestions;
  }
}

export const moodAPI = new MoodAPI();

const { MongoClient } = require("mongodb");

async function debugAuth() {
  const client = new MongoClient("mongodb://localhost:27017");

  try {
    await client.connect();
    const db = client.db("theramea");

    // Check for the specific session
    const session = await db
      .collection("sessions")
      .findOne({ _id: new ObjectId("689e688440af152213173417") });
    console.log("Session found:", session);

    // Check recent sessions
    const recentSessions = await db
      .collection("sessions")
      .find({})
      .sort({ _id: -1 })
      .limit(5)
      .toArray();
    console.log(
      "Recent sessions:",
      recentSessions.map((s) => ({
        _id: s._id,
        userId: s.userId,
        counselorId: s.counselorId,
        status: s.status,
        paymentStatus: s.paymentStatus,
      }))
    );

    // Check users
    const users = await db.collection("users").find({}).limit(5).toArray();
    console.log(
      "Recent users:",
      users.map((u) => ({
        _id: u._id,
        email: u.email,
        role: u.role,
      }))
    );
  } catch (error) {
    console.error("Error:", error);
  } finally {
    await client.close();
  }
}

debugAuth();

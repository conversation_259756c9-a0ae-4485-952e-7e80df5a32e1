const mongoose = require("mongoose");

const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/theramea_dev";

const resourceSchema = new mongoose.Schema({}, { strict: false });
const Resource = mongoose.model("Resource", resourceSchema);

async function listResources() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    const resources = await Resource.find(
      {},
      "_id title seo.slug seoData.slug"
    ).lean();

    console.log("📚 Available Resources:");
    resources.forEach((doc, i) => {
      console.log(`${i + 1}. ID: ${doc._id}`);
      console.log(`   Title: ${doc.title}`);
      console.log(
        `   Slug: ${doc.seo?.slug || doc.seoData?.slug || "no-slug"}`
      );
      console.log("");
    });

    console.log("🔗 Test URLs:");
    resources.forEach((doc) => {
      console.log(`- http://localhost:5000/api/resources/${doc._id}`);
      if (doc.seo?.slug || doc.seoData?.slug) {
        console.log(
          `- http://localhost:5000/api/resources/${
            doc.seo?.slug || doc.seoData?.slug
          }`
        );
      }
    });
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

listResources();

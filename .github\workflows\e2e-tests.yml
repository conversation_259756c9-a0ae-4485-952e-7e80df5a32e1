name: E2E Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run tests daily at 6 AM UTC
    - cron: "0 6 * * *"

env:
  NODE_VERSION: "18"

jobs:
  e2e-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies (Frontend)
        working-directory: ./frontend
        run: npm ci

      - name: Install dependencies (Backend)
        working-directory: ./backend
        run: npm ci

      - name: Install Playwright Browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Build Frontend
        working-directory: ./frontend
        run: npm run build

      - name: Start Backend
        working-directory: ./backend
        run: |
          npm run build
          npm start &
          # Wait for backend to be ready
          npx wait-on http://localhost:5000/api/health --timeout 60000
        env:
          NODE_ENV: test
          PORT: 5000

      - name: Start Frontend
        working-directory: ./frontend
        run: |
          npm start &
          # Wait for frontend to be ready
          npx wait-on http://localhost:3000 --timeout 60000
        env:
          NODE_ENV: production
          PORT: 3000

      - name: Run E2E tests
        working-directory: ./frontend
        run: npx playwright test --project=${{ matrix.browser }}
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report-${{ matrix.browser }}
          path: frontend/test-results/
          retention-days: 30

      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-html-report-${{ matrix.browser }}
          path: frontend/playwright-report/
          retention-days: 30

  # Smoke tests for quick feedback
  smoke-tests:
    timeout-minutes: 15
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies (Frontend)
        working-directory: ./frontend
        run: npm ci

      - name: Install dependencies (Backend)
        working-directory: ./backend
        run: npm ci

      - name: Install Playwright Browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps chromium

      - name: Build and Start Services
        run: |
          # Start backend
          cd backend && npm run build && npm start &

          # Start frontend
          cd frontend && npm run build && npm start &

          # Wait for services to be ready
          npx wait-on http://localhost:5000/api/health --timeout 60000
          npx wait-on http://localhost:3000 --timeout 60000
        env:
          NODE_ENV: test

      - name: Run Smoke Tests
        working-directory: ./frontend
        run: npx playwright test smoke.spec.ts --project=chromium
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

  # Mobile-specific tests
  mobile-tests:
    timeout-minutes: 45
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Install dependencies (Backend)
        working-directory: ./backend
        run: npm ci

      - name: Install Playwright Browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps chromium

      - name: Start Services
        run: |
          cd backend && npm start &
          cd frontend && npm start &
          npx wait-on http://localhost:5000/api/health --timeout 60000
          npx wait-on http://localhost:3000 --timeout 60000
        env:
          NODE_ENV: test

      - name: Run Mobile Tests
        working-directory: ./frontend
        run: npx playwright test --project="Mobile Chrome" --project="Mobile Safari"
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: Upload mobile test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-mobile-report
          path: frontend/test-results/
          retention-days: 30

  # Accessibility tests
  accessibility-tests:
    timeout-minutes: 30
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        working-directory: ./frontend
        run: |
          npm ci
          npm install --save-dev @axe-core/playwright

      - name: Install dependencies (Backend)
        working-directory: ./backend
        run: npm ci

      - name: Install Playwright Browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps chromium

      - name: Start Services
        run: |
          cd backend && npm start &
          cd frontend && npm start &
          npx wait-on http://localhost:5000/api/health --timeout 60000
          npx wait-on http://localhost:3000 --timeout 60000
        env:
          NODE_ENV: test

      - name: Run Accessibility Tests
        working-directory: ./frontend
        run: |
          # Create a simple accessibility test
          npx playwright test --grep "accessibility"
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

  # Test results aggregation
  test-results:
    if: always()
    needs: [e2e-tests, smoke-tests, mobile-tests]
    runs-on: ubuntu-latest

    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Display test results
        run: |
          echo "## Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Smoke Tests: ${{ needs.smoke-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "### E2E Tests: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "### Mobile Tests: ${{ needs.mobile-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ -d "playwright-report-chromium" ]; then
            echo "Chromium test artifacts available" >> $GITHUB_STEP_SUMMARY
          fi

          if [ -d "playwright-report-firefox" ]; then
            echo "Firefox test artifacts available" >> $GITHUB_STEP_SUMMARY
          fi

          if [ -d "playwright-report-webkit" ]; then
            echo "WebKit test artifacts available" >> $GITHUB_STEP_SUMMARY
          fi

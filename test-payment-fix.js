const axios = require("axios");

const BASE_URL = "http://localhost:5000/api";

// Test user credentials
const TEST_USER = {
  email: "<EMAIL>",
  password: "password123",
};

let authToken = null;

// Authenticate user
async function authenticateUser() {
  try {
    console.log("🔐 Authenticating user...");

    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password,
    });

    if (response.data.success && response.data.data.tokens) {
      authToken = response.data.data.tokens.accessToken;
      console.log("✅ Authentication successful!");
      return true;
    }
  } catch (error) {
    console.error(
      "❌ Authentication failed:",
      error.response?.data || error.message
    );
    return false;
  }
}

// Test the original payment initialization without request body (like frontend does)
async function testOriginalFrontendCall() {
  const sessionId = "689e7d8b8d814e4aae4767af";

  console.log(
    `\n💳 Testing frontend-style call (no request body) for session: ${sessionId}`
  );

  try {
    const response = await axios.post(
      `${BASE_URL}/sessions/${sessionId}/payment/initialize`,
      {}, // Empty body - exactly how frontend calls it
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ SUCCESS - Payment initialization worked!");
    console.log("Response:", {
      status: response.status,
      message: response.data.message,
      hasAuthUrl: !!response.data.data?.authorization_url,
      reference: response.data.data?.reference,
    });

    if (response.data.data?.authorization_url) {
      console.log("🔗 Payment URL generated successfully");
    }
  } catch (error) {
    if (error.response) {
      console.log(`❌ Failed with status: ${error.response.status}`);
      console.log("Error:", error.response.data);

      if (error.response.status === 403) {
        console.log(
          "💡 Authorization issue: User doesn't own this session or session doesn't exist"
        );
      } else if (error.response.status === 404) {
        console.log("💡 Session not found");
      } else if (error.response.status === 400 && error.response.data.errors) {
        console.log(
          "💡 Validation errors still exist - backend fix didn't work"
        );
      }
    } else {
      console.log("❌ Network error:", error.message);
    }
  }
}

// Test with different request body variations
async function testDifferentRequestBodies() {
  const sessionId = "689e7d8b8d814e4aae4767af";

  console.log("\n🧪 Testing different request body variations:");

  const testCases = [
    { name: "Empty body", data: {} },
    { name: "Null body", data: null },
    { name: "No body", data: undefined },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n   Testing: ${testCase.name}`);

      const config = {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      };

      if (testCase.data !== undefined) {
        config.data = testCase.data;
      }

      const response = await axios.post(
        `${BASE_URL}/sessions/${sessionId}/payment/initialize`,
        testCase.data,
        config
      );

      console.log(`   ✅ ${testCase.name}: SUCCESS`);
    } catch (error) {
      console.log(
        `   ❌ ${testCase.name}: ${error.response?.status || error.message}`
      );
    }
  }
}

async function main() {
  console.log("🚀 Testing Payment Initialization Fix");
  console.log("=====================================\n");

  const isAuthenticated = await authenticateUser();
  if (!isAuthenticated) {
    console.log("Cannot proceed without authentication");
    return;
  }

  await testOriginalFrontendCall();
  await testDifferentRequestBodies();

  console.log("\n📋 Summary:");
  console.log("- Removed unnecessary validation from backend route");
  console.log(
    "- Payment initialization should now work with empty request body"
  );
  console.log("- Frontend can continue using current implementation");
  console.log(
    "- Controller gets all payment data from session, not request body"
  );
}

main().catch(console.error);

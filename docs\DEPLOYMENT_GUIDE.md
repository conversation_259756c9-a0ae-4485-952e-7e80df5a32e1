# Theramea Deployment Guide

This guide covers deploying Theramea to production environments including cloud platforms, containerization, and CI/CD setup.

## 🚀 Deployment Options

### 1. Cloud Platform Deployment

#### Vercel (Frontend) + Railway (Backend)

**Frontend (Vercel):**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy from frontend directory
cd frontend
vercel --prod

# Set environment variables in Vercel dashboard
NEXT_PUBLIC_API_URL=https://your-backend.railway.app
NEXT_PUBLIC_SOCKET_URL=https://your-backend.railway.app
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_live_your-key
NEXT_PUBLIC_DAILY_DOMAIN=your-daily-domain
```

**Backend (Railway):**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up

# Set environment variables
railway variables set NODE_ENV=production
railway variables set MONGODB_URI=mongodb+srv://...
railway variables set JWT_SECRET=your-production-secret
# ... other environment variables
```

#### AWS Deployment

**Frontend (S3 + CloudFront):**
```bash
# Build the application
cd frontend
npm run build
npm run export

# Deploy to S3
aws s3 sync out/ s3://your-bucket-name --delete
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

**Backend (EC2 + RDS + ElastiCache):**
```bash
# Create EC2 instance and install dependencies
sudo yum update -y
sudo yum install -y nodejs npm git

# Clone and setup application
git clone https://github.com/your-org/theramea.git
cd theramea/backend
npm install
npm run build

# Setup PM2 for process management
npm install -g pm2
pm2 start dist/app.js --name "theramea-api"
pm2 startup
pm2 save
```

### 2. Docker Deployment

**Docker Compose (Development):**
```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:5000
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/theramea
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"

volumes:
  mongo_data:
```

**Production Docker Compose:**
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:3000"
    environment:
      - NEXT_PUBLIC_API_URL=https://api.theramea.com
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    restart: unless-stopped
    depends_on:
      - mongo
      - redis

  mongo:
    image: mongo:5.0
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=secure_password
    volumes:
      - mongo_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    restart: unless-stopped

  redis:
    image: redis:6.2-alpine
    command: redis-server --requirepass secure_redis_password
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  mongo_data:
  redis_data:
```

### 3. Kubernetes Deployment

**Namespace:**
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: theramea
```

**ConfigMap:**
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: theramea-config
  namespace: theramea
data:
  NODE_ENV: "production"
  MONGODB_URI: "mongodb://mongo-service:27017/theramea"
  REDIS_URL: "redis://redis-service:6379"
```

**Secrets:**
```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: theramea-secrets
  namespace: theramea
type: Opaque
data:
  JWT_SECRET: <base64-encoded-secret>
  PAYSTACK_SECRET_KEY: <base64-encoded-key>
  CLOUDINARY_API_SECRET: <base64-encoded-secret>
```

**Backend Deployment:**
```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: theramea
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: theramea/backend:latest
        ports:
        - containerPort: 5000
        envFrom:
        - configMapRef:
            name: theramea-config
        - secretRef:
            name: theramea-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 🔧 Environment Configuration

### Production Environment Variables

**Backend (.env.production):**
```env
NODE_ENV=production
PORT=5000

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/theramea?retryWrites=true&w=majority
REDIS_URL=redis://username:password@redis-host:6379

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-key-min-32-chars
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# File Storage
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Payment
PAYSTACK_SECRET_KEY=sk_live_your-live-secret-key
PAYSTACK_PUBLIC_KEY=pk_live_your-live-public-key
PAYSTACK_WEBHOOK_SECRET=your-webhook-secret

# Video Calls
DAILY_API_KEY=your-daily-api-key
DAILY_DOMAIN=your-daily-domain.daily.co

# Email
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>

# Security
CORS_ORIGIN=https://theramea.com,https://www.theramea.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info
```

**Frontend (.env.production):**
```env
NEXT_PUBLIC_API_URL=https://api.theramea.com
NEXT_PUBLIC_SOCKET_URL=https://api.theramea.com
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_live_your-live-public-key
NEXT_PUBLIC_DAILY_DOMAIN=your-daily-domain.daily.co
NEXT_PUBLIC_SENTRY_DSN=your-frontend-sentry-dsn
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
```

## 🔒 Security Configuration

### SSL/TLS Setup

**Nginx Configuration:**
```nginx
# nginx.conf
server {
    listen 80;
    server_name theramea.com www.theramea.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name theramea.com www.theramea.com;

    ssl_certificate /etc/nginx/ssl/theramea.com.crt;
    ssl_certificate_key /etc/nginx/ssl/theramea.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Frontend
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API
    location /api/ {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://backend:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Database Security

**MongoDB Security:**
```javascript
// mongo-init.js
db = db.getSiblingDB('theramea');
db.createUser({
  user: 'theramea_user',
  pwd: 'secure_database_password',
  roles: [
    {
      role: 'readWrite',
      db: 'theramea'
    }
  ]
});

// Create indexes for performance
db.users.createIndex({ email: 1 }, { unique: true });
db.sessions.createIndex({ scheduledAt: 1 });
db.sessions.createIndex({ counselorId: 1, status: 1 });
db.chatRooms.createIndex({ isActive: 1, category: 1 });
db.messages.createIndex({ roomId: 1, createdAt: -1 });
```

## 📊 Monitoring & Logging

### Health Checks

**Backend Health Endpoint:**
```typescript
// health.ts
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    services: {
      database: 'unknown',
      redis: 'unknown',
      paystack: 'unknown',
      daily: 'unknown'
    }
  };

  try {
    // Check MongoDB
    await mongoose.connection.db.admin().ping();
    health.services.database = 'ok';
  } catch (error) {
    health.services.database = 'error';
    health.status = 'degraded';
  }

  try {
    // Check Redis
    await redisClient.ping();
    health.services.redis = 'ok';
  } catch (error) {
    health.services.redis = 'error';
    health.status = 'degraded';
  }

  res.status(health.status === 'ok' ? 200 : 503).json(health);
});
```

### Logging Configuration

**Winston Logger Setup:**
```typescript
// logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'theramea-api' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

export default logger;
```

## 🚀 CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci
      
      - name: Run tests
        run: |
          cd backend && npm test
          cd ../frontend && npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to production
        run: |
          # Add your deployment commands here
          echo "Deploying to production..."
```

## 📋 Pre-deployment Checklist

- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Database migrations run
- [ ] Redis cache configured
- [ ] Payment webhooks configured
- [ ] Email service configured
- [ ] Monitoring and logging setup
- [ ] Health checks working
- [ ] Security headers configured
- [ ] CORS properly configured
- [ ] Rate limiting enabled
- [ ] Backup strategy implemented
- [ ] CDN configured (if applicable)
- [ ] DNS records updated
- [ ] Load testing completed

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check MongoDB URI and credentials
   - Verify network connectivity
   - Check firewall rules

2. **Payment Webhook Failures**
   - Verify webhook URL is accessible
   - Check webhook secret configuration
   - Review Paystack dashboard logs

3. **Video Call Issues**
   - Verify Daily.co API key and domain
   - Check CORS configuration
   - Test room creation manually

4. **Socket.io Connection Issues**
   - Verify WebSocket support in load balancer
   - Check CORS configuration
   - Review client connection logs

For additional support, contact: <EMAIL>

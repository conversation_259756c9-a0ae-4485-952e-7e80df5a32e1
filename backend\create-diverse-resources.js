const mongoose = require("mongoose");

// MongoDB connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// Resource Schema (complete for testing)
const resourceSchema = new mongoose.Schema({
  title: String,
  description: String,
  content: String,
  type: {
    type: String,
    enum: ["article", "video", "audio", "tool", "worksheet", "guide"],
  },
  category: String,
  subcategory: String,
  tags: [String],
  difficulty: { type: String, enum: ["beginner", "intermediate", "advanced"] },
  estimatedReadTime: Number,
  estimatedDuration: Number,
  media: {
    thumbnailUrl: String,
    videoUrl: String,
    audioUrl: String,
    downloadUrl: String,
    fileSize: Number,
  },
  author: {
    name: String,
    credentials: String,
    bio: String,
    profilePicture: String,
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
  },
  statistics: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0 },
  },
  interactions: [
    {
      userId: String,
      action: {
        type: String,
        enum: ["view", "like", "bookmark", "share", "complete"],
      },
      timestamp: { type: Date, default: Date.now },
    },
  ],
  ratings: [
    {
      userId: String,
      rating: { type: Number, min: 1, max: 5 },
      review: String,
      createdAt: { type: Date, default: Date.now },
    },
  ],
  bookmarkedBy: [String],
  relatedResources: [String],
  prerequisites: [String],
  learningObjectives: [String],
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  isPremium: { type: Boolean, default: false },
  publishedAt: { type: Date, default: Date.now },
  lastUpdatedAt: { type: Date, default: Date.now },
  createdBy: String,
  reviewedBy: String,
  reviewedAt: Date,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Resource = mongoose.model("Resource", resourceSchema);

// Sample resources with different types and media
const sampleResources = [
  {
    title: "Introduction to Mindfulness Meditation",
    description:
      "Learn the basics of mindfulness meditation with this comprehensive video guide that covers breathing techniques and posture.",
    content: `<h2>Getting Started with Mindfulness</h2>
    <p>Mindfulness meditation is a powerful practice that can help reduce stress, improve focus, and enhance overall well-being.</p>
    <h3>What You'll Learn:</h3>
    <ul>
      <li>Basic breathing techniques</li>
      <li>Proper meditation posture</li>
      <li>How to handle distracting thoughts</li>
      <li>Building a daily practice</li>
    </ul>
    <p>This video provides step-by-step instructions that are perfect for beginners.</p>`,
    type: "video",
    category: "Mindfulness",
    subcategory: "Meditation",
    tags: ["meditation", "mindfulness", "stress-relief", "beginner"],
    difficulty: "beginner",
    estimatedDuration: 15,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",
      videoUrl:
        "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
      fileSize: 1048576,
    },
    author: {
      name: "Dr. Sarah Chen",
      credentials: "PhD in Psychology, Certified Mindfulness Instructor",
      bio: "Dr. Chen has over 10 years of experience teaching mindfulness and meditation.",
    },
    seo: {
      metaTitle: "Learn Mindfulness Meditation - Beginner Video Guide",
      metaDescription:
        "Master the basics of mindfulness meditation with this 15-minute video guide perfect for beginners.",
      keywords: [
        "mindfulness",
        "meditation",
        "stress relief",
        "beginner guide",
      ],
      slug: "mindfulness-meditation-video-guide",
    },
    statistics: {
      views: 1523,
      likes: 234,
      bookmarks: 89,
      averageRating: 4.7,
      totalRatings: 156,
    },
    bookmarkedBy: [],
    ratings: [],
    learningObjectives: [
      "Understand basic mindfulness principles",
      "Learn proper meditation posture",
      "Practice basic breathing techniques",
    ],
  },
  {
    title: "Calming Nature Sounds for Sleep",
    description:
      "A 30-minute audio track featuring gentle rain and forest sounds to help you relax and fall asleep naturally.",
    content: `<h2>Peaceful Sleep with Nature</h2>
    <p>This carefully crafted audio experience combines gentle rainfall with subtle forest ambience to create the perfect sleep environment.</p>
    <h3>Features:</h3>
    <ul>
      <li>30 minutes of continuous, seamless audio</li>
      <li>High-quality nature recordings</li>
      <li>No sudden loud sounds or interruptions</li>
      <li>Scientifically designed for relaxation</li>
    </ul>
    <p>Listen with headphones or speakers at a comfortable volume. Allow the natural sounds to guide you into deep, restful sleep.</p>`,
    type: "audio",
    category: "Sleep",
    subcategory: "Sleep Aids",
    tags: ["sleep", "relaxation", "nature-sounds", "insomnia"],
    difficulty: "beginner",
    estimatedDuration: 30,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      fileSize: 2097152,
    },
    author: {
      name: "Sound Therapy Institute",
      credentials: "Certified Sound Therapists",
      bio: "Specialists in creating therapeutic audio experiences for wellness and relaxation.",
    },
    seo: {
      metaTitle: "Nature Sounds for Sleep - 30 Min Relaxation Audio",
      metaDescription:
        "Fall asleep naturally with this 30-minute nature sounds audio featuring rain and forest ambience.",
      keywords: ["nature sounds", "sleep aid", "relaxation", "rain sounds"],
      slug: "nature-sounds-sleep-audio",
    },
    statistics: {
      views: 892,
      likes: 167,
      bookmarks: 203,
      averageRating: 4.9,
      totalRatings: 78,
    },
    bookmarkedBy: [],
    ratings: [],
    learningObjectives: [
      "Achieve deeper relaxation",
      "Improve sleep quality",
      "Reduce bedtime anxiety",
    ],
  },
  {
    title: "Daily Mood Tracking Worksheet",
    description:
      "A comprehensive worksheet to help you track your daily moods, identify patterns, and improve emotional awareness.",
    content: `<h2>Track Your Emotional Journey</h2>
    <p>This mood tracking worksheet is designed to help you become more aware of your emotional patterns and triggers.</p>
    <h3>What's Included:</h3>
    <ul>
      <li>30-day mood tracking calendar</li>
      <li>Emotional awareness exercises</li>
      <li>Pattern identification guide</li>
      <li>Reflection prompts</li>
      <li>Goal setting section</li>
    </ul>
    <h3>How to Use:</h3>
    <p>Download and print this worksheet, then spend 5-10 minutes each day recording your mood and any relevant notes. After a week, review your entries to identify patterns.</p>
    <p><strong>Best used in conjunction with therapy or counseling for maximum benefit.</strong></p>`,
    type: "worksheet",
    category: "Mental Health",
    subcategory: "Self-Assessment",
    tags: ["mood-tracking", "self-awareness", "mental-health", "worksheet"],
    difficulty: "beginner",
    estimatedReadTime: 5,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=600&fit=crop",
      downloadUrl:
        "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      fileSize: 524288,
    },
    author: {
      name: "Dr. Michael Rodriguez",
      credentials: "Licensed Clinical Psychologist",
      bio: "Specializing in cognitive behavioral therapy and emotional wellness.",
    },
    seo: {
      metaTitle: "Daily Mood Tracking Worksheet - Free Download",
      metaDescription:
        "Download this comprehensive mood tracking worksheet to improve emotional awareness and identify patterns.",
      keywords: [
        "mood tracking",
        "worksheet",
        "mental health",
        "emotional awareness",
      ],
      slug: "daily-mood-tracking-worksheet",
    },
    statistics: {
      views: 2156,
      likes: 345,
      bookmarks: 412,
      averageRating: 4.6,
      totalRatings: 203,
    },
    bookmarkedBy: [],
    ratings: [],
    learningObjectives: [
      "Develop emotional awareness",
      "Identify mood patterns",
      "Track progress over time",
    ],
  },
  {
    title: "Anxiety Assessment Tool",
    description:
      "An interactive self-assessment tool to help you understand your anxiety levels and get personalized recommendations.",
    content: `<h2>Understanding Your Anxiety</h2>
    <p>This interactive tool uses validated psychological assessments to help you understand your anxiety levels and patterns.</p>
    <h3>Assessment Features:</h3>
    <ul>
      <li>Comprehensive anxiety questionnaire</li>
      <li>Immediate scoring and results</li>
      <li>Personalized recommendations</li>
      <li>Resource suggestions based on your results</li>
      <li>Progress tracking over time</li>
    </ul>
    <h3>How It Works:</h3>
    <p>The tool asks a series of questions about your thoughts, feelings, and behaviors. Your responses are scored using established psychological frameworks to provide insights into your anxiety patterns.</p>
    <p><em>Note: This tool is for educational purposes and is not a substitute for professional mental health diagnosis or treatment.</em></p>`,
    type: "tool",
    category: "Mental Health",
    subcategory: "Assessment",
    tags: ["anxiety", "assessment", "self-help", "interactive"],
    difficulty: "intermediate",
    estimatedDuration: 10,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop",
      downloadUrl:
        "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      fileSize: 1048576,
    },
    author: {
      name: "Theramea Clinical Team",
      credentials: "Licensed Mental Health Professionals",
      bio: "A team of psychologists and therapists dedicated to creating evidence-based tools.",
    },
    seo: {
      metaTitle: "Anxiety Assessment Tool - Free Interactive Self-Assessment",
      metaDescription:
        "Take this comprehensive anxiety assessment to understand your anxiety levels and get personalized recommendations.",
      keywords: [
        "anxiety assessment",
        "mental health tool",
        "self-assessment",
        "anxiety test",
      ],
      slug: "anxiety-assessment-tool",
    },
    statistics: {
      views: 3421,
      likes: 567,
      bookmarks: 789,
      averageRating: 4.8,
      totalRatings: 432,
    },
    bookmarkedBy: [],
    ratings: [],
    learningObjectives: [
      "Assess current anxiety levels",
      "Understand anxiety patterns",
      "Receive personalized recommendations",
    ],
  },
  {
    title: "Complete Guide to Building Healthy Habits",
    description:
      "A comprehensive step-by-step guide to understanding habit formation and building lasting positive changes in your life.",
    content: `<h1>The Science of Habit Formation</h1>
    <p>Building healthy habits is one of the most powerful ways to improve your mental and physical well-being. This comprehensive guide will teach you the science behind habits and provide practical strategies for lasting change.</p>
    
    <h2>Understanding the Habit Loop</h2>
    <p>Every habit consists of three components:</p>
    <ol>
      <li><strong>Cue:</strong> The trigger that initiates the behavior</li>
      <li><strong>Routine:</strong> The behavior itself</li>
      <li><strong>Reward:</strong> The benefit you gain from the behavior</li>
    </ol>
    
    <h2>The 21-Day Myth</h2>
    <p>Contrary to popular belief, habits don't form in 21 days. Research shows it takes an average of 66 days to form a new habit, with a range of 18-254 days depending on the complexity.</p>
    
    <h2>Strategies for Success</h2>
    <h3>1. Start Small</h3>
    <p>Begin with tiny habits that are easy to maintain. Want to exercise? Start with 5 pushups, not an hour at the gym.</p>
    
    <h3>2. Stack Your Habits</h3>
    <p>Link new habits to existing ones. "After I brush my teeth, I will meditate for 2 minutes."</p>
    
    <h3>3. Design Your Environment</h3>
    <p>Make good habits obvious and bad habits invisible. Place your running shoes by your bed if you want to exercise in the morning.</p>
    
    <h3>4. Track Your Progress</h3>
    <p>Use a habit tracker to monitor your consistency. Visual progress is motivating and helps identify patterns.</p>
    
    <h2>Common Pitfalls to Avoid</h2>
    <ul>
      <li>Setting unrealistic expectations</li>
      <li>Trying to change too many habits at once</li>
      <li>Focusing on outcomes instead of systems</li>
      <li>Giving up after a few setbacks</li>
    </ul>
    
    <h2>Building Your Habit Plan</h2>
    <p>Follow these steps to create your personalized habit-building plan:</p>
    <ol>
      <li>Choose one specific habit to focus on</li>
      <li>Make it as small as possible</li>
      <li>Identify your cue and reward</li>
      <li>Track your progress daily</li>
      <li>Celebrate small wins</li>
    </ol>
    
    <h2>Conclusion</h2>
    <p>Remember, building habits is a marathon, not a sprint. Be patient with yourself, stay consistent, and focus on progress over perfection.</p>`,
    type: "guide",
    category: "Personal Development",
    subcategory: "Habit Formation",
    tags: ["habits", "personal-development", "behavior-change", "psychology"],
    difficulty: "intermediate",
    estimatedReadTime: 12,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=800&h=600&fit=crop",
      downloadUrl:
        "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      fileSize: 2097152,
    },
    author: {
      name: "Dr. Lisa Thompson",
      credentials: "PhD in Behavioral Psychology",
      bio: "Expert in habit formation and behavioral change with 15 years of research experience.",
    },
    seo: {
      metaTitle:
        "Complete Guide to Building Healthy Habits - Evidence-Based Strategies",
      metaDescription:
        "Learn the science of habit formation and practical strategies to build lasting positive changes in your life.",
      keywords: [
        "habit formation",
        "healthy habits",
        "behavior change",
        "personal development",
      ],
      slug: "complete-guide-building-healthy-habits",
    },
    statistics: {
      views: 4567,
      likes: 892,
      bookmarks: 1234,
      averageRating: 4.9,
      totalRatings: 567,
    },
    bookmarkedBy: [],
    ratings: [],
    learningObjectives: [
      "Understand the science of habit formation",
      "Learn practical habit-building strategies",
      "Create a personalized habit plan",
      "Avoid common habit-building mistakes",
    ],
  },
  {
    title: "Understanding Depression: Signs and Support",
    description:
      "A comprehensive article that explains depression symptoms, causes, and available support options for those seeking help.",
    content: `<h1>Understanding Depression: A Comprehensive Overview</h1>
    <p>Depression is more than just feeling sad or going through a rough patch. It's a serious mental health condition that requires understanding, treatment, and support.</p>
    
    <h2>What is Depression?</h2>
    <p>Depression, also known as major depressive disorder, is a mental health condition characterized by persistent feelings of sadness, hopelessness, and a loss of interest in activities that were once enjoyable.</p>
    
    <h2>Common Signs and Symptoms</h2>
    <h3>Emotional Symptoms:</h3>
    <ul>
      <li>Persistent sadness or emptiness</li>
      <li>Feelings of hopelessness or pessimism</li>
      <li>Irritability or restlessness</li>
      <li>Feelings of guilt, worthlessness, or helplessness</li>
      <li>Loss of interest in hobbies and activities</li>
    </ul>
    
    <h3>Physical Symptoms:</h3>
    <ul>
      <li>Fatigue or decreased energy</li>
      <li>Changes in appetite or weight</li>
      <li>Sleep disturbances</li>
      <li>Difficulty concentrating</li>
      <li>Physical aches and pains</li>
    </ul>
    
    <h2>Causes and Risk Factors</h2>
    <p>Depression can result from a combination of factors:</p>
    <ul>
      <li><strong>Biological factors:</strong> Brain chemistry, genetics, hormones</li>
      <li><strong>Psychological factors:</strong> Trauma, stress, personality traits</li>
      <li><strong>Environmental factors:</strong> Life events, relationships, work stress</li>
    </ul>
    
    <h2>Types of Depression</h2>
    <ul>
      <li><strong>Major Depressive Disorder:</strong> The most common type</li>
      <li><strong>Persistent Depressive Disorder:</strong> Long-term, chronic depression</li>
      <li><strong>Seasonal Affective Disorder:</strong> Depression related to changes in seasons</li>
      <li><strong>Postpartum Depression:</strong> Depression following childbirth</li>
    </ul>
    
    <h2>Treatment Options</h2>
    <h3>Professional Treatment:</h3>
    <ul>
      <li>Psychotherapy (talk therapy)</li>
      <li>Medication (antidepressants)</li>
      <li>Combination therapy</li>
      <li>Alternative treatments (TMS, ECT for severe cases)</li>
    </ul>
    
    <h3>Self-Care Strategies:</h3>
    <ul>
      <li>Regular exercise</li>
      <li>Healthy sleep habits</li>
      <li>Balanced nutrition</li>
      <li>Social support</li>
      <li>Stress management techniques</li>
    </ul>
    
    <h2>When to Seek Help</h2>
    <p>If you experience symptoms for more than two weeks, or if they interfere with your daily life, it's important to seek professional help. Don't wait for symptoms to worsen.</p>
    
    <h2>Supporting Someone with Depression</h2>
    <ul>
      <li>Listen without judgment</li>
      <li>Encourage professional help</li>
      <li>Be patient and understanding</li>
      <li>Help with daily activities when needed</li>
      <li>Take care of your own mental health</li>
    </ul>
    
    <h2>Resources and Support</h2>
    <p>Remember, depression is treatable, and recovery is possible. Reach out to mental health professionals, support groups, or crisis hotlines when you need help.</p>
    
    <p><em>If you're having thoughts of self-harm, please reach out for immediate help through a crisis hotline or emergency services.</em></p>`,
    type: "article",
    category: "Mental Health",
    subcategory: "Depression",
    tags: ["depression", "mental-health", "support", "awareness"],
    difficulty: "beginner",
    estimatedReadTime: 8,
    media: {
      thumbnailUrl:
        "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&h=600&fit=crop",
    },
    author: {
      name: "Dr. Emma Williams",
      credentials: "MD, Psychiatrist",
      bio: "Board-certified psychiatrist with expertise in mood disorders and depression treatment.",
    },
    seo: {
      metaTitle:
        "Understanding Depression: Signs, Symptoms, and Support Options",
      metaDescription:
        "Learn about depression symptoms, causes, and treatment options. Find support and resources for mental health.",
      keywords: [
        "depression",
        "mental health",
        "symptoms",
        "treatment",
        "support",
      ],
      slug: "understanding-depression-signs-support",
    },
    statistics: {
      views: 5234,
      likes: 678,
      bookmarks: 892,
      averageRating: 4.8,
      totalRatings: 345,
    },
    bookmarkedBy: [],
    ratings: [],
    learningObjectives: [
      "Recognize signs and symptoms of depression",
      "Understand causes and risk factors",
      "Learn about treatment options",
      "Know when and how to seek help",
    ],
  },
];

async function createResources() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    // Clear existing resources
    await Resource.deleteMany({});
    console.log("Cleared existing resources");

    // Insert new resources
    const insertedResources = await Resource.insertMany(sampleResources);
    console.log(`✅ Created ${insertedResources.length} diverse resources:`);

    insertedResources.forEach((resource, index) => {
      console.log(
        `${index + 1}. ${resource.title} (${resource.type.toUpperCase()})`
      );
      console.log(`   - Slug: ${resource.seo.slug}`);
      console.log(
        `   - Media: ${
          resource.media?.videoUrl
            ? "Video"
            : resource.media?.audioUrl
            ? "Audio"
            : resource.media?.downloadUrl
            ? "Download"
            : "None"
        }`
      );
      console.log("");
    });

    console.log("🎉 All resources created successfully!");
    console.log("\nResource URLs:");
    insertedResources.forEach((resource) => {
      console.log(`http://localhost:3000/resources/${resource.seo.slug}`);
    });
  } catch (error) {
    console.error("Error creating resources:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

createResources();

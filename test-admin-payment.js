const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

async function testAdminPayment() {
  try {
    console.log("=== Testing Admin User ===\n");

    // Try to login as admin
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: "<EMAIL>",
      password: "password123", // Most common test password
    });

    if (loginResponse.data.success) {
      const authToken = loginResponse.data.data.tokens.accessToken;
      console.log("✅ Admin login successful!");
      console.log(
        `User: ${loginResponse.data.data.user.firstName} ${loginResponse.data.data.user.lastName}`
      );
      console.log(`User ID: ${loginResponse.data.data.user._id}`);
      console.log(`Role: ${loginResponse.data.data.user.role}`);

      // Test payment initialization
      console.log("\n=== Testing Payment Initialization ===");
      const sessionId = "689e8ac358caf361f78ce110";

      const paymentResponse = await axios.post(
        `${API_BASE_URL}/sessions/${sessionId}/payment/initialize`,
        {},
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      console.log("✅ Payment initialization successful!");
      console.log(
        "Payment data:",
        JSON.stringify(paymentResponse.data, null, 2)
      );
    } else {
      console.log("❌ Admin login failed");
      console.log(loginResponse.data);
    }
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);

    if (
      error.response?.status === 401 &&
      error.response?.data?.error?.message?.includes(
        "Invalid email or password"
      )
    ) {
      console.log("\n💡 Trying alternative admin passwords...");

      // Try other common passwords
      const otherPasswords = [
        "admin123",
        "Admin123",
        "test123",
        "TestPassword123",
      ];

      for (const password of otherPasswords) {
        try {
          console.log(`Trying password: ${password}`);
          const altLoginResponse = await axios.post(
            `${API_BASE_URL}/auth/login`,
            {
              email: "<EMAIL>",
              password: password,
            }
          );

          if (altLoginResponse.data.success) {
            console.log(`✅ Success with password: ${password}`);
            const authToken = altLoginResponse.data.data.tokens.accessToken;

            // Test payment
            const paymentResponse = await axios.post(
              `${API_BASE_URL}/sessions/${sessionId}/payment/initialize`,
              {},
              {
                headers: {
                  Authorization: `Bearer ${authToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            console.log("✅ Payment initialization successful!");
            console.log(
              "Payment data:",
              JSON.stringify(paymentResponse.data, null, 2)
            );
            return;
          }
        } catch (altError) {
          console.log(`❌ Failed with ${password}`);
          if (
            altError.response?.data?.error?.message?.includes(
              "Too many authentication attempts"
            )
          ) {
            console.log("Rate limited, stopping password attempts");
            break;
          }
        }
      }
    }
  }
}

testAdminPayment();

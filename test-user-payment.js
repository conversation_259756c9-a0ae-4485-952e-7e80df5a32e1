const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

async function testUserPayment() {
  try {
    console.log("=== Testing User Payment ===\n");

    // Login as the session owner
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: "<EMAIL>",
      password: "Anambrastate@1",
    });

    if (loginResponse.data.success) {
      const authToken = loginResponse.data.data.tokens.accessToken;
      console.log("✅ Login successful!");
      console.log(
        `User: ${loginResponse.data.data.user.firstName} ${loginResponse.data.data.user.lastName}`
      );
      console.log(`User ID: ${loginResponse.data.data.user._id}`);
      console.log(`Role: ${loginResponse.data.data.user.role}`);

      // Test payment initialization
      console.log("\n=== Testing Payment Initialization ===");
      const sessionId = "689e8ac358caf361f78ce110";

      const paymentResponse = await axios.post(
        `${API_BASE_URL}/sessions/${sessionId}/payment/initialize`,
        {},
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      console.log("✅ Payment initialization successful!");
      console.log(
        "Payment data:",
        JSON.stringify(paymentResponse.data, null, 2)
      );
    } else {
      console.log("❌ Login failed");
      console.log(loginResponse.data);
    }
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);

    if (
      error.response?.status === 403 &&
      error.response?.data?.error?.message?.includes("Unauthorized to pay")
    ) {
      console.log("\n💡 The payment authorization issue persists.");
      console.log(
        "This might indicate the user ID in the session doesn't match the authenticated user."
      );
    }
  }
}

testUserPayment();

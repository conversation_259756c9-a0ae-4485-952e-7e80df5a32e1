const { MongoClient } = require("mongodb");

const MONGODB_URI =
  "******************************************************************";

async function checkCounselorAvailability() {
  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log("Connected to MongoDB");

    const db = client.db();
    const counselorsCollection = db.collection("counselors");

    // Get all counselors
    const counselors = await counselorsCollection.find({}).toArray();
    console.log(`Found ${counselors.length} counselors\n`);

    for (const counselor of counselors) {
      console.log(
        `=== ${counselor.basicInfo?.firstName || "Unknown"} ${
          counselor.basicInfo?.lastName || ""
        } ===`
      );
      console.log(`ID: ${counselor._id}`);
      console.log(
        `Verification Status: ${counselor.verification?.status || "Not set"}`
      );
      console.log(
        `Accepting New Clients: ${
          counselor.settings?.acceptingNewClients || "Not set"
        }`
      );

      if (counselor.availability) {
        console.log("Availability Schedule:");
        if (counselor.availability.schedule) {
          const schedule = counselor.availability.schedule;
          const days = [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
          ];

          for (const day of days) {
            if (schedule[day]) {
              console.log(
                `  ${day}: Available: ${
                  schedule[day].isAvailable
                }, Slots: ${JSON.stringify(schedule[day].timeSlots)}`
              );
            } else {
              console.log(`  ${day}: Not configured`);
            }
          }
        } else {
          console.log("  No schedule configured");
        }

        if (
          counselor.availability.unavailableDates &&
          counselor.availability.unavailableDates.length > 0
        ) {
          console.log(
            `Unavailable Dates: ${counselor.availability.unavailableDates.length} dates`
          );
        } else {
          console.log("No unavailable dates set");
        }
      } else {
        console.log("No availability data found");
      }
      console.log("---\n");
    }
  } catch (error) {
    console.error("❌ Error checking counselor availability:", error);
  } finally {
    await client.close();
  }
}

checkCounselorAvailability();

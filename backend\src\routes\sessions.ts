import { Router } from "express";
import { BookingController } from "@/controllers/bookingController";
import { sessionValidations, paramValidations } from "@/utils/validation";
import { authenticate, requireCounselor } from "@/middleware/auth";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// Public routes
router.get("/pricing", BookingController.calculatePricing);

router.get("/banks", BookingController.getSupportedBanks);

// Booking management
router.post(
  "/",
  authenticate,
  sessionValidations.book,
  validateRequest,
  BookingController.createBooking
);

router.get("/", authenticate, BookingController.getBookings);

router.get(
  "/:sessionId",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  BookingController.getBooking
);

// Payment routes
router.post(
  "/:sessionId/payment/initialize",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  BookingController.initializePayment
);

router.get("/payment/verify/:reference", BookingController.verifyPayment);

router.post("/payment/webhook", BookingController.handlePaymentWebhook);

// Booking actions
router.put(
  "/:sessionId/cancel",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  BookingController.cancelBooking
);

router.put(
  "/:sessionId/reschedule",
  authenticate,
  paramValidations.objectId("sessionId"),
  sessionValidations.reschedule,
  validateRequest,
  BookingController.rescheduleBooking
);

// Counselor actions
router.put(
  "/:sessionId/approve",
  authenticate,
  requireCounselor,
  paramValidations.objectId("sessionId"),
  validateRequest,
  BookingController.approveBooking
);

router.put(
  "/:sessionId/reject",
  authenticate,
  requireCounselor,
  paramValidations.objectId("sessionId"),
  validateRequest,
  BookingController.rejectBooking
);

export default router;

'use client';

import { Counselor, BookingRequest } from '@/types/counselor';
import { 
  StarIcon, 
  ClockIcon, 
  CurrencyDollarIcon,
  UserIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface CounselorSummaryProps {
  counselor: Counselor;
  bookingData: Partial<BookingRequest>;
}

export default function CounselorSummary({ counselor, bookingData }: CounselorSummaryProps) {
  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const calculateTotalCost = () => {
    if (!bookingData.duration) return 0;
    
    const baseRate = counselor.pricing.ratePerMinute * bookingData.duration;
    const urgentFee = bookingData.isUrgent ? baseRate * 0.25 : 0; // 25% urgent fee
    const platformFee = (baseRate + urgentFee) * 0.05; // 5% platform fee
    
    return {
      baseAmount: baseRate,
      urgentFee,
      platformFee,
      totalAmount: baseRate + urgentFee + platformFee,
    };
  };

  const pricing = calculateTotalCost();

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <div key={star}>
            {star <= rating ? (
              <StarIconSolid className="h-4 w-4 text-yellow-400" />
            ) : (
              <StarIcon className="h-4 w-4 text-gray-300" />
            )}
          </div>
        ))}
        <span className="text-sm text-gray-600 ml-1">
          ({counselor.statistics.totalReviews} reviews)
        </span>
      </div>
    );
  };

  const getSessionTypeLabel = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Individual Session';
      case 'couples':
        return 'Couples Session';
      case 'family':
        return 'Family Session';
      case 'group':
        return 'Group Session';
      default:
        return 'Session';
    }
  };

  return (
    <div className="space-y-6">
      {/* Counselor Info */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            {counselor.profile.profilePicture || counselor.user?.profilePicture ? (
              <img
                src={counselor.profile.profilePicture || counselor.user?.profilePicture}
                alt={`${counselor.user?.firstName} ${counselor.user?.lastName}`}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center">
                <UserIcon className="h-8 w-8 text-purple-600" />
              </div>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900">
              {counselor.user?.firstName} {counselor.user?.lastName}
            </h3>
            <p className="text-sm text-gray-600 mb-2">
              {counselor.specializations.slice(0, 2).join(', ')}
              {counselor.specializations.length > 2 && ` +${counselor.specializations.length - 2} more`}
            </p>
            
            {renderStars(counselor.statistics.averageRating)}
            
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <ClockIcon className="h-4 w-4" />
                <span>{counselor.experience.years} years exp.</span>
              </div>
              <div className="flex items-center space-x-1">
                <UserIcon className="h-4 w-4" />
                <span>{counselor.statistics.totalSessions} sessions</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Session Details */}
      {(bookingData.sessionType || bookingData.duration || bookingData.scheduledAt) && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Session Details</h4>
          
          <div className="space-y-3">
            {bookingData.sessionType && (
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium text-gray-900">
                  {getSessionTypeLabel(bookingData.sessionType)}
                </span>
              </div>
            )}
            
            {bookingData.duration && (
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium text-gray-900">
                  {bookingData.duration} minutes
                </span>
              </div>
            )}
            
            {bookingData.scheduledAt && (
              <div className="flex justify-between">
                <span className="text-gray-600">Date & Time:</span>
                <div className="text-right">
                  <div className="font-medium text-gray-900">
                    {new Date(bookingData.scheduledAt).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="text-sm text-gray-600">
                    {new Date(bookingData.scheduledAt).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            )}
            
            {bookingData.preferredLanguage && (
              <div className="flex justify-between">
                <span className="text-gray-600">Language:</span>
                <span className="font-medium text-gray-900 capitalize">
                  {bookingData.preferredLanguage === 'en' ? 'English' : bookingData.preferredLanguage}
                </span>
              </div>
            )}
            
            {bookingData.isUrgent && (
              <div className="flex justify-between">
                <span className="text-gray-600">Priority:</span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  Urgent Session
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Pricing Breakdown */}
      {bookingData.duration && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CurrencyDollarIcon className="h-5 w-5 mr-2" />
            Pricing
          </h4>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">
                Base Rate ({bookingData.duration} min × {formatCurrency(counselor.pricing.ratePerMinute, counselor.pricing.currency)}/min)
              </span>
              <span className="font-medium text-gray-900">
                {formatCurrency(pricing.baseAmount, counselor.pricing.currency)}
              </span>
            </div>
            
            {bookingData.isUrgent && pricing.urgentFee > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">Urgent Session Fee (25%)</span>
                <span className="font-medium text-gray-900">
                  {formatCurrency(pricing.urgentFee, counselor.pricing.currency)}
                </span>
              </div>
            )}
            
            <div className="flex justify-between">
              <span className="text-gray-600">Platform Fee (5%)</span>
              <span className="font-medium text-gray-900">
                {formatCurrency(pricing.platformFee, counselor.pricing.currency)}
              </span>
            </div>
            
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between">
                <span className="text-lg font-semibold text-gray-900">Total</span>
                <span className="text-lg font-bold text-purple-600">
                  {formatCurrency(pricing.totalAmount, counselor.pricing.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Policies */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-blue-900 mb-2">Important Notes</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Sessions can be cancelled up to 24 hours in advance for a full refund</li>
          <li>• Late cancellations (less than 24 hours) are subject to a 50% fee</li>
          <li>• No-shows will be charged the full session fee</li>
          <li>• Technical support is available during your session</li>
        </ul>
      </div>

      {/* Contact Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">Need Help?</h4>
        <p className="text-xs text-gray-600 mb-2">
          If you have questions about booking or need to reschedule, contact our support team.
        </p>
        <div className="text-xs text-gray-600">
          <p>📧 <EMAIL></p>
          <p>📞 +234 (0) ************</p>
        </div>
      </div>
    </div>
  );
}

// Test script to check counselors in database
require("dotenv").config();
const mongoose = require("mongoose");

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/theramea")
  .then(() => {
    console.log("Connected to MongoDB");

    // Simple schema for querying
    const counselorSchema = new mongoose.Schema(
      {},
      { collection: "counselors" }
    );
    const Counselor = mongoose.model("TestCounselor", counselorSchema);

    return Counselor.find({}).limit(5);
  })
  .then((counselors) => {
    console.log(`Found ${counselors.length} counselors in database:`);
    counselors.forEach((counselor, index) => {
      console.log(
        `${index + 1}. ID: ${counselor._id}, Active: ${counselor.isActive}`
      );
    });
    mongoose.disconnect();
  })
  .catch((error) => {
    console.error("Error:", error);
    mongoose.disconnect();
  });

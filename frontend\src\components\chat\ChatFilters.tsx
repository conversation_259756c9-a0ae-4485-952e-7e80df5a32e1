'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface ChatFiltersProps {
  filters: {
    category?: string;
    topic?: string;
    search?: string;
    sortBy?: 'recent' | 'popular' | 'participants';
  };
  onFilterChange: (filters: any) => void;
  categories: Array<{
    value: string;
    label: string;
    icon: string;
  }>;
}

export default function ChatFilters({ filters, onFilterChange, categories }: ChatFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [searchInput, setSearchInput] = useState(filters.search || '');

  const topics = [
    'anxiety',
    'depression',
    'relationships',
    'stress',
    'grief',
    'addiction',
    'trauma',
    'self-care',
    'mindfulness',
    'career',
    'family',
    'parenting',
    'lgbtq',
    'teens',
    'seniors',
    'chronic-illness',
    'eating-disorders',
    'sleep',
    'anger-management',
    'social-anxiety'
  ];

  const sortOptions = [
    { value: 'recent', label: 'Most Recent' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'participants', label: 'Most Participants' },
  ];

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilterChange({ search: searchInput.trim() || undefined });
  };

  const handleClearFilters = () => {
    setSearchInput('');
    onFilterChange({
      category: undefined,
      topic: undefined,
      search: undefined,
      sortBy: 'recent'
    });
  };

  const activeFiltersCount = [
    filters.category,
    filters.topic,
    filters.search
  ].filter(Boolean).length;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Find Chat Rooms</h3>
        <div className="flex items-center space-x-2">
          {activeFiltersCount > 0 && (
            <button
              onClick={handleClearFilters}
              className="text-sm text-gray-600 hover:text-gray-700 flex items-center space-x-1"
            >
              <XMarkIcon className="h-4 w-4" />
              <span>Clear ({activeFiltersCount})</span>
            </button>
          )}
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-purple-600 hover:text-purple-700 flex items-center space-x-1"
          >
            <FunnelIcon className="h-4 w-4" />
            <span>{showAdvanced ? 'Hide' : 'Show'} Filters</span>
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="mb-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
            placeholder="Search chat rooms..."
          />
        </div>
      </form>

      {/* Quick Category Filters */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onFilterChange({ category: undefined })}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              !filters.category
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All Categories
          </button>
          {categories.map((category) => (
            <button
              key={category.value}
              onClick={() => onFilterChange({ 
                category: filters.category === category.value ? undefined : category.value 
              })}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center space-x-1 ${
                filters.category === category.value
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span>{category.icon}</span>
              <span>{category.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          {/* Topic Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Topic
            </label>
            <select
              value={filters.topic || ''}
              onChange={(e) => onFilterChange({ topic: e.target.value || undefined })}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            >
              <option value="">All Topics</option>
              {topics.map((topic) => (
                <option key={topic} value={topic}>
                  {topic.charAt(0).toUpperCase() + topic.slice(1).replace('-', ' ')}
                </option>
              ))}
            </select>
          </div>

          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sort By
            </label>
            <select
              value={filters.sortBy || 'recent'}
              onChange={(e) => onFilterChange({ sortBy: e.target.value as any })}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {activeFiltersCount > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600">Active filters:</span>
            {filters.category && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                {categories.find(c => c.value === filters.category)?.label}
                <button
                  onClick={() => onFilterChange({ category: undefined })}
                  className="ml-1 text-purple-600 hover:text-purple-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {filters.topic && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {filters.topic.charAt(0).toUpperCase() + filters.topic.slice(1).replace('-', ' ')}
                <button
                  onClick={() => onFilterChange({ topic: undefined })}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {filters.search && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                "{filters.search}"
                <button
                  onClick={() => {
                    setSearchInput('');
                    onFilterChange({ search: undefined });
                  }}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

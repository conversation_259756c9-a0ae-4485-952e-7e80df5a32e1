const mongoose = require("mongoose");

const MONGODB_URI = "****************************************************************************";

async function finishResourceFix() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    const Resource = mongoose.model("Resource", new mongoose.Schema({}, { strict: false }));
    const User = mongoose.model("User", new mongoose.Schema({}, { strict: false }));

    // Find existing admin user
    let adminUser = await User.findOne({ email: "<EMAIL>" });
    if (!adminUser) {
      // Try to find any admin user
      adminUser = await User.findOne({ role: 'admin' });
    }
    
    if (!adminUser) {
      // Create a new admin user with unique email
      const timestamp = Date.now();
      adminUser = await User.create({
        firstName: "System",
        lastName: "Admin", 
        email: `admin${timestamp}@theramea.com`,
        role: "admin"
      });
      console.log(`✅ Created admin user: ${adminUser._id}`);
    } else {
      console.log(`✅ Using existing admin user: ${adminUser._id}`);
    }

    // Add createdBy field to all resources that don't have it
    const updateCreatedByResult = await Resource.updateMany(
      { createdBy: { $exists: false } },
      { $set: { createdBy: adminUser._id } }
    );
    console.log(`✅ Added createdBy field to ${updateCreatedByResult.modifiedCount} resource(s)`);

    // Show final state
    console.log("\n📋 Final resources:");
    const finalResources = await Resource.find({}, "_id title category createdBy seo.slug").lean();
    finalResources.forEach((doc, i) => {
      console.log(`${i+1}. ID: ${doc._id} | Title: ${doc.title} | Category: ${doc.category} | CreatedBy: ${doc.createdBy ? '✅' : '❌'} | Slug: ${doc.seo?.slug}`);
    });

    console.log(`\n✅ Total resources: ${finalResources.length}`);

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

finishResourceFix();

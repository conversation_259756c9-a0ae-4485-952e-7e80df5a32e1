/**
 * Session synchronization utilities
 * Keeps client-side JWT authentication in sync with server-side sessions
 */

export interface SessionData {
  userId: string;
  email?: string;
  role?: string;
  isGuest?: boolean;
  isEmailVerified?: boolean;
}

/**
 * Create a server-side session for authenticated users
 */
export async function createServerSession(
  sessionData: SessionData
): Promise<void> {
  try {
    await fetch("/api/auth/create-session", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(sessionData),
    });
  } catch (error) {
    console.error("Failed to create server session:", error);
    // Don't throw - this is a non-critical enhancement
  }
}

/**
 * Clear server-side session
 */
export async function clearServerSession(): Promise<void> {
  try {
    await fetch("/api/auth/clear-session", {
      method: "POST",
    });
  } catch (error) {
    console.error("Failed to clear server session:", error);
    // Don't throw - this is a non-critical enhancement
  }
}

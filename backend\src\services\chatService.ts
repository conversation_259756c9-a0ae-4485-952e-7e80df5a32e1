import { ChatRoom, <PERSON><PERSON><PERSON>Room } from "@/models/ChatRoom";
import { Message, IMessage } from "@/models/Message";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { uploadToCloudinary } from "@/utils/cloudinary";
import mongoose from "mongoose";
import crypto from "crypto";

export interface CreateRoomData {
  name: string;
  description: string;
  topic: string;
  category: string;
  maxParticipants?: number;
  isModerated?: boolean;
  settings?: {
    allowAnonymous?: boolean;
    requireApproval?: boolean;
    allowFileSharing?: boolean;
    messageRetentionDays?: number;
    slowModeDelay?: number;
    profanityFilter?: boolean;
  };
  rules?: string[];
  tags?: string[];
  schedule?: {
    isScheduled: boolean;
    startTime?: Date;
    endTime?: Date;
    recurringPattern?: "daily" | "weekly" | "monthly";
    timezone?: string;
  };
}

export interface SendMessageData {
  content: {
    text?: string;
    type: "text" | "image" | "file" | "emoji";
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  };
  replyTo?: string;
  mentions?: string[];
}

export interface JoinRoomData {
  userId?: string;
  anonymousId?: string;
  displayName: string;
}

export interface ChatFilters {
  topic?: string;
  category?: string;
  isActive?: boolean;
  tags?: string[];
  search?: string;
}

export class ChatService {
  /**
   * Create a new chat room
   */
  static async createChatRoom(
    creatorId: string,
    data: CreateRoomData
  ): Promise<IChatRoom> {
    try {
      const chatRoom = new ChatRoom({
        name: data.name,
        description: data.description,
        topic: data.topic,
        category: data.category,
        maxParticipants: data.maxParticipants || 50,
        isModerated: data.isModerated ?? true,
        settings: {
          allowAnonymous: data.settings?.allowAnonymous ?? true,
          requireApproval: data.settings?.requireApproval ?? false,
          allowFileSharing: data.settings?.allowFileSharing ?? false,
          messageRetentionDays: data.settings?.messageRetentionDays ?? 30,
          slowModeDelay: data.settings?.slowModeDelay ?? 0,
          profanityFilter: data.settings?.profanityFilter ?? true,
        },
        rules: data.rules || [],
        tags: data.tags || [],
        schedule: data.schedule || {
          isScheduled: false,
          timezone: "Africa/Lagos",
        },
        createdBy: creatorId,
        moderators: [creatorId],
      });

      await chatRoom.save();
      logger.info(`Chat room created: ${chatRoom.name} by user: ${creatorId}`);

      return chatRoom;
    } catch (error) {
      logger.error("Create chat room error:", error);
      throw error;
    }
  }

  /**
   * Get chat rooms with filters and pagination
   */
  static async getChatRooms(filters: ChatFilters = {}, options: any = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "statistics.lastActivityAt",
        sortOrder = "desc",
      } = options;

      // Build query
      const query: any = { isActive: true };

      if (filters.topic) query.topic = filters.topic;
      if (filters.category) query.category = filters.category;
      if (filters.isActive !== undefined) query.isActive = filters.isActive;
      if (filters.tags?.length) query.tags = { $in: filters.tags };

      // Search functionality
      if (filters.search) {
        query.$or = [
          { name: { $regex: filters.search, $options: "i" } },
          { description: { $regex: filters.search, $options: "i" } },
          { tags: { $regex: filters.search, $options: "i" } },
        ];
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === "asc" ? 1 : -1;

      const [chatRooms, total] = await Promise.all([
        ChatRoom.find(query)
          .populate("createdBy", "firstName lastName profilePicture")
          .sort(sortOptions)
          .skip(skip)
          .limit(limit),
        ChatRoom.countDocuments(query),
      ]);

      return {
        chatRooms,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get chat rooms error:", error);
      throw error;
    }
  }

  /**
   * Get chat room by ID
   */
  static async getChatRoom(roomId: string): Promise<IChatRoom> {
    try {
      const chatRoom = await ChatRoom.findById(roomId)
        .populate("createdBy", "firstName lastName profilePicture")
        .populate("moderators", "firstName lastName profilePicture");

      if (!chatRoom) {
        throw createError("Chat room not found", 404);
      }

      return chatRoom;
    } catch (error) {
      logger.error("Get chat room error:", error);
      throw error;
    }
  }

  /**
   * Join a chat room
   */
  static async joinChatRoom(
    roomId: string,
    joinData: JoinRoomData
  ): Promise<IChatRoom> {
    try {
      const chatRoom = await ChatRoom.findById(roomId);

      if (!chatRoom) {
        throw createError("Chat room not found", 404);
      }

      if (!chatRoom.isActive) {
        throw createError("Chat room is not active", 400);
      }

      if (chatRoom.currentParticipants >= chatRoom.maxParticipants) {
        throw createError("Chat room is full", 400);
      }

      // Check if user is already in the room
      const existingParticipant = chatRoom.participants.find(
        (p) =>
          (joinData.userId && p.userId?.toString() === joinData.userId) ||
          (joinData.anonymousId && p.anonymousId === joinData.anonymousId)
      );

      if (existingParticipant) {
        // Update last seen, online status, and display name
        existingParticipant.lastSeen = new Date();
        existingParticipant.isOnline = true;
        existingParticipant.displayName = joinData.displayName; // Update display name
      } else {
        // Add new participant
        // Determine participant role based on authentication status
        let participantRole: "participant" | "moderator" | "readonly" =
          "participant"; // Default to participant for registered users

        // Only assign readonly role if this is explicitly a guest/anonymous user
        if (!joinData.userId && joinData.anonymousId) {
          participantRole = "readonly";
          console.log(
            "DEBUG - Anonymous user, assigning readonly role:",
            joinData.anonymousId
          );
        } else if (joinData.userId) {
          participantRole = "participant";
          console.log(
            "DEBUG - Registered user with userId, assigning participant role:",
            joinData.userId
          );
        } else {
          // Fallback - if no userId and no anonymousId, treat as registered user
          participantRole = "participant";
          console.log(
            "DEBUG - No clear identification, defaulting to participant role"
          );
        }

        console.log("DEBUG - Final role assignment:", {
          participantRole,
          hasUserId: !!joinData.userId,
          hasAnonymousId: !!joinData.anonymousId,
          displayName: joinData.displayName,
        });

        chatRoom.participants.push({
          userId: joinData.userId ? (joinData.userId as any) : undefined,
          anonymousId: joinData.anonymousId,
          joinedAt: new Date(),
          lastSeen: new Date(),
          isOnline: true,
          role: participantRole,
          displayName: joinData.displayName,
          isMuted: false,
        });

        chatRoom.currentParticipants += 1;
        chatRoom.statistics.totalParticipants += 1;

        if (
          chatRoom.currentParticipants > chatRoom.statistics.peakParticipants
        ) {
          chatRoom.statistics.peakParticipants = chatRoom.currentParticipants;
        }
      }

      chatRoom.statistics.lastActivityAt = new Date();
      await chatRoom.save();

      logger.info(`User joined chat room: ${roomId}`);
      return chatRoom;
    } catch (error) {
      logger.error("Join chat room error:", error);
      throw error;
    }
  }

  /**
   * Leave a chat room
   */
  static async leaveChatRoom(
    roomId: string,
    userId?: string,
    anonymousId?: string
  ): Promise<void> {
    try {
      const chatRoom = await ChatRoom.findById(roomId);

      if (!chatRoom) {
        throw createError("Chat room not found", 404);
      }

      const participantIndex = chatRoom.participants.findIndex(
        (p) =>
          (userId && p.userId?.toString() === userId) ||
          (anonymousId && p.anonymousId === anonymousId)
      );

      if (participantIndex > -1) {
        chatRoom.participants.splice(participantIndex, 1);
        chatRoom.currentParticipants = Math.max(
          0,
          chatRoom.currentParticipants - 1
        );
        await chatRoom.save();

        logger.info(`User left chat room: ${roomId}`);
      }
    } catch (error) {
      logger.error("Leave chat room error:", error);
      throw error;
    }
  }

  /**
   * Send a message to a chat room
   */
  static async sendMessage(
    roomId: string,
    senderId: string | undefined,
    anonymousSenderId: string | undefined,
    senderDisplayName: string,
    messageData: SendMessageData,
    metadata?: any
  ): Promise<IMessage> {
    try {
      const chatRoom = await ChatRoom.findById(roomId);

      if (!chatRoom) {
        throw createError("Chat room not found", 404);
      }

      if (!chatRoom.isActive) {
        throw createError("Chat room is not active", 400);
      }

      // Check if sender is in the room
      const participant = chatRoom.participants.find(
        (p) =>
          (senderId && p.userId?.toString() === senderId) ||
          (anonymousSenderId && p.anonymousId === anonymousSenderId)
      );

      console.log("DEBUG - Send Message Check:", {
        roomId,
        senderId,
        anonymousSenderId,
        senderDisplayName,
        participant: participant
          ? {
              userId: participant.userId,
              anonymousId: participant.anonymousId,
              role: participant.role,
              displayName: participant.displayName,
              isMuted: participant.isMuted,
            }
          : null,
        allParticipants: chatRoom.participants.map((p) => ({
          userId: p.userId,
          anonymousId: p.anonymousId,
          role: p.role,
          displayName: p.displayName,
        })),
      });

      if (!participant) {
        throw createError(
          "You must join the room before sending messages",
          403
        );
      }

      if (participant.isMuted) {
        throw createError("You are muted in this room", 403);
      }

      // Check if participant has readonly access
      if (participant.role === "readonly") {
        console.log("DEBUG - Participant is readonly, blocking message:", {
          participantRole: participant.role,
          userId: participant.userId,
          anonymousId: participant.anonymousId,
        });
        throw createError(
          "Read-only access: You cannot send messages in this room",
          403
        );
      }

      // Check slow mode
      if (chatRoom.settings.slowModeDelay > 0) {
        const lastMessage = await Message.findOne({
          chatRoomId: roomId,
          $or: [{ senderId }, { anonymousSenderId }],
        }).sort({ createdAt: -1 });

        if (lastMessage) {
          const timeSinceLastMessage =
            Date.now() - lastMessage.createdAt.getTime();
          if (timeSinceLastMessage < chatRoom.settings.slowModeDelay * 1000) {
            throw createError(
              `Please wait ${chatRoom.settings.slowModeDelay} seconds between messages`,
              429
            );
          }
        }
      }

      // Create message
      const message = new Message({
        chatRoomId: roomId,
        senderId: senderId ? (senderId as any) : undefined,
        anonymousSenderId,
        senderDisplayName,
        content: messageData.content,
        mentions: messageData.mentions || [],
        replyTo: messageData.replyTo ? (messageData.replyTo as any) : undefined,
        metadata: metadata || {},
      });

      await message.save();

      // Update room statistics
      chatRoom.statistics.totalMessages += 1;
      chatRoom.statistics.lastActivityAt = new Date();
      await chatRoom.save();

      logger.info(`Message sent to room ${roomId} by ${senderDisplayName}`);
      return message;
    } catch (error) {
      logger.error("Send message error:", error);
      throw error;
    }
  }

  /**
   * Get messages from a chat room
   */
  static async getMessages(roomId: string, options: any = {}) {
    try {
      const { page = 1, limit = 50, before, after } = options;

      const query: any = {
        chatRoomId: roomId,
        "moderation.isDeleted": false,
      };

      if (before) {
        query.createdAt = { $lt: new Date(before) };
      }

      if (after) {
        query.createdAt = { $gt: new Date(after) };
      }

      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        Message.find(query)
          .populate("senderId", "firstName lastName profilePicture")
          .populate("replyTo")
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        Message.countDocuments(query),
      ]);

      return {
        messages: messages.reverse(), // Return in chronological order
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get messages error:", error);
      throw error;
    }
  }

  /**
   * Generate anonymous ID for guest users
   */
  static generateAnonymousId(): string {
    return crypto.randomUUID();
  }

  /**
   * Generate anonymous display name
   */
  static generateAnonymousDisplayName(): string {
    const adjectives = [
      "Anonymous",
      "Guest",
      "Visitor",
      "Friend",
      "Helper",
      "Supporter",
    ];
    const numbers = Math.floor(Math.random() * 9999) + 1;
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    return `${adjective}${numbers}`;
  }

  /**
   * Upload file for chat message
   */
  static async uploadChatFile(file: Express.Multer.File): Promise<string> {
    try {
      const fileUrl = await uploadToCloudinary(file, "chat-files");
      logger.info(`Chat file uploaded: ${file.originalname}`);
      return fileUrl;
    } catch (error) {
      logger.error("Upload chat file error:", error);
      throw error;
    }
  }

  /**
   * Mark messages as read for a user
   */
  static async markMessagesAsRead(
    roomId: string,
    userId?: string,
    anonymousId?: string
  ): Promise<void> {
    try {
      if (!userId && !anonymousId) {
        throw createError("User ID or anonymous ID required", 400);
      }

      const readByQuery = userId ? { userId } : { anonymousId };

      await Message.updateMany(
        {
          chatRoomId: roomId,
          readBy: { $not: { $elemMatch: readByQuery } },
        },
        {
          $push: {
            readBy: {
              ...readByQuery,
              readAt: new Date(),
            },
          },
        }
      );

      logger.info(
        `Messages marked as read for ${userId || anonymousId} in room ${roomId}`
      );
    } catch (error) {
      logger.error("Mark messages as read error:", error);
      throw error;
    }
  }

  /**
   * Get unread message count for a user in a room
   */
  static async getUnreadMessageCount(
    roomId: string,
    userId?: string,
    anonymousId?: string
  ): Promise<number> {
    try {
      if (!userId && !anonymousId) {
        return 0;
      }

      const readByQuery = userId ? { userId } : { anonymousId };

      const count = await Message.countDocuments({
        chatRoomId: roomId,
        readBy: { $not: { $elemMatch: readByQuery } },
      });

      return count;
    } catch (error) {
      logger.error("Get unread message count error:", error);
      return 0;
    }
  }

  /**
   * Edit user's own message
   */
  static async editOwnMessage(
    messageId: string,
    newContent: any,
    userId?: string,
    anonymousId?: string
  ): Promise<void> {
    try {
      if (!userId && !anonymousId) {
        throw createError("User ID or anonymous ID required", 400);
      }

      const message = await Message.findById(messageId);
      if (!message) {
        throw createError("Message not found", 404);
      }

      // Check if user owns this message
      const isOwner = userId
        ? message.senderId?.toString() === userId
        : message.anonymousSenderId === anonymousId;

      if (!isOwner) {
        throw createError("You can only edit your own messages", 403);
      }

      // Store previous content in edit history
      message.editHistory.push({
        previousContent: message.content.text || "",
        editedAt: new Date(),
      });

      // Update message content
      message.content = newContent;
      message.isEdited = true;

      await message.save();

      logger.info(`Message ${messageId} edited by ${userId || anonymousId}`);
    } catch (error) {
      logger.error("Edit own message error:", error);
      throw error;
    }
  }

  /**
   * Delete user's own message
   */
  static async deleteOwnMessage(
    messageId: string,
    userId?: string,
    anonymousId?: string
  ): Promise<void> {
    try {
      if (!userId && !anonymousId) {
        throw createError("User ID or anonymous ID required", 400);
      }

      const message = await Message.findById(messageId);
      if (!message) {
        throw createError("Message not found", 404);
      }

      // Check if user owns this message
      const isOwner = userId
        ? message.senderId?.toString() === userId
        : message.anonymousSenderId === anonymousId;

      if (!isOwner) {
        throw createError("You can only delete your own messages", 403);
      }

      // Soft delete: mark as deleted instead of removing
      message.moderation.isDeleted = true;
      message.moderation.deletedBy = userId
        ? new mongoose.Types.ObjectId(userId)
        : undefined;
      message.moderation.deletedAt = new Date();

      await message.save();

      logger.info(`Message ${messageId} deleted by ${userId || anonymousId}`);
    } catch (error) {
      logger.error("Delete own message error:", error);
      throw error;
    }
  }
}

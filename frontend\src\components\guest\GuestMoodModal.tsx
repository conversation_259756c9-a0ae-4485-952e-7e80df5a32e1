"use client";

import { useState } from "react";
import { X } from "lucide-react";

const MOOD_OPTIONS = [
  { value: 1, emoji: "😔", label: "Very Sad", color: "text-blue-600" },
  { value: 2, emoji: "😟", label: "Sad", color: "text-blue-500" },
  { value: 3, emoji: "😐", label: "Neutral", color: "text-gray-500" },
  { value: 4, emoji: "🙂", label: "Happy", color: "text-green-500" },
  { value: 5, emoji: "😄", label: "Very Happy", color: "text-green-600" },
];

interface GuestMoodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (mood: number, note?: string) => void;
}

export default function GuestMoodModal({
  isOpen,
  onClose,
  onComplete,
}: GuestMoodModalProps) {
  const [selectedMood, setSelectedMood] = useState<number | null>(null);
  const [note, setNote] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async () => {
    if (selectedMood === null) return;

    setIsSubmitting(true);
    try {
      await onComplete(selectedMood, note.trim() || undefined);
      onClose();
    } catch (error) {
      console.error("Error submitting mood:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedMoodData = MOOD_OPTIONS.find(
    (option) => option.value === selectedMood
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            How are you feeling today?
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <p className="text-gray-600 mb-6">
          Want us to recommend the right tips for you? Tell us how you're
          feeling today.
        </p>

        {/* Mood Scale */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            {MOOD_OPTIONS.map((option) => (
              <button
                key={option.value}
                onClick={() => setSelectedMood(option.value)}
                className={`
                  flex flex-col items-center p-2 rounded-lg transition-all duration-200
                  ${
                    selectedMood === option.value
                      ? "bg-purple-100 border-2 border-purple-500 scale-110"
                      : "bg-gray-50 border-2 border-transparent hover:bg-purple-50 hover:border-purple-300"
                  }
                `}
              >
                <span className="text-2xl mb-1">{option.emoji}</span>
                <span className={`text-xs font-medium ${option.color}`}>
                  {option.label}
                </span>
              </button>
            ))}
          </div>

          {selectedMoodData && (
            <div className="text-center">
              <p className="text-sm text-gray-600">
                You're feeling:{" "}
                <span className={`font-medium ${selectedMoodData.color}`}>
                  {selectedMoodData.label}
                </span>
              </p>
            </div>
          )}
        </div>

        {/* Optional Note */}
        <div className="mb-6">
          <label
            htmlFor="note"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Want to tell us more? (Optional)
          </label>
          <textarea
            id="note"
            rows={3}
            value={note}
            onChange={(e) => setNote(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            placeholder="Share what's on your mind..."
            maxLength={200}
          />
          <p className="mt-1 text-xs text-gray-500">
            {note.length}/200 characters
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Maybe later
          </button>
          <button
            onClick={handleSubmit}
            disabled={selectedMood === null || isSubmitting}
            className="flex-1 py-2 px-4 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Saving..." : "Save"}
          </button>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface AvailabilityCalendarProps {
  unavailableDates: string[];
  onChange: (dates: string[]) => void;
}

export default function AvailabilityCalendar({ unavailableDates, onChange }: AvailabilityCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const formatDateString = (year: number, month: number, day: number) => {
    return `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  };

  const isDateUnavailable = (dateString: string) => {
    return unavailableDates.includes(dateString);
  };

  const isDateInPast = (year: number, month: number, day: number) => {
    const date = new Date(year, month, day);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  const toggleDateAvailability = (year: number, month: number, day: number) => {
    const dateString = formatDateString(year, month, day);
    
    if (isDateInPast(year, month, day)) {
      return; // Don't allow toggling past dates
    }

    let newUnavailableDates;
    if (isDateUnavailable(dateString)) {
      newUnavailableDates = unavailableDates.filter(date => date !== dateString);
    } else {
      newUnavailableDates = [...unavailableDates, dateString];
    }
    
    onChange(newUnavailableDates);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const renderCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDayOfMonth = getFirstDayOfMonth(currentDate);
    
    const days = [];
    
    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <div key={`empty-${i}`} className="h-10 w-10"></div>
      );
    }
    
    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = formatDateString(year, month, day);
      const isUnavailable = isDateUnavailable(dateString);
      const isPast = isDateInPast(year, month, day);
      const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();
      
      days.push(
        <button
          key={day}
          onClick={() => toggleDateAvailability(year, month, day)}
          disabled={isPast}
          className={`h-10 w-10 rounded-full text-sm font-medium transition-colors ${
            isPast
              ? 'text-gray-300 cursor-not-allowed'
              : isUnavailable
              ? 'bg-red-500 text-white hover:bg-red-600'
              : isToday
              ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
              : 'text-gray-700 hover:bg-gray-100'
          }`}
        >
          {day}
        </button>
      );
    }
    
    return days;
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => navigateMonth('prev')}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
        </button>
        
        <h3 className="text-lg font-semibold text-gray-900">
          {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
        </h3>
        
        <button
          onClick={() => navigateMonth('next')}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <ChevronRightIcon className="h-5 w-5 text-gray-600" />
        </button>
      </div>

      {/* Day Names */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((dayName) => (
          <div key={dayName} className="h-10 flex items-center justify-center">
            <span className="text-sm font-medium text-gray-500">{dayName}</span>
          </div>
        ))}
      </div>

      {/* Calendar Days */}
      <div className="grid grid-cols-7 gap-1">
        {renderCalendarDays()}
      </div>

      {/* Legend */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Legend</h4>
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 bg-red-500 rounded-full"></div>
            <span className="text-gray-700">Unavailable</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 bg-purple-100 border border-purple-300 rounded-full"></div>
            <span className="text-gray-700">Today</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 bg-gray-100 border border-gray-300 rounded-full"></div>
            <span className="text-gray-700">Available</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 bg-gray-50 border border-gray-200 rounded-full opacity-50"></div>
            <span className="text-gray-500">Past dates</span>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <p className="text-sm text-blue-800">
          <strong>Instructions:</strong> Click on any future date to mark it as unavailable (red). 
          Click again to make it available. Past dates cannot be modified.
        </p>
      </div>

      {/* Summary */}
      {unavailableDates.length > 0 && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Unavailable Dates ({unavailableDates.length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {unavailableDates
              .filter(date => new Date(date) >= new Date())
              .sort()
              .slice(0, 10)
              .map((date) => (
                <span
                  key={date}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                >
                  {new Date(date).toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </span>
              ))}
            {unavailableDates.filter(date => new Date(date) >= new Date()).length > 10 && (
              <span className="text-xs text-gray-500">
                +{unavailableDates.filter(date => new Date(date) >= new Date()).length - 10} more
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

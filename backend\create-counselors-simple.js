const mongoose = require("mongoose");

const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

// Simple schemas
const userSchema = new mongoose.Schema(
  {
    firstName: String,
    lastName: String,
    email: { type: String, unique: true },
    password: String,
    role: {
      type: String,
      enum: ["user", "counselor", "admin"],
      default: "user",
    },
    isActive: { type: Boolean, default: true },
    isEmailVerified: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
  },
  { strict: false }
);

const counselorSchema = new mongoose.Schema(
  {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    bio: String,
    specializations: [String],
    experience: {
      years: Number,
      description: String,
    },
    qualifications: [String],
    pricing: {
      currency: { type: String, enum: ["NGN", "USD"], default: "NGN" },
      ratePerMinute: Number,
    },
    verification: {
      status: {
        type: String,
        enum: ["pending", "approved", "rejected"],
        default: "approved",
      },
    },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
  },
  { strict: false }
);

const User = mongoose.model("User", userSchema);
const Counselor = mongoose.model("Counselor", counselorSchema);

const counselorUsers = [
  {
    firstName: "Dr. Sarah",
    lastName: "Johnson",
    email: "<EMAIL>",
    password: "$2a$10$Hash.Of.Password123",
    role: "counselor",
    phone: "+2348011111111",
    gender: "female",
    location: "Lagos, Nigeria",
  },
  {
    firstName: "Dr. Michael",
    lastName: "Brown",
    email: "<EMAIL>",
    password: "$2a$10$Hash.Of.Password123",
    role: "counselor",
    phone: "+2348022222222",
    gender: "male",
    location: "Abuja, Nigeria",
  },
  {
    firstName: "Dr. Emily",
    lastName: "Davis",
    email: "<EMAIL>",
    password: "$2a$10$Hash.Of.Password123",
    role: "counselor",
    phone: "+2348033333333",
    gender: "female",
    location: "Port Harcourt, Nigeria",
  },
];

const counselorProfiles = [
  {
    bio: "Experienced clinical psychologist specializing in anxiety and depression treatment with over 8 years of practice.",
    specializations: ["anxiety", "depression", "stress-management"],
    experience: {
      years: 8,
      description:
        "Clinical psychologist with extensive experience in cognitive behavioral therapy.",
    },
    qualifications: [
      "Ph.D. in Clinical Psychology",
      "Licensed Clinical Psychologist",
    ],
    pricing: {
      currency: "NGN",
      ratePerMinute: 150,
    },
  },
  {
    bio: "Licensed therapist focusing on relationship counseling and family therapy with a holistic approach.",
    specializations: ["relationships", "family-therapy", "couples-counseling"],
    experience: {
      years: 6,
      description: "Family therapist with specialization in systemic therapy.",
    },
    qualifications: [
      "Master of Arts in Marriage and Family Therapy",
      "Licensed Marriage and Family Therapist",
    ],
    pricing: {
      currency: "NGN",
      ratePerMinute: 120,
    },
  },
  {
    bio: "Child psychologist with expertise in developmental disorders and behavioral interventions.",
    specializations: [
      "child-psychology",
      "developmental-disorders",
      "behavioral-therapy",
    ],
    experience: {
      years: 5,
      description: "Specializing in child and adolescent mental health.",
    },
    qualifications: [
      "Ph.D. in Child Psychology",
      "Licensed Child Psychologist",
    ],
    pricing: {
      currency: "NGN",
      ratePerMinute: 130,
    },
  },
];

async function createCounselors() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    console.log("👥 Creating counselor users...");
    const createdUsers = [];

    for (const userData of counselorUsers) {
      try {
        // Check if user already exists
        const existingUser = await User.findOne({ email: userData.email });
        if (existingUser) {
          console.log(`⚠️  User already exists: ${userData.email}`);
          createdUsers.push(existingUser);
          continue;
        }

        const user = new User(userData);
        const savedUser = await user.save();
        createdUsers.push(savedUser);
        console.log(
          `✅ Created user: ${userData.firstName} ${userData.lastName} (${userData.email})`
        );
      } catch (error) {
        console.log(`❌ Error creating user ${userData.email}:`, error.message);
      }
    }

    console.log("🩺 Creating counselor profiles...");
    for (
      let i = 0;
      i < createdUsers.length && i < counselorProfiles.length;
      i++
    ) {
      try {
        // Check if counselor profile already exists
        const existingCounselor = await Counselor.findOne({
          userId: createdUsers[i]._id,
        });
        if (existingCounselor) {
          console.log(
            `⚠️  Counselor profile already exists for: ${createdUsers[i].firstName} ${createdUsers[i].lastName}`
          );
          continue;
        }

        const counselor = new Counselor({
          ...counselorProfiles[i],
          userId: createdUsers[i]._id,
        });
        await counselor.save();
        console.log(
          `✅ Created counselor profile for: ${createdUsers[i].firstName} ${createdUsers[i].lastName}`
        );
      } catch (error) {
        console.log(`❌ Error creating counselor profile:`, error.message);
      }
    }

    // Verify creation
    const totalCounselors = await Counselor.countDocuments();
    const totalCounselorUsers = await User.countDocuments({
      role: "counselor",
    });

    console.log(`\n🎉 Creation completed!`);
    console.log(`📊 Total counselor users: ${totalCounselorUsers}`);
    console.log(`📊 Total counselor profiles: ${totalCounselors}`);
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

createCounselors();

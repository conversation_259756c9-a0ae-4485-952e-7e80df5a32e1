'use client';

import { useState } from 'react';
import { Counselor, BookingRequest } from '@/types/counselor';
import { 
  CheckCircleIcon, 
  CalendarIcon, 
  ClockIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';

interface BookingSummaryProps {
  counselor: Counselor;
  bookingData: BookingRequest;
  onPrev: () => void;
  onConfirm: () => void;
}

export default function BookingSummary({ counselor, bookingData, onPrev, onConfirm }: BookingSummaryProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const calculateTotalCost = () => {
    const baseRate = counselor.pricing.ratePerMinute * bookingData.duration;
    const urgentFee = bookingData.isUrgent ? baseRate * 0.25 : 0;
    const platformFee = (baseRate + urgentFee) * 0.05;
    
    return {
      baseAmount: baseRate,
      urgentFee,
      platformFee,
      totalAmount: baseRate + urgentFee + platformFee,
    };
  };

  const pricing = calculateTotalCost();

  const getSessionTypeLabel = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Individual Session';
      case 'couples':
        return 'Couples Session';
      case 'family':
        return 'Family Session';
      case 'group':
        return 'Group Session';
      default:
        return 'Session';
    }
  };

  const handleConfirm = async () => {
    if (!agreedToTerms) {
      alert('Please agree to the terms and conditions to proceed.');
      return;
    }

    setIsSubmitting(true);
    try {
      await onConfirm();
    } catch (error) {
      console.error('Booking confirmation failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-3 mb-6">
        <CheckCircleIcon className="h-8 w-8 text-green-600" />
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Review Your Booking</h2>
          <p className="text-gray-600">Please review all details before confirming your session</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Counselor Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Counselor</h3>
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {counselor.profile.profilePicture || counselor.user?.profilePicture ? (
                <img
                  src={counselor.profile.profilePicture || counselor.user?.profilePicture}
                  alt={`${counselor.user?.firstName} ${counselor.user?.lastName}`}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-purple-600" />
                </div>
              )}
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">
                {counselor.user?.firstName} {counselor.user?.lastName}
              </h4>
              <p className="text-sm text-gray-600">
                {counselor.specializations.slice(0, 2).join(', ')}
              </p>
              <p className="text-sm text-gray-500">
                {counselor.experience.years} years experience
              </p>
            </div>
          </div>
        </div>

        {/* Session Details */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Session Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Session Type</p>
                <p className="text-sm text-gray-600">{getSessionTypeLabel(bookingData.sessionType)}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <ClockIcon className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Duration</p>
                <p className="text-sm text-gray-600">{bookingData.duration} minutes</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <CalendarIcon className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Date & Time</p>
                <p className="text-sm text-gray-600">
                  {new Date(bookingData.scheduledAt).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <p className="text-sm text-gray-600">
                  {new Date(bookingData.scheduledAt).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <UserIcon className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Language</p>
                <p className="text-sm text-gray-600 capitalize">
                  {bookingData.preferredLanguage === 'en' ? 'English' : bookingData.preferredLanguage}
                </p>
              </div>
            </div>
          </div>

          {bookingData.isUrgent && (
            <div className="mt-4 flex items-center space-x-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
              <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">
                This is marked as an urgent session
              </span>
            </div>
          )}

          {bookingData.notes && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-900 mb-2">Additional Notes</p>
              <div className="bg-white border border-gray-200 rounded-md p-3">
                <p className="text-sm text-gray-700">{bookingData.notes}</p>
              </div>
            </div>
          )}
        </div>

        {/* Pricing Breakdown */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Pricing Breakdown</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">
                Base Rate ({bookingData.duration} min × {formatCurrency(counselor.pricing.ratePerMinute, counselor.pricing.currency)}/min)
              </span>
              <span className="font-medium text-gray-900">
                {formatCurrency(pricing.baseAmount, counselor.pricing.currency)}
              </span>
            </div>

            {bookingData.isUrgent && pricing.urgentFee > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">Urgent Session Fee (25%)</span>
                <span className="font-medium text-gray-900">
                  {formatCurrency(pricing.urgentFee, counselor.pricing.currency)}
                </span>
              </div>
            )}

            <div className="flex justify-between">
              <span className="text-gray-600">Platform Fee (5%)</span>
              <span className="font-medium text-gray-900">
                {formatCurrency(pricing.platformFee, counselor.pricing.currency)}
              </span>
            </div>

            <div className="border-t border-gray-300 pt-2 mt-2">
              <div className="flex justify-between">
                <span className="text-lg font-semibold text-gray-900">Total Amount</span>
                <span className="text-lg font-bold text-purple-600">
                  {formatCurrency(pricing.totalAmount, counselor.pricing.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-semibold text-blue-900 mb-2">Terms & Conditions</h4>
          <ul className="text-xs text-blue-800 space-y-1 mb-3">
            <li>• Payment will be processed after session confirmation</li>
            <li>• Sessions can be cancelled up to 24 hours in advance for a full refund</li>
            <li>• Late cancellations (less than 24 hours) are subject to a 50% fee</li>
            <li>• No-shows will be charged the full session fee</li>
            <li>• All sessions are confidential and secure</li>
            <li>• Technical support is available during your session</li>
          </ul>
          
          <div className="flex items-start space-x-2">
            <input
              type="checkbox"
              id="terms"
              checked={agreedToTerms}
              onChange={(e) => setAgreedToTerms(e.target.checked)}
              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded mt-0.5"
            />
            <label htmlFor="terms" className="text-xs text-blue-800">
              I agree to the terms and conditions, cancellation policy, and privacy policy
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <button
            onClick={onPrev}
            className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Details
          </button>
          
          <button
            onClick={handleConfirm}
            disabled={!agreedToTerms || isSubmitting}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Confirm & Proceed to Payment
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

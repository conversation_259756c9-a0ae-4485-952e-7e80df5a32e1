import { jwtVerify, SignJWT } from "jose";

const secretKey = process.env.JWT_SECRET || "your-secret-key-here";
const encodedKey = new TextEncoder().encode(secretKey);

export interface SessionPayload {
  userId: string;
  email?: string;
  role?: string;
  isGuest?: boolean;
  isEmailVerified?: boolean;
  expiresAt: string;
}

export async function encrypt(payload: SessionPayload): Promise<string> {
  const jwtPayload = {
    userId: payload.userId,
    email: payload.email,
    role: payload.role,
    isGuest: payload.isGuest,
    isEmailVerified: payload.isEmailVerified,
    expiresAt: payload.expiresAt,
  };

  return new SignJWT(jwtPayload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .sign(encodedKey);
}

export async function decrypt(
  session: string | undefined = ""
): Promise<SessionPayload | null> {
  if (!session) return null;

  try {
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ["HS256"],
    });

    // Safely extract the session data
    if (
      payload &&
      typeof payload === "object" &&
      "userId" in payload &&
      typeof payload.userId === "string"
    ) {
      return {
        userId: payload.userId,
        email: payload.email as string | undefined,
        role: payload.role as string | undefined,
        isGuest: payload.isGuest as boolean | undefined,
        isEmailVerified: payload.isEmailVerified as boolean | undefined,
        expiresAt: payload.expiresAt as string,
      };
    }

    return null;
  } catch (error) {
    console.log("Failed to verify session:", error);
    return null;
  }
}

export async function createSession(
  userId: string,
  email?: string,
  role?: string,
  isGuest?: boolean,
  isEmailVerified?: boolean
): Promise<string> {
  const expiresAt = new Date(
    Date.now() + 7 * 24 * 60 * 60 * 1000
  ).toISOString(); // 7 days
  const session: SessionPayload = {
    userId,
    email,
    role,
    isGuest,
    isEmailVerified,
    expiresAt,
  };

  return await encrypt(session);
}

export function deleteSession() {
  // This would typically involve clearing the session cookie
  // The actual cookie deletion would be handled by the calling code
  return null;
}

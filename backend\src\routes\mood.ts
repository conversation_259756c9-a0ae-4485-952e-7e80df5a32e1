import { Router } from "express";
import { <PERSON><PERSON><PERSON><PERSON>roll<PERSON> } from "@/controllers/moodController";
import { moodValidations, paramValidations } from "@/utils/validation";
import { authenticate } from "@/middleware/auth";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// All mood routes require authentication
router.use(authenticate);

// Create a new mood entry
router.post(
  "/",
  moodValidations.create,
  validateRequest,
  MoodController.createMoodEntry
);

// Get user's mood entries with filtering and pagination
router.get("/", MoodController.getMoodEntries);

// Get mood analytics and insights
router.get("/analytics", MoodController.getMoodAnalytics);

// Get personalized feedback for a mood entry
router.post("/feedback", MoodController.getPersonalizedFeedback);

// Get resource suggestions based on mood entry
router.post("/suggestions", MoodController.getResourceSuggestions);

// Get a specific mood entry
router.get(
  "/:entryId",
  paramValidations.objectId("entryId"),
  validateRequest,
  MoodController.getMoodEntry
);

// Update a mood entry
router.put(
  "/:entryId",
  paramValidations.objectId("entryId"),
  moodValidations.update,
  validateRequest,
  MoodController.updateMoodEntry
);

// Delete a mood entry
router.delete(
  "/:entryId",
  paramValidations.objectId("entryId"),
  validateRequest,
  MoodController.deleteMoodEntry
);

export default router;

import { User, IUser } from "@/models/User";
import { JWTService, TokenPair } from "@/utils/jwt";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { sendEmail } from "@/utils/email";
import crypto from "crypto";

export interface RegisterData {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  areasOfInterest?: string[];
}

export interface LoginData {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: Partial<IUser>;
  tokens: TokenPair;
}

export class AuthService {
  /**
   * Register a new user
   */
  static async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email: userData.email }, { username: userData.username }],
      });

      if (existingUser) {
        if (existingUser.email === userData.email) {
          throw createError("Email already registered", 400);
        }
        if (existingUser.username === userData.username) {
          throw createError("Username already taken", 400);
        }
      }

      // Create new user
      const user = new User({
        firstName: userData.firstName,
        lastName: userData.lastName,
        username: userData.username.toLowerCase(),
        email: userData.email.toLowerCase(),
        password: userData.password,
        areasOfInterest: userData.areasOfInterest || [],
        isEmailVerified: false,
        isActive: true,
      });

      await user.save();

      // Generate email verification token
      const verificationToken = JWTService.generateEmailVerificationToken(
        user.email
      );
      user.emailVerificationToken = verificationToken;
      user.emailVerificationExpires = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours
      await user.save();

      // Send verification email
      await this.sendVerificationEmail(
        user.email,
        user.firstName,
        verificationToken
      );

      // Generate tokens
      const tokens = JWTService.generateTokenPair(user);

      // Return user without password
      const userResponse = user.toObject();
      delete userResponse.password;
      delete userResponse.emailVerificationToken;
      delete userResponse.passwordResetToken;

      logger.info(`New user registered: ${user.email}`);

      return {
        user: userResponse,
        tokens,
      };
    } catch (error) {
      logger.error("Registration error:", error);
      throw error;
    }
  }

  /**
   * Login user
   */
  static async login(loginData: LoginData): Promise<AuthResponse> {
    try {
      // Find user by email
      const user = await User.findOne({
        email: loginData.email.toLowerCase(),
      }).select("+password");

      if (!user) {
        throw createError("Invalid email or password", 401);
      }

      if (!user.isActive) {
        throw createError("Account is deactivated", 401);
      }

      // Check password
      const isPasswordValid = await user.comparePassword(loginData.password);
      if (!isPasswordValid) {
        throw createError("Invalid email or password", 401);
      }

      // Generate tokens
      const tokens = JWTService.generateTokenPair(user);

      // Return user without password
      const userResponse = user.toObject();
      delete userResponse.password;
      delete userResponse.emailVerificationToken;
      delete userResponse.passwordResetToken;

      logger.info(`User logged in: ${user.email}`);

      return {
        user: userResponse,
        tokens,
      };
    } catch (error) {
      logger.error("Login error:", error);
      throw error;
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = JWTService.verifyRefreshToken(refreshToken);

      // Find user
      const user = await User.findById(payload.userId);
      if (!user || !user.isActive) {
        throw createError("Invalid refresh token", 401);
      }

      // Generate new tokens
      const tokens = JWTService.generateTokenPair(user);

      logger.info(`Token refreshed for user: ${user.email}`);

      return tokens;
    } catch (error) {
      logger.error("Token refresh error:", error);
      throw createError("Invalid refresh token", 401);
    }
  }

  /**
   * Verify email
   */
  static async verifyEmail(token: string): Promise<void> {
    try {
      // Verify token
      const { email } = JWTService.verifyEmailVerificationToken(token);

      // Find user
      const user = await User.findOne({ email });
      if (!user) {
        throw createError("User not found", 404);
      }

      if (user.isEmailVerified) {
        throw createError("Email already verified", 400);
      }

      // Update user
      user.isEmailVerified = true;
      user.emailVerificationToken = undefined;
      user.emailVerificationExpires = undefined;
      await user.save();

      logger.info(`Email verified for user: ${user.email}`);
    } catch (error) {
      logger.error("Email verification error:", error);
      throw error;
    }
  }

  /**
   * Resend verification email
   */
  static async resendVerificationEmail(email: string): Promise<void> {
    try {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        throw createError("User not found", 404);
      }

      if (user.isEmailVerified) {
        throw createError("Email already verified", 400);
      }

      // Generate new verification token
      const verificationToken = JWTService.generateEmailVerificationToken(
        user.email
      );
      user.emailVerificationToken = verificationToken;
      user.emailVerificationExpires = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      );
      await user.save();

      // Send verification email
      await this.sendVerificationEmail(
        user.email,
        user.firstName,
        verificationToken
      );

      logger.info(`Verification email resent to: ${user.email}`);
    } catch (error) {
      logger.error("Resend verification email error:", error);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<void> {
    try {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        // Don't reveal if email exists
        logger.warn(
          `Password reset requested for non-existent email: ${email}`
        );
        return;
      }

      // Generate reset token
      const resetToken = JWTService.generatePasswordResetToken(user.email);
      user.passwordResetToken = resetToken;
      user.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
      await user.save();

      // Send reset email
      await this.sendPasswordResetEmail(user.email, user.firstName, resetToken);

      logger.info(`Password reset requested for: ${user.email}`);
    } catch (error) {
      logger.error("Password reset request error:", error);
      throw error;
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(
    token: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Verify token
      const { email } = JWTService.verifyPasswordResetToken(token);

      // Find user
      const user = await User.findOne({ email });
      if (!user) {
        throw createError("Invalid reset token", 400);
      }

      // Update password
      user.password = newPassword;
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      await user.save();

      logger.info(`Password reset for user: ${user.email}`);
    } catch (error) {
      logger.error("Password reset error:", error);
      throw error;
    }
  }

  /**
   * Change password (authenticated user)
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      const user = await User.findById(userId).select("+password");
      if (!user) {
        throw createError("User not found", 404);
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(
        currentPassword
      );
      if (!isCurrentPasswordValid) {
        throw createError("Current password is incorrect", 400);
      }

      // Update password
      user.password = newPassword;
      await user.save();

      logger.info(`Password changed for user: ${user.email}`);
    } catch (error) {
      logger.error("Password change error:", error);
      throw error;
    }
  }

  /**
   * Generate guest token for anonymous access
   */
  static generateGuestToken(displayName?: string): string {
    const guestId = crypto.randomUUID();
    return JWTService.generateGuestTokenWithDisplayName(guestId, displayName);
  }

  /**
   * Send verification email
   */
  private static async sendVerificationEmail(
    email: string,
    firstName: string,
    token: string
  ): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;

    const emailContent = `
      <h2>Welcome to Theramea, ${firstName}!</h2>
      <p>Thank you for joining our mental health support community.</p>
      <p>Please click the link below to verify your email address:</p>
      <a href="${verificationUrl}" style="background-color: #7916ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Verify Email</a>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p>${verificationUrl}</p>
      <p>This link will expire in 24 hours.</p>
      <p>If you didn't create an account with Theramea, please ignore this email.</p>
    `;

    await sendEmail({
      to: email,
      subject: "Verify your Theramea account",
      html: emailContent,
    });
  }

  /**
   * Send password reset email
   */
  private static async sendPasswordResetEmail(
    email: string,
    firstName: string,
    token: string
  ): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;

    const emailContent = `
      <h2>Password Reset Request</h2>
      <p>Hi ${firstName},</p>
      <p>You requested to reset your password for your Theramea account.</p>
      <p>Click the link below to reset your password:</p>
      <a href="${resetUrl}" style="background-color: #7916ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p>${resetUrl}</p>
      <p>This link will expire in 1 hour.</p>
      <p>If you didn't request a password reset, please ignore this email.</p>
    `;

    await sendEmail({
      to: email,
      subject: "Reset your Theramea password",
      html: emailContent,
    });
  }
}

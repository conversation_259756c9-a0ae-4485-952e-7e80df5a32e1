const axios = require("axios");

// Test the payment initialization endpoint
async function testPaymentInitialization() {
  try {
    console.log("Testing payment initialization endpoint...");

    // Test with the correct request body that the validation expects
    const response = await axios.post(
      "http://localhost:5000/api/sessions/689e7d8b8d814e4aae4767af/payment/initialize",
      {
        amount: 5000, // Amount in kobo/cents (50.00 NGN)
        currency: "NGN",
        paymentMethod: "card", // Valid payment method
      },
      {
        headers: {
          "Content-Type": "application/json",
          // Add a mock auth token if needed - you may need a real one
          Authorization: "Bearer mock-token",
        },
      }
    );

    console.log("✅ Response status:", response.status);
    console.log("✅ Response data:", JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error("❌ Error testing payment initialization:");

    if (error.response) {
      console.error("Status:", error.response.status);
      console.error(
        "Response data:",
        JSON.stringify(error.response.data, null, 2)
      );

      // Analyze the specific validation errors
      if (error.response.data.errors) {
        console.log("\n🔍 Validation Issues Found:");
        error.response.data.errors.forEach((err) => {
          console.log(`- ${err.field}: ${err.message}`);
        });

        console.log("\n💡 Solutions:");
        error.response.data.errors.forEach((err) => {
          if (err.field === "amount") {
            console.log(
              "- Amount must be a number greater than 0 (in kobo/cents)"
            );
          }
          if (err.field === "currency") {
            console.log("- Currency must be either 'NGN' or 'USD'");
          }
          if (err.field === "paymentMethod") {
            console.log(
              "- Payment method must be one of: 'card', 'bank_transfer', 'ussd'"
            );
          }
        });
      }
    } else {
      console.error("Network error:", error.message);
    }
  }
}

// Test different valid payment methods
async function testValidPaymentMethods() {
  const validMethods = ["card", "bank_transfer", "ussd"];

  for (const method of validMethods) {
    try {
      console.log(`\n🧪 Testing payment method: ${method}`);

      const response = await axios.post(
        "http://localhost:5000/api/sessions/689e7d8b8d814e4aae4767af/payment/initialize",
        {
          amount: 5000, // 50.00 NGN
          currency: "NGN",
          paymentMethod: method,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer mock-token",
          },
        }
      );

      console.log(`✅ ${method} method works - Status: ${response.status}`);
    } catch (error) {
      if (error.response) {
        console.log(
          `❌ ${method} method failed - Status: ${error.response.status}`
        );
        if (error.response.data.errors) {
          console.log(
            "Validation errors:",
            error.response.data.errors.map((e) => e.message)
          );
        }
      } else {
        console.log(
          `❌ ${method} method failed - Network error: ${error.message}`
        );
      }
    }
  }
}

// Test invalid values to confirm validation works
async function testInvalidValues() {
  console.log("\n🧪 Testing invalid values to confirm validation...");

  const testCases = [
    {
      name: "Invalid amount (0)",
      data: { amount: 0, currency: "NGN", paymentMethod: "card" },
    },
    {
      name: "Invalid currency",
      data: { amount: 5000, currency: "EUR", paymentMethod: "card" },
    },
    {
      name: "Invalid payment method",
      data: { amount: 5000, currency: "NGN", paymentMethod: "crypto" },
    },
    {
      name: "Missing amount",
      data: { currency: "NGN", paymentMethod: "card" },
    },
  ];

  for (const testCase of testCases) {
    try {
      await axios.post(
        "http://localhost:5000/api/sessions/689e7d8b8d814e4aae4767af/payment/initialize",
        testCase.data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer mock-token",
          },
        }
      );
      console.log(`⚠️ ${testCase.name}: Unexpectedly succeeded`);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log(`✅ ${testCase.name}: Correctly rejected`);
      } else {
        console.log(
          `❓ ${testCase.name}: Unexpected error - ${
            error.response?.status || error.message
          }`
        );
      }
    }
  }
}

async function runAllTests() {
  console.log("🚀 Starting Payment Initialization Tests\n");

  await testPaymentInitialization();
  await testValidPaymentMethods();
  await testInvalidValues();

  console.log("\n✨ All tests completed!");
}

runAllTests();

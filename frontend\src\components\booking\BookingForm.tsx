"use client";

import { useState, useEffect } from "react";
import { Counselor, BookingRequest } from "@/types/counselor";
import { counselorAPI } from "@/lib/counselor";
import { useAuthStore } from "@/store/authStore";
import {
  CalendarIcon,
  ClockIcon,
  VideoCameraIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

interface BookingFormProps {
  counselor: Counselor;
  bookingData: Partial<BookingRequest>;
  onChange: (data: Partial<BookingRequest>) => void;
  onNext: () => void;
}

interface AvailableSlot {
  date: string;
  time: string;
  available: boolean;
}

export default function BookingForm({
  counselor,
  bookingData,
  onChange,
  onNext,
}: BookingFormProps) {
  console.log("=== BOOKING FORM DEBUG ===");
  console.log("Received bookingData:", bookingData);
  console.log("counselorId from bookingData:", bookingData.counselorId);

  const { tokens } = useAuthStore();
  const [availableSlots, setAvailableSlots] = useState<AvailableSlot[]>([]);
  const [loadingSlots, setLoadingSlots] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [slotsError, setSlotsError] = useState<string>("");
  const [formData, setFormData] = useState({
    sessionType: bookingData.sessionType || "individual",
    duration: bookingData.duration || 60,
    notes: bookingData.notes || "",
    isUrgent: bookingData.isUrgent || false,
    preferredLanguage: bookingData.preferredLanguage || "en",
  });

  useEffect(() => {
    if (selectedDate) {
      setSelectedTime(""); // Clear selected time when date changes
      fetchAvailableSlots(selectedDate);
    }
  }, [selectedDate, formData.duration]); // Also refetch when duration changes

  const fetchAvailableSlots = async (date: string) => {
    try {
      setLoadingSlots(true);
      setSlotsError("");
      const response = await counselorAPI.getAvailability(
        counselor._id,
        date,
        tokens?.accessToken,
        formData.duration
      );
      setAvailableSlots(response.data.slots);
      if (response.data.slots.length === 0) {
        setSlotsError(
          "No available times for this date. Please select another date."
        );
      }
    } catch (error) {
      console.error("Failed to fetch available slots:", error);
      setSlotsError("Failed to load available times. Please try again.");
      setAvailableSlots([]);
    } finally {
      setLoadingSlots(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);
    // Preserve existing booking data including counselorId
    onChange({ ...bookingData, ...newFormData });
  };

  const handleDateTimeSelection = () => {
    if (selectedDate && selectedTime) {
      const scheduledAt = `${selectedDate}T${selectedTime}:00.000Z`;
      // Preserve existing booking data including counselorId
      onChange({ ...bookingData, ...formData, scheduledAt });
    }
  };

  useEffect(() => {
    handleDateTimeSelection();
  }, [selectedDate, selectedTime]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedDate || !selectedTime) {
      alert("Please select a date and time for your session.");
      return;
    }

    onNext();
  };

  const sessionTypes = [
    {
      value: "individual",
      label: "Individual Session",
      icon: VideoCameraIcon,
      description: "One-on-one counseling session",
    },
    {
      value: "couples",
      label: "Couples Session",
      icon: PhoneIcon,
      description: "Counseling for couples",
    },
    {
      value: "family",
      label: "Family Session",
      icon: ChatBubbleLeftRightIcon,
      description: "Family counseling session",
    },
  ];

  const durations = [
    { value: 30, label: "30 minutes" },
    { value: 45, label: "45 minutes" },
    { value: 60, label: "60 minutes" },
    { value: 90, label: "90 minutes" },
  ];

  const languages = [
    { value: "en", label: "English" },
    { value: "es", label: "Spanish" },
    { value: "fr", label: "French" },
    { value: "de", label: "German" },
  ];

  // Generate next 14 days for date selection
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 1; i <= 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        value: date.toISOString().split("T")[0],
        label: date.toLocaleDateString("en-US", {
          weekday: "short",
          month: "short",
          day: "numeric",
        }),
        disabled: date.getDay() === 0 || date.getDay() === 6, // Disable weekends for now
      });
    }

    return dates;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        Session Details
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Session Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Session Type
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {sessionTypes.map((type) => {
              const Icon = type.icon;
              return (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => handleInputChange("sessionType", type.value)}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    formData.sessionType === type.value
                      ? "border-purple-600 bg-purple-50 text-purple-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <Icon className="h-5 w-5" />
                    <span className="font-medium">{type.label}</span>
                  </div>
                  <p className="text-sm text-gray-600">{type.description}</p>
                </button>
              );
            })}
          </div>
        </div>

        {/* Duration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Session Duration
          </label>
          <select
            value={formData.duration}
            onChange={(e) =>
              handleInputChange("duration", parseInt(e.target.value))
            }
            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
          >
            {durations.map((duration) => (
              <option key={duration.value} value={duration.value}>
                {duration.label}
              </option>
            ))}
          </select>
        </div>

        {/* Date Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <CalendarIcon className="h-4 w-4 inline mr-1" />
            Select Date
          </label>
          <select
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            required
          >
            <option value="">Choose a date</option>
            {getAvailableDates().map((date) => (
              <option
                key={date.value}
                value={date.value}
                disabled={date.disabled}
              >
                {date.label}
              </option>
            ))}
          </select>
        </div>

        {/* Time Selection */}
        {selectedDate && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <ClockIcon className="h-4 w-4 inline mr-1" />
              Select Time
            </label>
            {loadingSlots ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">
                  Loading available times...
                </p>
              </div>
            ) : slotsError ? (
              <div className="text-center py-4">
                <p className="text-sm text-red-600">{slotsError}</p>
              </div>
            ) : (
              <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                {availableSlots.length > 0 ? (
                  availableSlots.map((slot) => (
                    <button
                      key={slot.time}
                      type="button"
                      onClick={() => setSelectedTime(slot.time)}
                      disabled={!slot.available}
                      className={`p-2 text-sm rounded-md border transition-colors ${
                        selectedTime === slot.time
                          ? "border-purple-600 bg-purple-600 text-white"
                          : slot.available
                          ? "border-gray-300 hover:border-purple-300 hover:bg-purple-50"
                          : "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
                      }`}
                    >
                      {slot.time}
                    </button>
                  ))
                ) : (
                  <div className="col-span-full text-center py-4 text-gray-500">
                    No available times for this date
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Language Preference */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Language
          </label>
          <select
            value={formData.preferredLanguage}
            onChange={(e) =>
              handleInputChange("preferredLanguage", e.target.value)
            }
            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
          >
            {languages.map((language) => (
              <option key={language.value} value={language.value}>
                {language.label}
              </option>
            ))}
          </select>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Additional Notes (Optional)
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange("notes", e.target.value)}
            rows={4}
            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
            placeholder="Share any specific concerns or topics you'd like to discuss..."
          />
        </div>

        {/* Urgent Session */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="isUrgent"
            checked={formData.isUrgent}
            onChange={(e) => handleInputChange("isUrgent", e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label
            htmlFor="isUrgent"
            className="flex items-center text-sm text-gray-700"
          >
            <ExclamationTriangleIcon className="h-4 w-4 text-orange-500 mr-1" />
            This is an urgent session (additional fees may apply)
          </label>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-4">
          <button
            type="submit"
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            Continue to Review
          </button>
        </div>
      </form>
    </div>
  );
}

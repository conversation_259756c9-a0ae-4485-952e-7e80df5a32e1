import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import { Heart, Scale, Users, Shield, BookOpen, Gavel } from "lucide-react";

const ethicalPrinciples = [
  {
    icon: Heart,
    title: "Beneficence and Non-maleficence",
    description:
      "We are committed to promoting the welfare of our users while avoiding harm. All our services are designed to support mental health and wellbeing.",
    details: [
      "Prioritize client safety and wellbeing in all decisions",
      "Regularly assess and minimize potential risks",
      "Provide crisis intervention and emergency resources",
      "Maintain evidence-based practices and treatments",
    ],
  },
  {
    icon: Scale,
    title: "Justice and Fairness",
    description:
      "We ensure equitable access to mental health services regardless of background, identity, or circumstances.",
    details: [
      "Provide affordable and accessible mental health care",
      "Eliminate barriers to treatment for underserved populations",
      "Maintain fair and transparent pricing",
      "Respect cultural diversity and individual differences",
    ],
  },
  {
    icon: Shield,
    title: "Autonomy and Informed Consent",
    description:
      "We respect users' right to make informed decisions about their mental health care and treatment.",
    details: [
      "Obtain clear informed consent for all services",
      "Respect client choice in treatment decisions",
      "Provide comprehensive information about treatment options",
      "Honor client preferences and values",
    ],
  },
  {
    icon: Users,
    title: "Confidentiality and Privacy",
    description:
      "We maintain the highest standards of confidentiality and privacy protection for all user information.",
    details: [
      "Protect all personal and health information",
      "Limit information sharing to necessary circumstances",
      "Obtain consent before any information disclosure",
      "Maintain secure systems and protocols",
    ],
  },
];

const professionalStandards = [
  {
    title: "Counselor Qualifications",
    requirements: [
      "Active professional license in mental health",
      "Master's degree or higher in relevant field",
      "Minimum 2 years of clinical experience",
      "Completion of ethics training programs",
      "Clean background check and reference verification",
      "Ongoing continuing education requirements",
    ],
  },
  {
    title: "Clinical Practice Standards",
    requirements: [
      "Evidence-based treatment approaches",
      "Cultural competency and sensitivity",
      "Trauma-informed care principles",
      "Regular clinical supervision",
      "Professional development and training",
      "Adherence to scope of practice",
    ],
  },
  {
    title: "Platform Conduct",
    requirements: [
      "Professional communication at all times",
      "Timely response to client communications",
      "Appropriate boundary maintenance",
      "Crisis intervention protocols",
      "Documentation and record-keeping standards",
      "Reporting of ethical concerns",
    ],
  },
];

export default function EthicsPage() {
  return (
<div className="min-h-screen bg-gray-50">
        <Header />
      {/* Hero Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <Gavel className="h-16 w-16 text-blue-600 mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            Code of Ethics
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Our commitment to ethical practice guides every aspect of our mental
            health platform, ensuring the highest standards of care and
            professional conduct.
          </p>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-8">
              <h2 className="text-2xl font-bold mb-4 text-blue-800">
                Our Ethical Mission
              </h2>
              <p className="text-blue-700 text-lg leading-relaxed">
                Theramea is committed to providing ethical, professional, and
                compassionate mental health services. We adhere to the highest
                standards of professional ethics as established by leading
                mental health organizations, including the American
                Psychological Association (APA), American Counseling Association
                (ACA), and National Association of Social Workers (NASW).
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Ethical Principles */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Core Ethical Principles
            </h2>

            <div className="space-y-12">
              {ethicalPrinciples.map((principle, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-8">
                  <div className="flex items-start gap-6">
                    <principle.icon className="h-12 w-12 text-blue-600 flex-shrink-0 mt-2" />
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-4 text-gray-800">
                        {principle.title}
                      </h3>
                      <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                        {principle.description}
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {principle.details.map((detail, detailIndex) => (
                          <div
                            key={detailIndex}
                            className="flex items-start gap-2"
                          >
                            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700">{detail}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Professional Standards */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Professional Standards
            </h2>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {professionalStandards.map((standard, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-xl font-bold mb-6 text-gray-800">
                    {standard.title}
                  </h3>
                  <ul className="space-y-3">
                    {standard.requirements.map((requirement, reqIndex) => (
                      <li key={reqIndex} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-600 text-sm">
                          {requirement}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Ethical Decision Making */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Ethical Decision-Making Framework
            </h2>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-8">
              <div className="flex items-center gap-3 mb-6">
                <BookOpen className="h-8 w-8 text-purple-600" />
                <h3 className="text-2xl font-bold text-purple-800">
                  Our Decision-Making Process
                </h3>
              </div>

              <p className="text-purple-700 mb-6 leading-relaxed">
                When facing ethical dilemmas, our team follows a structured
                decision-making process to ensure the best outcomes for our
                users while maintaining professional integrity.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold mb-3 text-purple-800">
                    Process Steps:
                  </h4>
                  <ol className="space-y-2 text-purple-700">
                    <li>1. Identify the ethical issue and stakeholders</li>
                    <li>2. Gather relevant facts and information</li>
                    <li>3. Consider applicable ethical principles and codes</li>
                    <li>4. Identify possible courses of action</li>
                    <li>5. Evaluate consequences and risks</li>
                    <li>6. Consult with colleagues and supervisors</li>
                    <li>7. Make and implement the decision</li>
                    <li>8. Monitor outcomes and adjust as needed</li>
                  </ol>
                </div>
                <div>
                  <h4 className="text-lg font-semibold mb-3 text-purple-800">
                    Key Considerations:
                  </h4>
                  <ul className="space-y-2 text-purple-700">
                    <li>• Client welfare and safety</li>
                    <li>• Legal and regulatory requirements</li>
                    <li>• Professional standards and guidelines</li>
                    <li>• Cultural and individual factors</li>
                    <li>• Potential for harm or benefit</li>
                    <li>• Long-term implications</li>
                    <li>• Precedent and consistency</li>
                    <li>• Resource availability and limitations</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Reporting and Compliance */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Reporting Ethical Concerns
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-bold mb-4 text-gray-800">
                  For Users and Clients
                </h3>
                <p className="text-gray-600 mb-4">
                  If you have concerns about ethical conduct or professional
                  standards, we encourage you to report them through our
                  confidential channels.
                </p>
                <ul className="space-y-2 text-gray-600 mb-4">
                  <li>• Ethics hotline: 1-800-ETHICS-T</li>
                  <li>• Email: <EMAIL></li>
                  <li>• Online reporting form</li>
                  <li>• Direct contact with your counselor's supervisor</li>
                </ul>
                <p className="text-sm text-gray-500">
                  All reports are investigated promptly and confidentially. No
                  retaliation will occur for good faith reporting.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-bold mb-4 text-gray-800">
                  For Counselors and Staff
                </h3>
                <p className="text-gray-600 mb-4">
                  Professional staff have a duty to report ethical violations
                  and must follow our established reporting procedures.
                </p>
                <ul className="space-y-2 text-gray-600 mb-4">
                  <li>• Immediate supervisor notification</li>
                  <li>• Ethics committee review</li>
                  <li>• Professional licensing board reporting</li>
                  <li>• Internal investigation procedures</li>
                </ul>
                <p className="text-sm text-gray-500">
                  Staff receive regular ethics training and have access to
                  consultation resources for ethical decision-making.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Continuous Improvement */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-8 text-gray-800">
              Commitment to Continuous Improvement
            </h2>

            <div className="bg-green-50 border border-green-200 rounded-lg p-8">
              <p className="text-green-700 text-lg mb-6 leading-relaxed">
                We regularly review and update our ethical standards to reflect
                evolving best practices, research findings, and community
                feedback. Our commitment to ethical excellence is ongoing and
                adaptive.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <BookOpen className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-green-800">
                    Regular Training
                  </h4>
                  <p className="text-green-700 text-sm">
                    Ongoing ethics education for all staff
                  </p>
                </div>
                <div className="text-center">
                  <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-green-800">
                    Community Input
                  </h4>
                  <p className="text-green-700 text-sm">
                    Feedback from users and stakeholders
                  </p>
                </div>
                <div className="text-center">
                  <Scale className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-green-800">
                    External Review
                  </h4>
                  <p className="text-green-700 text-sm">
                    Independent ethics committee oversight
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              Ethics Questions or Concerns?
            </h2>
            <p className="text-gray-600 mb-8">
              Our Ethics Committee is available to address any questions about
              our code of ethics or to receive reports of ethical concerns.
            </p>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">
                Contact Our Ethics Committee
              </h3>
              <div className="text-gray-600">
                <p>
                  <strong>Phone:</strong> 1-800-ETHICS-T (**************)
                </p>
                <p>
                  <strong>Email:</strong> <EMAIL>
                </p>
                <p>
                  <strong>Mail:</strong> Ethics Committee, Theramea Inc., 123
                  Wellness Way, Suite 400, New York, NY 10001
                </p>
              </div>
              <div className="mt-6">
                <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors mr-4">
                  Report a Concern
                </button>
                <button className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors">
                  Download Full Code
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
}

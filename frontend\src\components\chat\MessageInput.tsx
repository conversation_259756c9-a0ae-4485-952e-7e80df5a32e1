"use client";

import { useState, useRef, useEffect } from "react";
import { ChatMessage } from "@/types/chat";
import { Send, Smile, Image, File, X, Plus, Camera } from "lucide-react";

interface MessageInputProps {
  onSendMessage: (content: { text: string; type: "text" }) => void;
  onTypingStart: () => void;
  onTypingStop: () => void;
  replyTo?: ChatMessage | null;
  onCancelReply?: () => void;
  disabled?: boolean;
  placeholder?: string;
}

export default function MessageInput({
  onSendMessage,
  onTypingStart,
  onTypingStop,
  replyTo,
  onCancelReply,
  disabled = false,
  placeholder = "Type your message...",
}: MessageInputProps) {
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle typing indicators
    if (value.trim() && !isTyping) {
      setIsTyping(true);
      onTypingStart();
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        onTypingStop();
      }
    }, 1000);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const trimmedMessage = message.trim();
    if (!trimmedMessage || disabled) return;

    onSendMessage({
      text: trimmedMessage,
      type: "text",
    });

    setMessage("");

    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      onTypingStop();
    }

    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="relative">
      {/* Reply Context */}
      {replyTo && (
        <div className="mx-6 mb-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-2xl border-l-4 border-blue-400 shadow-sm">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {replyTo.senderDisplayName?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
                <p className="text-sm font-medium text-blue-700">
                  Replying to {replyTo.senderDisplayName}
                </p>
              </div>
              <p className="text-sm text-gray-700 bg-white/50 rounded-lg px-3 py-2 line-clamp-2">
                {replyTo.content.text || "📎 Media message"}
              </p>
            </div>
            <button
              onClick={onCancelReply}
              className="ml-3 p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200"
              title="Cancel reply"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Attached Files Preview */}
      {attachedFiles.length > 0 && (
        <div className="mx-6 mb-3 p-3 bg-gray-50 rounded-xl">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              {attachedFiles.length} file{attachedFiles.length > 1 ? "s" : ""}{" "}
              attached
            </span>
            <button
              onClick={() => setAttachedFiles([])}
              className="text-gray-400 hover:text-red-500 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          <div className="space-y-2">
            {attachedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-2 bg-white rounded-lg"
              >
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <File className="w-4 h-4 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(file.size / 1024).toFixed(1)} KB
                  </p>
                </div>
                <button
                  onClick={() =>
                    setAttachedFiles((files) =>
                      files.filter((_, i) => i !== index)
                    )
                  }
                  className="text-gray-400 hover:text-red-500 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* WhatsApp-Style Message Input */}
      <div className="bg-gray-50 border-t border-gray-200 px-4 py-3">
        <form onSubmit={handleSubmit} className="relative">
          {/* Main Input Container */}
          <div className="relative bg-white rounded-3xl border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 focus-within:ring-1 focus-within:ring-green-500 focus-within:border-green-500">
            {/* Input Area */}
            <div className="flex items-end">
              {/* Left Action Buttons */}
              <div className="flex items-center space-x-1 px-3 py-3">
                {/* Attachment Menu */}
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
                    disabled={disabled}
                    className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50"
                    title="Attach files"
                  >
                    <Plus className="w-5 h-5" />
                  </button>

                  {/* Attachment Menu Dropdown */}
                  {showAttachmentMenu && (
                    <div className="absolute bottom-full left-0 mb-2 bg-white rounded-2xl shadow-xl border border-gray-200/50 p-2 min-w-48 animate-fade-in-up">
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                      >
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <File className="w-4 h-4 text-blue-600" />
                        </div>
                        <span className="text-sm font-medium">Upload File</span>
                      </button>
                      <button
                        type="button"
                        className="w-full flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                      >
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                          <Image className="w-4 h-4 text-green-600" />
                        </div>
                        <span className="text-sm font-medium">
                          Upload Image
                        </span>
                      </button>
                      <button
                        type="button"
                        className="w-full flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                      >
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Camera className="w-4 h-4 text-purple-600" />
                        </div>
                        <span className="text-sm font-medium">Take Photo</span>
                      </button>
                    </div>
                  )}
                </div>

                {/* Emoji Button */}
                <button
                  type="button"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  disabled={disabled}
                  className="p-2 text-gray-400 hover:text-yellow-500 hover:bg-yellow-50 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50"
                  title="Add emoji"
                >
                  <Smile className="w-5 h-5" />
                </button>
              </div>

              {/* WhatsApp-Style Text Input */}
              <div className="flex-1 px-3">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  placeholder={placeholder}
                  disabled={disabled}
                  rows={1}
                  className="w-full py-2.5 bg-transparent border-none outline-none resize-none max-h-32 placeholder-gray-500 text-gray-900 disabled:cursor-not-allowed text-sm leading-relaxed"
                />
              </div>

              {/* WhatsApp-Style Send Button */}
              <div className="flex items-center px-2 py-2">
                <button
                  type="submit"
                  disabled={disabled || message.trim() === ""}
                  className="p-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg"
                  title="Send message"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </form>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*,.pdf,.doc,.docx,.txt"
          className="hidden"
          onChange={(e) => {
            if (e.target.files) {
              const newFiles = Array.from(e.target.files);
              setAttachedFiles((prev) => [...prev, ...newFiles]);
              setShowAttachmentMenu(false);
              // Reset the input so the same file can be selected again
              e.target.value = "";
            }
          }}
        />
      </div>

      {/* WhatsApp-Style Emoji Picker */}
      {showEmojiPicker && (
        <div className="absolute bottom-full left-4 mb-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-3 animate-fade-in-up z-20 max-w-sm">
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              Frequently Used
            </h3>
            <div className="grid grid-cols-8 gap-1">
              {["😀", "😂", "❤️", "😍", "😊", "🤔", "👍", "🙏"].map((emoji) => (
                <button
                  key={emoji}
                  type="button"
                  onClick={() => {
                    setMessage((prev) => prev + emoji);
                    setShowEmojiPicker(false);
                    textareaRef.current?.focus();
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-lg hover:scale-110 active:scale-95"
                  title={`Add ${emoji}`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              All Emojis
            </h3>
            <div className="grid grid-cols-8 gap-1 max-h-40 overflow-y-auto">
              {[
                "😀",
                "😃",
                "😄",
                "😁",
                "😆",
                "😅",
                "😂",
                "🤣",
                "😊",
                "😇",
                "🙂",
                "🙃",
                "😉",
                "😌",
                "😍",
                "🥰",
                "😘",
                "😗",
                "😙",
                "😚",
                "😋",
                "😛",
                "😝",
                "😜",
                "🤪",
                "🤨",
                "🧐",
                "🤓",
                "😎",
                "🤩",
                "🥳",
                "😏",
                "😒",
                "😞",
                "😔",
                "😟",
                "😕",
                "🙁",
                "☹️",
                "😣",
                "😖",
                "😫",
                "😩",
                "🥺",
                "😢",
                "😭",
                "😤",
                "😠",
                "😡",
                "🤬",
                "🤯",
                "😳",
                "🥵",
                "🥶",
                "😱",
                "😨",
                "😰",
                "😥",
                "😓",
                "🤗",
                "🤔",
                "🤭",
                "🤫",
                "🤥",
                "😶",
                "😐",
                "😑",
                "😬",
                "🙄",
                "😯",
                "😦",
                "😧",
                "😮",
                "😲",
                "🥱",
                "😴",
                "🤤",
                "😪",
                "😵",
                "🤐",
                "🥴",
                "🤢",
                "🤮",
                "🤧",
                "😷",
                "🤒",
                "🤕",
                "🤑",
                "🤠",
                "😈",
                "👿",
                "👹",
                "👺",
                "🤡",
                "💩",
                "👻",
                "💀",
                "☠️",
                "👽",
                "👾",
                "🤖",
                "🎃",
                "😺",
                "😸",
                "😹",
                "😻",
                "😼",
                "😽",
                "🙀",
                "😿",
                "😾",
                "❤️",
                "🧡",
                "💛",
                "💚",
                "💙",
                "💜",
                "🖤",
                "🤍",
                "🤎",
                "💔",
                "❣️",
                "💕",
                "💞",
                "💓",
                "💗",
                "💖",
                "💘",
                "💝",
                "💟",
                "☮️",
                "✝️",
                "☪️",
                "🕉️",
                "☸️",
                "✡️",
                "🔯",
                "🕎",
                "☯️",
                "☦️",
                "🛐",
                "⛎",
                "♈",
                "♉",
                "♊",
                "♋",
                "♌",
                "♍",
                "♎",
                "♏",
                "♐",
                "♑",
                "♒",
                "♓",
                "🆔",
                "⚛️",
                "🉑",
                "☢️",
                "☣️",
                "📴",
              ].map((emoji) => (
                <button
                  key={emoji}
                  type="button"
                  onClick={() => {
                    setMessage((prev) => prev + emoji);
                    setShowEmojiPicker(false);
                    textareaRef.current?.focus();
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-lg hover:scale-110 active:scale-95"
                  title={`Add ${emoji}`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Test API call to debug authorization issue
const fetch = require("node-fetch");

async function testAPICall() {
  try {
    console.log("Making test API call...");

    // This token is from the logs - user ID: 687fe3b739da96aef19699d3
    const token =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODdmZTNiNzM5ZGE5NmFlZjE5Njk5ZDMiLCJyb2xlIjoidXNlciIsImlhdCI6MTcyMzY3ODEzMCwiZXhwIjoxNzIzNzY0NTMwfQ.UF_FKdgbIZDJ8WO3E4zBNV6XKMJP7-5Roc45MNp8_fY";

    const sessionId = "689e688440af152213173417";
    const url = `http://localhost:5000/api/sessions/${sessionId}`;

    console.log("URL:", url);
    console.log("Session ID:", sessionId);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    console.log("Response status:", response.status);

    const data = await response.json();
    console.log("Response data:", JSON.stringify(data, null, 2));
  } catch (error) {
    console.error("Error:", error);
  }
}

testAPICall();

import {
  Video,
  MessageCircle,
  Calendar,
  Lock,
  Clock,
  Users,
} from "lucide-react";

const features = [
  {
    icon: Video,
    title: "1-on-1 Video Sessions",
    description:
      "Connect with licensed counselors through secure video calls from the comfort of your home.",
  },
  {
    icon: MessageCircle,
    title: "Chat Support",
    description:
      "Access 24/7 chat support and join group discussions with people facing similar challenges.",
  },
  {
    icon: Calendar,
    title: "Flexible Scheduling",
    description:
      "Book sessions that fit your schedule with easy rescheduling and reminder notifications.",
  },
  {
    icon: Lock,
    title: "Complete Privacy",
    description:
      "Your conversations are completely confidential and protected with end-to-end encryption.",
  },
  {
    icon: Clock,
    title: "Available 24/7",
    description:
      "Get support when you need it most with round-the-clock availability and crisis support.",
  },
  {
    icon: Users,
    title: "Group Therapy",
    description:
      "Join specialized group sessions and connect with others in a supportive environment.",
  },
];

export default function Features() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need for Better Mental Health
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our comprehensive platform provides all the tools and support you
            need to improve your mental well-being.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                <feature.icon className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

"use client";

import { useEffect } from "react";
import { useAuthStore } from "@/store/authStore";

interface HydrationProviderProps {
  children: React.ReactNode;
}

export function HydrationProvider({ children }: HydrationProviderProps) {
  const setHydrated = useAuthStore((state) => state.setHydrated);

  useEffect(() => {
    // Mark the store as hydrated after client-side rendering
    setHydrated();
  }, [setHydrated]);

  return <>{children}</>;
}

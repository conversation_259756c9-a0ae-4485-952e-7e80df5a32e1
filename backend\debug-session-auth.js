const axios = require('axios');
const mongoose = require('mongoose');

const BASE_URL = 'http://localhost:5000/api';
const MONGODB_URI = "*********************************************************************";

async function debugSessionAuth() {
  try {
    console.log('🔍 Debugging Session Authorization Issue...\n');

    // Connect to MongoDB to check session data directly
    console.log('1. Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get session data directly from database
    const sessionId = '689e5af766e30bf744d47aeb';
    const session = await mongoose.connection.db
      .collection('sessions')
      .findOne({ _id: new mongoose.Types.ObjectId(sessionId) });

    console.log('\n2. Session data from database:');
    console.log('Session ID:', session._id.toString());
    console.log('User ID:', session.userId.toString());
    console.log('User ID type:', typeof session.userId);
    console.log('Counselor ID:', session.counselorId.toString());
    console.log('Status:', session.status);

    // Authenticate and get user data
    console.log('\n3. Authenticating user...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Authentication failed');
      return;
    }

    const authToken = loginResponse.data.data.tokens.accessToken;
    const userData = loginResponse.data.data.user;
    
    console.log('✅ Authentication successful');
    console.log('Authenticated User ID:', userData._id);
    console.log('Authenticated User ID type:', typeof userData._id);

    // Compare IDs
    console.log('\n4. ID Comparison:');
    console.log('Session User ID:', session.userId.toString());
    console.log('Auth User ID:', userData._id);
    console.log('IDs Match (string comparison):', session.userId.toString() === userData._id);
    console.log('IDs Match (loose comparison):', session.userId.toString() == userData._id);

    // Try to get the session via API to see what the BookingService returns
    console.log('\n5. Testing session retrieval via API...');
    try {
      const sessionResponse = await axios.get(
        `${BASE_URL}/sessions/${sessionId}`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✅ Session retrieved via API');
      const apiSession = sessionResponse.data.data.session;
      console.log('API Session User ID:', apiSession.userId);
      console.log('API Session User ID type:', typeof apiSession.userId);
      console.log('API vs Auth User ID match:', apiSession.userId === userData._id);
      
    } catch (error) {
      console.log('❌ Failed to retrieve session via API');
      if (error.response) {
        console.log('Status:', error.response.status);
        console.log('Error:', error.response.data);
      }
    }

    // Now try the cancellation
    console.log('\n6. Testing cancellation...');
    try {
      const cancelResponse = await axios.put(
        `${BASE_URL}/sessions/${sessionId}/cancel`,
        { reason: 'Debug test cancellation' },
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✅ Cancellation successful!');
      console.log('Response:', cancelResponse.data);
      
    } catch (error) {
      console.log('❌ Cancellation failed');
      if (error.response) {
        console.log('Status:', error.response.status);
        console.log('Error:', error.response.data);
      }
    }

  } catch (error) {
    console.log('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

debugSessionAuth();

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";

const AREAS_OF_INTEREST = [
  { value: "relationships", label: "Relationships", emoji: "💕" },
  { value: "school", label: "School", emoji: "📚" },
  { value: "anxiety", label: "Anxiety", emoji: "😰" },
  { value: "addiction", label: "Addiction", emoji: "🔗" },
  { value: "stress", label: "Stress", emoji: "😤" },
  { value: "mental-health", label: "Mental Health", emoji: "🧠" },
  { value: "depression", label: "Depression", emoji: "😔" },
  { value: "family", label: "Family Issues", emoji: "👨‍👩‍👧‍👦" },
  { value: "career", label: "Career", emoji: "💼" },
  { value: "self-discovery", label: "Self Discovery", emoji: "🔍" },
  { value: "grief", label: "Grief & Loss", emoji: "💔" },
  { value: "trauma", label: "Trauma", emoji: "⚡" },
  { value: "anger-management", label: "Anger Management", emoji: "😡" },
  { value: "eating-disorders", label: "Eating Disorders", emoji: "🍽️" },
  { value: "sleep-issues", label: "Sleep Issues", emoji: "😴" },
];

export default function InterestsOnboardingPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuthStore();
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleInterestToggle = (interest: string) => {
    setSelectedInterests((prev) => {
      if (prev.includes(interest)) {
        return prev.filter((i) => i !== interest);
      } else if (prev.length < 3) {
        return [...prev, interest];
      }
      return prev; // Don't add if already at max
    });
  };

  const handleContinue = async () => {
    if (selectedInterests.length === 0) {
      return; // Don't allow empty selection
    }

    setIsSubmitting(true);

    try {
      // TODO: Update user profile with selected interests
      // For now, we'll store in localStorage and continue
      localStorage.setItem(
        "onboarding_interests",
        JSON.stringify(selectedInterests)
      );
      router.push("/onboarding/mood");
    } catch (error) {
      console.error("Error saving interests:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    router.push("/onboarding/mood");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">🎯</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            What topics matter to you most?
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Select 1-3 areas you'd like support with. This helps us personalize
            your experience.
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <div className="w-8 h-2 bg-purple-600 rounded-full"></div>
            <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
            <span>Step 1 of 2</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {AREAS_OF_INTEREST.map((area) => (
              <button
                key={area.value}
                onClick={() => handleInterestToggle(area.value)}
                disabled={
                  !selectedInterests.includes(area.value) &&
                  selectedInterests.length >= 3
                }
                className={`
                  p-4 rounded-lg border-2 text-left transition-all duration-200
                  ${
                    selectedInterests.includes(area.value)
                      ? "border-purple-500 bg-purple-50 text-purple-900"
                      : "border-gray-200 bg-white text-gray-700 hover:border-purple-300 hover:bg-purple-25"
                  }
                  ${
                    !selectedInterests.includes(area.value) &&
                    selectedInterests.length >= 3
                      ? "opacity-50 cursor-not-allowed"
                      : "cursor-pointer"
                  }
                `}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{area.emoji}</span>
                  <span className="font-medium">{area.label}</span>
                </div>
              </button>
            ))}
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              Selected: {selectedInterests.length}/3
            </p>
          </div>
        </div>

        <div className="flex space-x-4">
          <button
            onClick={handleSkip}
            className="flex-1 py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Skip for now
          </button>
          <button
            onClick={handleContinue}
            disabled={selectedInterests.length === 0 || isSubmitting}
            className="flex-1 py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </div>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

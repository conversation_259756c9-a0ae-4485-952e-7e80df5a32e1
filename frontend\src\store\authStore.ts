import { create } from "zustand";
import { persist } from "zustand/middleware";
import { authAPI, RegisterData } from "@/lib/auth";
import { User, AuthTokens } from "@/types/auth";
import { createServerSession, clearServerSession } from "@/lib/sessionSync";

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isGuest: boolean;
  guestToken: string | null;
  isLoading: boolean;
  error: string | null;
  lastAuthCheck: number | null;
  hasHydrated: boolean; // Add hydration state
}

interface AuthActions {
  login: (credentials: { email: string; password: string }) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  signup: (userData: RegisterData) => Promise<void>; // Alias for register
  logout: () => Promise<void>;
  checkAuth: (force?: boolean) => Promise<void>; // Add force parameter
  generateGuestToken: (displayName?: string) => Promise<void>;
  clearError: () => void;
  refreshTokens: () => Promise<void>;
  setHydrated: () => void; // Add hydration setter
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      tokens: null,
      isAuthenticated: false,
      isGuest: false,
      guestToken: null,
      isLoading: false,
      error: null,
      lastAuthCheck: null,
      hasHydrated: false,

      // Actions
      login: async (credentials: { email: string; password: string }) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authAPI.login(credentials);

          // Create server-side session
          await createServerSession({
            userId: response.data.user._id,
            email: response.data.user.email,
            role: response.data.user.role,
            isGuest: false,
            isEmailVerified: response.data.user.isEmailVerified,
          });

          set({
            user: response.data.user,
            tokens: response.data.tokens,
            isAuthenticated: true,
            isGuest: false,
            guestToken: null,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || "Login failed",
            isAuthenticated: false,
            user: null,
            tokens: null,
          });
          throw error;
        }
      },

      register: async (userData: RegisterData) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authAPI.register(userData);

          // Create server-side session
          await createServerSession({
            userId: response.data.user._id,
            email: response.data.user.email,
            role: response.data.user.role,
            isGuest: false,
            isEmailVerified: response.data.user.isEmailVerified,
          });

          set({
            user: response.data.user,
            tokens: response.data.tokens,
            isAuthenticated: true,
            isGuest: false,
            guestToken: null,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || "Registration failed",
            isAuthenticated: false,
            user: null,
            tokens: null,
          });
          throw error;
        }
      },

      // Alias for register to maintain compatibility
      signup: async (userData: RegisterData) => {
        return get().register(userData);
      },

      logout: async () => {
        const { tokens } = get();

        try {
          if (tokens?.accessToken) {
            await authAPI.logout(tokens.accessToken);
          }
          // Clear server-side session
          await clearServerSession();
        } catch (error) {
          console.error("Logout error:", error);
        } finally {
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isGuest: false,
            guestToken: null,
            isLoading: false,
            error: null,
          });
        }
      },

      checkAuth: async (force: boolean = false) => {
        const { tokens, guestToken, lastAuthCheck } = get();

        // Cache auth checks for 30 seconds unless forced
        const CACHE_DURATION = 30 * 1000; // 30 seconds
        const now = Date.now();

        if (!force && lastAuthCheck && now - lastAuthCheck < CACHE_DURATION) {
          return; // Skip if recently checked
        }

        if (!tokens?.accessToken && !guestToken) {
          set({ isLoading: false, lastAuthCheck: now });
          return;
        }

        set({ isLoading: true });

        try {
          const token = tokens?.accessToken || guestToken;
          if (!token) {
            set({ isLoading: false, lastAuthCheck: now });
            return;
          }

          // Validate token format before sending to API
          if (typeof token !== "string" || token.split(".").length !== 3) {
            console.warn("Invalid token format detected, clearing auth state");
            set({
              user: null,
              tokens: null,
              isAuthenticated: false,
              isGuest: false,
              guestToken: null,
              isLoading: false,
              lastAuthCheck: now,
            });
            return;
          }

          const response = await authAPI.checkAuth(token);

          if (response.success && response.authenticated) {
            // Handle guest authentication
            if (response.isGuest) {
              set({
                user: response.data?.user || null,
                isAuthenticated: true,
                isGuest: true,
                isLoading: false,
                lastAuthCheck: now,
              });
            } else {
              // Handle regular user authentication
              set({
                user: response.data?.user || null,
                isAuthenticated: true,
                isGuest: false,
                isLoading: false,
                lastAuthCheck: now,
              });
            }
          } else {
            // Token is invalid
            set({
              user: null,
              tokens: null,
              isAuthenticated: false,
              isGuest: false,
              guestToken: null,
              isLoading: false,
              lastAuthCheck: now,
            });
          }
        } catch (error) {
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isGuest: false,
            guestToken: null,
            isLoading: false,
            lastAuthCheck: now,
          });
        }
      },

      generateGuestToken: async (displayName?: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await authAPI.generateGuestToken(displayName);

          // Validate the received token
          if (
            !response.data.token ||
            typeof response.data.token !== "string" ||
            response.data.token.split(".").length !== 3
          ) {
            throw new Error("Invalid token format received from server");
          }

          // Decode the guest token to get user info
          const tokenPayload = JSON.parse(
            atob(response.data.token.split(".")[1])
          );

          // Create server-side session for guest
          await createServerSession({
            userId: tokenPayload.userId,
            role: tokenPayload.role,
            isGuest: true,
            isEmailVerified: false,
          });

          set({
            guestToken: response.data.token,
            isGuest: true,
            isAuthenticated: true,
            user: null,
            tokens: null,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || "Failed to generate guest token",
          });
          throw error;
        }
      },

      refreshTokens: async () => {
        const { tokens } = get();

        if (!tokens?.refreshToken) {
          throw new Error("No refresh token available");
        }

        try {
          const response = await authAPI.refreshToken(tokens.refreshToken);

          set({
            tokens: response.data.tokens,
            user: response.data.user,
            isAuthenticated: true,
          });
        } catch (error: any) {
          // Refresh failed, logout user
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isGuest: false,
            guestToken: null,
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setHydrated: () => {
        set({ hasHydrated: true });
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        guestToken: state.guestToken,
        isAuthenticated: state.isAuthenticated,
        isGuest: state.isGuest,
      }),
    }
  )
);

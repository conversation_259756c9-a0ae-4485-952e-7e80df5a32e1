import Link from "next/link";
import { ArrowRight, CheckCircle } from "lucide-react";

const benefits = [
  "No waiting lists - get started immediately",
  "Affordable pricing with flexible payment options",
  "Licensed and verified counselors",
  "Complete privacy and confidentiality",
];

export default function CTA() {
  return (
    <section className="py-20 bg-gradient-to-r from-purple-600 to-purple-800 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Ready to Start Your Mental Health Journey?
          </h2>
          <p className="text-xl mb-8 text-purple-100">
            Join thousands of people who have already taken the first step
            towards better mental health.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-10 max-w-2xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center text-left">
                <CheckCircle className="h-6 w-6 text-green-300 mr-3 flex-shrink-0" />
                <span className="text-purple-100">{benefit}</span>
              </div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup"
              className="bg-white text-purple-800 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-50 transition-colors inline-flex items-center justify-center"
            >
              Get Started Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="/resources"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-800 transition-colors inline-flex items-center justify-center"
            >
              Explore Resources
            </Link>
          </div>

          <p className="text-sm text-purple-200 mt-6">
            No commitment required. Cancel anytime.
          </p>
        </div>
      </div>
    </section>
  );
}

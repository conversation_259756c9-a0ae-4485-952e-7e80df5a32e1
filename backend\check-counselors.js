const mongoose = require("mongoose");

const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

async function checkCounselors() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Check all collections
    const collections = await mongoose.connection.db
      .listCollections()
      .toArray();
    console.log("📁 Available collections:");
    collections.forEach((col) => console.log("  -", col.name));

    // Try to find counselors collection
    const counselorSchema = new mongoose.Schema({}, { strict: false });
    const Counselor = mongoose.model(
      "Counselor",
      counselorSchema,
      "counselors"
    );

    const counselorCount = await Counselor.countDocuments();
    console.log("\n👥 Counselors count:", counselorCount);

    if (counselorCount > 0) {
      const counselors = await Counselor.find({}).limit(5).lean();
      console.log("\n📋 Sample counselors:");
      counselors.forEach((doc, i) => {
        console.log(`${i + 1}. ID: ${doc._id}`);
        console.log(
          `   Name: ${doc.name || doc.firstName || doc.fullName || "No name"}`
        );
        console.log(`   Email: ${doc.email || "No email"}`);
        console.log("   Specializations:", doc.specializations || "None");
        console.log("");
      });
    } else {
      console.log("❌ No counselors found in the database");
    }

    // Also check users collection for counselor-type users
    const userSchema = new mongoose.Schema({}, { strict: false });
    const User = mongoose.model("User", userSchema, "users");

    const counselorUsers = await User.find({ role: "counselor" }).lean();
    console.log("\n👤 Users with counselor role:", counselorUsers.length);

    if (counselorUsers.length > 0) {
      console.log("\n📋 Sample counselor users:");
      counselorUsers.slice(0, 5).forEach((doc, i) => {
        console.log(`${i + 1}. ID: ${doc._id}`);
        console.log(
          `   Name: ${doc.name || doc.firstName || doc.fullName || "No name"}`
        );
        console.log(`   Email: ${doc.email || "No email"}`);
        console.log(`   Role: ${doc.role}`);
        console.log("");
      });
    }
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

checkCounselors();

const mongoose = require("mongoose");

// MongoDB connection URI from environment variables or default
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "*********************************************************************";

async function checkSession() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB successfully");

    // Check specific session for cancellation test
    const testSessionId = "689e5af766e30bf744d47aeb";
    const testSession = await mongoose.connection.db
      .collection("sessions")
      .findOne({
        _id: new mongoose.Types.ObjectId(testSessionId),
      });

    console.log(`\n=== Test Session Details ===`);
    if (testSession) {
      console.log(`Session ID: ${testSession._id}`);
      console.log(`User ID: ${testSession.userId}`);
      console.log(`User ID Type: ${typeof testSession.userId}`);
      console.log(`Counselor ID: ${testSession.counselorId}`);
      console.log(`Status: ${testSession.status}`);
      console.log(
        `Payment Status: ${
          testSession.payment?.status || testSession.paymentStatus || "NOT SET"
        }`
      );

      // Check if user ID matches our authenticated user
      const authenticatedUserId = "687fe3b739da96aef19699d3";
      const sessionUserIdStr = testSession.userId.toString();
      console.log(`\n=== Authorization Check ===`);
      console.log(`Authenticated User ID: ${authenticatedUserId}`);
      console.log(`Session User ID (string): ${sessionUserIdStr}`);
      console.log(`IDs Match: ${sessionUserIdStr === authenticatedUserId}`);
    } else {
      console.log("Test session not found!");
    }

    // List all sessions in the database
    const sessions = await mongoose.connection.db
      .collection("sessions")
      .find({})
      .toArray();

    console.log(`\n=== All Sessions (${sessions.length} found) ===`);
    sessions.forEach((session, index) => {
      console.log(`\n${index + 1}. Session ID: ${session._id}`);
      console.log(`   User ID: ${session.userId || "NOT SET"}`);
      console.log(`   Counselor ID: ${session.counselorId || "NOT SET"}`);
      console.log(`   Status: ${session.status || "NOT SET"}`);
      console.log(
        `   Payment Status: ${
          session.payment?.status || session.paymentStatus || "NOT SET"
        }`
      );
      console.log(
        `   Scheduled At: ${
          session.scheduledAt || session.scheduledDate || "NOT SET"
        }`
      );
    });

    if (sessions.length === 0) {
      console.log(
        "No sessions found in database. You may need to create test data first."
      );
    }

    // Also check users to see what user IDs exist
    const users = await mongoose.connection.db
      .collection("users")
      .find({})
      .toArray();
    console.log(`\n=== Available Users (${users.length} found) ===`);
    users.forEach((user) => {
      console.log(
        `User ID: ${user._id} - ${user.firstName || "Unknown"} ${
          user.lastName || ""
        } (${user.email}) - Role: ${user.role}`
      );
    });
  } catch (error) {
    console.error("❌ Database connection error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("\nDisconnected from MongoDB");
  }
}

checkSession();

import mongoose, { Document, Schema } from "mongoose";

export interface ISession extends Document {
  _id: any; // Add explicit _id property
  userId: mongoose.Types.ObjectId;
  clientId: mongoose.Types.ObjectId; // Added for compatibility
  counselorId: mongoose.Types.ObjectId;
  scheduledDate: Date;
  scheduledAt: Date; // Alternative property name used in some places
  duration: number; // in minutes
  actualDuration?: number; // actual session duration
  pricePerMinute: number;
  totalCost: number;
  status:
    | "scheduled"
    | "in-progress"
    | "completed"
    | "cancelled"
    | "no-show"
    | "pending_payment"
    | "pending_approval";
  paymentStatus: "pending" | "paid" | "refunded";
  paymentId: string;
  pricing: {
    totalAmount: number;
    currency: string;
    platformFee: number;
  };
  payment: {
    transactionId?: string;
    status: string;
    reference: string;
    authorizationUrl?: string;
    accessCode?: string;
    paidAt?: Date;
    gateway_response?: any;
    fees?: number;
  };
  type?: string; // session type
  startedAt?: Date;
  endedAt?: Date;
  cancellation?: {
    refundAmount?: number;
    reason?: string;
    cancelledBy?: any;
    cancelledAt?: Date;
    refundEligible?: boolean;
  };
  reschedule?: any;
  approval?: any;
  qualityMetrics?: {
    samples: any[];
    averageQuality: number;
    issues: any[];
  };
  events?: any[];
  videoSession?: {
    roomId?: string;
    roomName?: string;
    roomUrl?: string;
    provider?: string;
    recording?: {
      isRecording: boolean;
      stoppedAt?: Date;
      stoppedBy?: any;
      startedAt?: Date;
    };
    analytics?: any;
    deletedAt?: Date;
    completedAt?: Date;
  };
  notes?: any;
  outcome?: any;
  noShow?: any;
  feedback?: any;
  reminderSent?: boolean;
  refund?: {
    status: string;
    processedAt?: Date;
    amount?: number;
    transactionId?: string;
  };
  dailyRoomUrl?: string;
  sessionNotes?: string;
  rating?: number;
  issueReported?: {
    reportedBy: "user" | "counselor";
    issueType: string;
    description: string;
    reportedAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

const sessionSchema = new Schema<ISession>(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    counselorId: {
      type: Schema.Types.ObjectId,
      ref: "Counselor",
      required: true,
    },
    scheduledDate: { type: Date, required: true },
    duration: { type: Number, required: true, min: 15 },
    actualDuration: { type: Number },
    pricePerMinute: { type: Number, required: true },
    totalCost: { type: Number, required: true },
    status: {
      type: String,
      enum: [
        "scheduled",
        "in-progress",
        "completed",
        "cancelled",
        "no-show",
        "pending_payment",
        "pending_approval",
      ],
      default: "scheduled",
    },
    paymentStatus: {
      type: String,
      enum: ["pending", "paid", "refunded"],
      default: "pending",
    },
    paymentId: String,
    pricing: {
      totalAmount: { type: Number },
      currency: { type: String, default: "USD" },
      platformFee: { type: Number, default: 0 },
    },
    payment: {
      transactionId: String,
      status: String,
      reference: String,
      authorizationUrl: String,
      accessCode: String,
      paidAt: Date,
      gateway_response: Schema.Types.Mixed,
      fees: Number,
    },
    type: String,
    startedAt: Date,
    endedAt: Date,
    cancellation: {
      refundAmount: Number,
      reason: String,
      cancelledBy: Schema.Types.Mixed,
      cancelledAt: Date,
      refundEligible: Boolean,
    },
    reschedule: Schema.Types.Mixed,
    approval: Schema.Types.Mixed,
    qualityMetrics: {
      samples: [Schema.Types.Mixed],
      averageQuality: Number,
      issues: [Schema.Types.Mixed],
    },
    events: [Schema.Types.Mixed],
    videoSession: {
      roomId: String,
      roomName: String,
      roomUrl: String,
      provider: String,
      recording: {
        isRecording: { type: Boolean, default: false },
        stoppedAt: Date,
        stoppedBy: Schema.Types.Mixed,
        startedAt: Date,
      },
      analytics: Schema.Types.Mixed,
      deletedAt: Date,
      completedAt: Date,
    },
    notes: Schema.Types.Mixed,
    outcome: Schema.Types.Mixed,
    noShow: Schema.Types.Mixed,
    feedback: Schema.Types.Mixed,
    reminderSent: { type: Boolean, default: false },
    refund: {
      status: String,
      processedAt: Date,
      amount: Number,
      transactionId: String,
    },
    dailyRoomUrl: String,
    sessionNotes: String,
    rating: { type: Number, min: 1, max: 5 },
    issueReported: {
      reportedBy: { type: String, enum: ["user", "counselor"] },
      issueType: String,
      description: String,
      reportedAt: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Ensure virtual fields are serialized
sessionSchema.set("toJSON", { virtuals: true });
sessionSchema.set("toObject", { virtuals: true });

export default mongoose.model<ISession>("Session", sessionSchema);

// Also export as named export for convenience
export const Session = mongoose.model<ISession>("Session", sessionSchema);

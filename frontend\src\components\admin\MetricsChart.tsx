"use client";

import { useState } from "react";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  <PERSON>hart,
  Pie,
  Cell,
} from "recharts";

interface MetricsChartProps {
  data: any[];
  type: "revenue" | "users" | "sessions" | "engagement";
  period: "week" | "month" | "quarter" | "year";
}

const COLORS = ["#8B5CF6", "#06B6D4", "#10B981", "#F59E0B"];

export default function MetricsChart({
  data,
  type,
  period,
}: MetricsChartProps) {
  const [chartType, setChartType] = useState<"line" | "bar" | "pie">("line");

  const formatValue = (value: number) => {
    if (type === "revenue") {
      return `$${value.toLocaleString()}`;
    } else if (type === "users" || type === "sessions") {
      return value.toLocaleString();
    } else {
      return `${value}%`;
    }
  };

  const getValueKey = () => {
    switch (type) {
      case "revenue":
        return "revenue";
      case "users":
        return "users";
      case "sessions":
        return "sessions";
      case "engagement":
        return "engagement";
      default:
        return "value";
    }
  };

  const renderChart = (): JSX.Element => {
    switch (chartType) {
      case "line":
        return (
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="period" />
            <YAxis tickFormatter={formatValue} />
            <Tooltip
              formatter={(value: number) => [formatValue(value), type]}
              labelStyle={{ color: "#374151" }}
            />
            <Line
              type="monotone"
              dataKey={getValueKey()}
              stroke="#8B5CF6"
              strokeWidth={2}
              dot={{ fill: "#8B5CF6", strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        );

      case "bar":
        return (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="period" />
            <YAxis tickFormatter={formatValue} />
            <Tooltip
              formatter={(value: number) => [formatValue(value), type]}
              labelStyle={{ color: "#374151" }}
            />
            <Bar dataKey={getValueKey()} fill="#8B5CF6" />
          </BarChart>
        );

      case "pie":
        return (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }: { name?: string; percent?: number }) =>
                `${name || ""} ${percent ? (percent * 100).toFixed(0) : 0}%`
              }
              outerRadius={80}
              fill="#8884d8"
              dataKey={getValueKey()}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [formatValue(value), type]}
            />
          </PieChart>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">No chart available</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900 capitalize">
          {type} Analytics
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setChartType("line")}
            className={`px-3 py-1 text-sm rounded-md ${
              chartType === "line"
                ? "bg-purple-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            Line
          </button>
          <button
            onClick={() => setChartType("bar")}
            className={`px-3 py-1 text-sm rounded-md ${
              chartType === "bar"
                ? "bg-purple-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            Bar
          </button>
          <button
            onClick={() => setChartType("pie")}
            className={`px-3 py-1 text-sm rounded-md ${
              chartType === "pie"
                ? "bg-purple-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            Pie
          </button>
        </div>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </div>
    </div>
  );
}

import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { jest } from "@jest/globals";
import LoginForm from "@/components/auth/LoginForm";
import { useAuthStore } from "@/store/authStore";

// Mock the auth store
jest.mock("@/store/authStore");
const mockUseAuthStore = useAuthStore as jest.MockedFunction<
  typeof useAuthStore
>;

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
}));

describe("LoginForm", () => {
  const mockLogin = jest.fn();
  const mockGuestLogin = jest.fn();

  beforeEach(() => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      guestLogin: mockGuestLogin,
      isLoading: false,
      error: null,
      isAuthenticated: false,
      isGuest: false,
      user: null,
      tokens: null,
      guestToken: null,
      logout: jest.fn(),
      clearError: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders login form correctly", () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /sign in/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /continue as guest/i })
    ).toBeInTheDocument();
  });

  it("validates required fields", async () => {
    render(<LoginForm />);

    const submitButton = screen.getByRole("button", { name: /sign in/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it("validates email format", async () => {
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole("button", { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: "invalid-email" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(/please enter a valid email/i)
      ).toBeInTheDocument();
    });
  });

  it("submits form with valid data", async () => {
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole("button", { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "password123" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "password123",
      });
    });
  });

  it("handles guest login", async () => {
    render(<LoginForm />);

    const guestButton = screen.getByRole("button", {
      name: /continue as guest/i,
    });
    fireEvent.click(guestButton);

    await waitFor(() => {
      expect(mockGuestLogin).toHaveBeenCalled();
    });
  });

  it("displays loading state", () => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      guestLogin: mockGuestLogin,
      isLoading: true,
      error: null,
      isAuthenticated: false,
      isGuest: false,
      user: null,
      tokens: null,
      guestToken: null,
      logout: jest.fn(),
      clearError: jest.fn(),
    });

    render(<LoginForm />);

    expect(screen.getByText(/signing in/i)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /signing in/i })).toBeDisabled();
  });

  it("displays error message", () => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      guestLogin: mockGuestLogin,
      isLoading: false,
      error: "Invalid credentials",
      isAuthenticated: false,
      isGuest: false,
      user: null,
      tokens: null,
      guestToken: null,
      logout: jest.fn(),
      clearError: jest.fn(),
    });

    render(<LoginForm />);

    expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
  });

  it("toggles password visibility", () => {
    render(<LoginForm />);

    const passwordInput = screen.getByLabelText(
      /password/i
    ) as HTMLInputElement;
    const toggleButton = screen.getByRole("button", {
      name: /toggle password visibility/i,
    });

    expect(passwordInput.type).toBe("password");

    fireEvent.click(toggleButton);
    expect(passwordInput.type).toBe("text");

    fireEvent.click(toggleButton);
    expect(passwordInput.type).toBe("password");
  });

  it("remembers user preference", () => {
    render(<LoginForm />);

    const rememberCheckbox = screen.getByLabelText(
      /remember me/i
    ) as HTMLInputElement;

    expect(rememberCheckbox.checked).toBe(false);

    fireEvent.click(rememberCheckbox);
    expect(rememberCheckbox.checked).toBe(true);
  });

  it("has proper accessibility attributes", () => {
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(emailInput).toHaveAttribute("type", "email");
    expect(emailInput).toHaveAttribute("required");
    expect(passwordInput).toHaveAttribute("required");

    // Check for proper ARIA attributes
    expect(emailInput).toHaveAttribute("aria-describedby");
    expect(passwordInput).toHaveAttribute("aria-describedby");
  });
});

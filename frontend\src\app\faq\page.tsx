import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import { Search, HelpCircle, Phone, Mail, MessageCircle } from "lucide-react";

const faqCategories = [
  {
    title: "Getting Started",
    faqs: [
      {
        question: "How do I create an account?",
        answer:
          "Click 'Sign Up' at the top of any page, provide your email and create a password. You'll receive a verification email to complete the process.",
      },
      {
        question: "Is Theramea free to use?",
        answer:
          "Creating an account and browsing counselors is free. Session fees vary by counselor and session type. Many insurance plans are accepted.",
      },
      {
        question: "How do I find the right counselor?",
        answer:
          "Use our matching system by answering a few questions about your needs, or browse counselors by specialty, availability, and reviews.",
      },
      {
        question: "What types of therapy are available?",
        answer:
          "We offer individual therapy, couples counseling, family therapy, group sessions, and specialized treatments for various mental health conditions.",
      },
    ],
  },
  {
    title: "Sessions & Scheduling",
    faqs: [
      {
        question: "How do I schedule a session?",
        answer:
          "Go to your chosen counselor's profile, view their availability, and select a time that works for you. You'll receive confirmation and reminder notifications.",
      },
      {
        question: "Can I cancel or reschedule a session?",
        answer:
          "Yes, you can cancel or reschedule up to 24 hours before your session time without penalty. Less than 24 hours may incur a fee.",
      },
      {
        question: "What if I'm late to my session?",
        answer:
          "Sessions will wait for up to 15 minutes. If you're more than 15 minutes late, the session may be cancelled and you may be charged a fee.",
      },
      {
        question: "How long are therapy sessions?",
        answer:
          "Standard sessions are 50 minutes. Some counselors offer 25-minute check-ins or 80-minute extended sessions.",
      },
    ],
  },
  {
    title: "Technology & Platform",
    faqs: [
      {
        question: "What devices can I use for sessions?",
        answer:
          "Sessions work on computers, tablets, and smartphones with a camera and microphone. We recommend using Chrome, Firefox, or Safari browsers.",
      },
      {
        question: "What if I have technical issues during a session?",
        answer:
          "Our technical support is available 24/7. You can also reach your counselor via secure messaging if video issues occur.",
      },
      {
        question: "Is my information secure?",
        answer:
          "Yes, Theramea is HIPAA compliant with end-to-end encryption for all communications. Your privacy and security are our top priorities.",
      },
      {
        question: "Can I access my session recordings?",
        answer:
          "Sessions are not recorded for privacy reasons. You can take notes during sessions or request session summaries from your counselor.",
      },
    ],
  },
  {
    title: "Billing & Insurance",
    faqs: [
      {
        question: "What payment methods do you accept?",
        answer:
          "We accept all major credit cards, debit cards, HSA/FSA cards, and many insurance plans. Payment is processed securely.",
      },
      {
        question: "Do you accept insurance?",
        answer:
          "Yes, we accept most major insurance plans. Check with your counselor during booking to confirm your specific plan is accepted.",
      },
      {
        question: "How much do sessions cost?",
        answer:
          "Session costs vary by counselor and typically range from $80-200 per session. Many are covered by insurance with co-pays as low as $20.",
      },
      {
        question: "Can I get a refund?",
        answer:
          "Refunds are available for cancelled sessions (24+ hours notice) and in certain circumstances. See our refund policy for details.",
      },
    ],
  },
  {
    title: "Privacy & Safety",
    faqs: [
      {
        question: "Who can see my therapy information?",
        answer:
          "Only you and your assigned counselor can access your session information. We never share data with third parties without your consent.",
      },
      {
        question: "What if I'm in crisis?",
        answer:
          "Call our 24/7 crisis line at 1-800-CRISIS-T, the National Suicide Prevention Lifeline at 988, or emergency services at 911.",
      },
      {
        question: "Are counselors licensed?",
        answer:
          "Yes, all Theramea counselors are licensed mental health professionals who have passed background checks and credential verification.",
      },
      {
        question: "Can I switch counselors?",
        answer:
          "Absolutely. You can switch counselors at any time. We'll help you find a better match if your current counselor isn't the right fit.",
      },
    ],
  },
];

export default function FAQPage() {
  return (
<div className="min-h-screen bg-gray-50">
        <Header />
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <HelpCircle className="h-16 w-16 text-purple-600 mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Find quick answers to common questions about Theramea
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search FAQs..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-lg text-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent"
            />
          </div>
        </div>
      </section>

      {/* FAQ Categories */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex} className="mb-12">
                <h2 className="text-2xl font-bold mb-6 text-gray-800 border-b border-gray-200 pb-2">
                  {category.title}
                </h2>

                <div className="space-y-4">
                  {category.faqs.map((faq, faqIndex) => (
                    <details
                      key={faqIndex}
                      className="bg-white rounded-lg shadow-sm border border-gray-200"
                    >
                      <summary className="p-6 cursor-pointer hover:bg-gray-50 font-semibold text-gray-800 flex items-center justify-between">
                        <span>{faq.question}</span>
                        <HelpCircle className="h-5 w-5 text-purple-600 flex-shrink-0 ml-4" />
                      </summary>
                      <div className="px-6 pb-6 pt-2">
                        <p className="text-gray-600 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </details>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Need Help Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8 text-gray-800">
            Still Have Questions?
          </h2>
          <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
            Our support team is here to help with any questions not covered in
            our FAQs
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <Phone className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Call Us</h3>
              <p className="text-gray-600 mb-4">Mon-Fri, 8AM-8PM EST</p>
              <p className="text-lg font-semibold text-purple-600">
                1-800-THERAPY
              </p>
            </div>

            <div className="text-center">
              <Mail className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Email Us</h3>
              <p className="text-gray-600 mb-4">Response within 24 hours</p>
              <a
                href="mailto:<EMAIL>"
                className="text-lg font-semibold text-blue-600 hover:underline"
              >
                <EMAIL>
              </a>
            </div>

            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Live Chat</h3>
              <p className="text-gray-600 mb-4">Available 24/7</p>
              <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                Start Chat
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl font-bold mb-8 text-gray-800">
              Helpful Resources
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <a
                href="/help"
                className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200"
              >
                <h3 className="font-semibold text-purple-600 mb-2">
                  Help Center
                </h3>
                <p className="text-sm text-gray-600">
                  Comprehensive guides and tutorials
                </p>
              </a>

              <a
                href="/how-it-works"
                className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200"
              >
                <h3 className="font-semibold text-blue-600 mb-2">
                  How It Works
                </h3>
                <p className="text-sm text-gray-600">
                  Learn about our platform
                </p>
              </a>

              <a
                href="/privacy"
                className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200"
              >
                <h3 className="font-semibold text-green-600 mb-2">
                  Privacy Policy
                </h3>
                <p className="text-sm text-gray-600">
                  How we protect your information
                </p>
              </a>

              <a
                href="/terms"
                className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200"
              >
                <h3 className="font-semibold text-orange-600 mb-2">
                  Terms of Service
                </h3>
                <p className="text-sm text-gray-600">
                  Platform terms and conditions
                </p>
              </a>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
}

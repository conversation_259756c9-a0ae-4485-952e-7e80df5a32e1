const mongoose = require("mongoose");

const MONGODB_URI = "****************************************************************************";

async function fixResourceData() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // First, let's see what we have
    const Resource = mongoose.model("Resource", new mongoose.Schema({}, { strict: false }));
    const resources = await Resource.find({}, "_id title category seo.slug seoData.slug").lean();
    
    console.log("📋 Current resources:");
    resources.forEach((doc, i) => {
      console.log(`${i+1}. ID: ${doc._id} | Title: ${doc.title} | Category: ${doc.category} | Slug: ${doc.seo?.slug || doc.seoData?.slug}`);
    });

    // Remove the resource with slug "5" (duplicate)
    console.log("\n🗑️ Removing resource with slug '5'...");
    const deleteResult = await Resource.deleteOne({ 
      $or: [
        { "seo.slug": "5" },
        { "seoData.slug": "5" }
      ]
    });
    console.log(`Deleted ${deleteResult.deletedCount} resource(s)`);

    // Fix category names to match enum values
    console.log("\n🔧 Fixing category values...");
    
    const categoryMappings = {
      "Anxiety": "anxiety-management",
      "Depression": "depression-support", 
      "Mindfulness": "mindfulness-meditation",
      "Relationships": "relationship-skills",
      "Self-Care": "self-care"
    };

    for (const [oldCategory, newCategory] of Object.entries(categoryMappings)) {
      const updateResult = await Resource.updateMany(
        { category: oldCategory },
        { $set: { category: newCategory } }
      );
      if (updateResult.modifiedCount > 0) {
        console.log(`Updated ${updateResult.modifiedCount} resource(s) from "${oldCategory}" to "${newCategory}"`);
      }
    }

    // Create a dummy admin user for createdBy field
    const User = mongoose.model("User", new mongoose.Schema({
      firstName: String,
      lastName: String,
      email: String,
      role: { type: String, enum: ['user', 'counselor', 'admin'], default: 'user' }
    }));

    let adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      adminUser = await User.create({
        firstName: "System",
        lastName: "Admin", 
        email: "<EMAIL>",
        role: "admin"
      });
      console.log(`✅ Created admin user: ${adminUser._id}`);
    }

    // Add createdBy field to all resources that don't have it
    const updateCreatedByResult = await Resource.updateMany(
      { createdBy: { $exists: false } },
      { $set: { createdBy: adminUser._id } }
    );
    console.log(`✅ Added createdBy field to ${updateCreatedByResult.modifiedCount} resource(s)`);

    // Show final state
    console.log("\n📋 Final resources:");
    const finalResources = await Resource.find({}, "_id title category createdBy").lean();
    finalResources.forEach((doc, i) => {
      console.log(`${i+1}. ID: ${doc._id} | Title: ${doc.title} | Category: ${doc.category} | CreatedBy: ${doc.createdBy ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

fixResourceData();

// Admin panel types and interfaces

export interface AdminStats {
  users: {
    total: number;
    active: number;
    inactive: number;
    newThisMonth: number;
    growth: number;
  };
  counselors: {
    total: number;
    approved: number;
    pending: number;
    rejected: number;
    averageRating: number;
  };
  sessions: {
    total: number;
    completed: number;
    cancelled: number;
    revenue: number;
    thisMonth: number;
  };
  platform: {
    totalRevenue: number;
    monthlyRevenue: number;
    activeUsers: number;
    conversionRate: number;
  };
}

export interface AdminUser {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: "user" | "counselor" | "admin" | "superadmin";
  isActive: boolean;
  isEmailVerified: boolean;
  profilePicture?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminCounselor {
  _id: string;
  userId: string;
  bio: string;
  specializations: string[];
  verification: {
    status: "pending" | "approved" | "rejected" | "suspended";
    submittedAt: string;
    reviewedAt?: string;
    reviewedBy?: string;
    rejectionReason?: string;
  };
  statistics: {
    totalSessions: number;
    totalEarnings: number;
    averageRating: number;
    totalReviews: number;
    completionRate: number;
  };
  user?: AdminUser;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AdminSession {
  _id: string;
  userId: string;
  counselorId: string;
  scheduledAt: string;
  duration: number;
  status:
    | "scheduled"
    | "in-progress"
    | "completed"
    | "cancelled"
    | "no-show"
    | "rescheduled";
  type: "individual" | "group" | "couples" | "family";
  pricing: {
    currency: "NGN" | "USD";
    totalAmount: number;
    platformFee: number;
    counselorEarnings: number;
  };
  payment: {
    status: "pending" | "completed" | "failed" | "refunded";
    paidAt?: string;
  };
  user?: AdminUser;
  counselor?: AdminCounselor;
  createdAt: string;
  updatedAt: string;
}

export interface AdminReport {
  _id: string;
  reporterId: string;
  reportedUserId?: string;
  reportedMessageId?: string;
  reportedResourceId?: string;
  type: "user" | "message" | "resource" | "session" | "other";
  category: string;
  description: string;
  status: "pending" | "investigating" | "resolved" | "dismissed" | "escalated";
  priority: "low" | "medium" | "high" | "urgent";
  assignedTo?: string;
  resolution?: {
    action: string;
    description: string;
    resolvedBy: string;
    resolvedAt: string;
  };
  reporter?: AdminUser;
  reportedUser?: AdminUser;
  createdAt: string;
  updatedAt: string;
}

export interface AdminAction {
  _id: string;
  adminId: string;
  action: string;
  targetType:
    | "user"
    | "counselor"
    | "session"
    | "chatroom"
    | "message"
    | "resource"
    | "system";
  targetId?: string;
  details: {
    description: string;
    previousState?: any;
    newState?: any;
    reason?: string;
  };
  severity: "low" | "medium" | "high" | "critical";
  admin?: AdminUser;
  createdAt: string;
}

export interface AdminFilters {
  status?: string;
  role?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface AdminListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PlatformMetrics {
  revenue: {
    total: number;
    growth: number;
    averageOrderValue: number;
    mrr: number;
    refundRate: number;
    platformFees: number;
  };
  users: {
    total: number;
    active: number;
    new: number;
    growth: number;
    retention: number;
  };
  sessions: {
    total: number;
    growth: number;
  };
  conversion: {
    rate: number;
    growth: number;
  };
  engagement: {
    avgSessionDuration: number;
  };
  charts: {
    revenue: any[];
    users: any[];
    sessions: any[];
    engagement: any[];
  };
  topPerformers: {
    counselors: {
      id: string;
      name: string;
      earnings: number;
      rating: number;
      sessions: number;
    }[];
  };
  popularTopics: {
    name: string;
    count: number;
    growth: number;
  }[];
  userGrowth: {
    period: string;
    users: number;
    counselors: number;
  }[];
  sessionMetrics: {
    period: string;
    sessions: number;
    revenue: number;
    completionRate: number;
  }[];
  topCounselors: {
    counselor: AdminCounselor;
    sessions: number;
    earnings: number;
    rating: number;
  }[];
  recentActivity: {
    type: string;
    description: string;
    timestamp: string;
    user?: AdminUser;
  }[];
}

// Admin action types
export const ADMIN_ACTIONS = {
  USER_MANAGEMENT: {
    ACTIVATE_USER: "activate_user",
    DEACTIVATE_USER: "deactivate_user",
    DELETE_USER: "delete_user",
    UPDATE_ROLE: "update_role",
    RESET_PASSWORD: "reset_password",
  },
  COUNSELOR_MANAGEMENT: {
    APPROVE_COUNSELOR: "approve_counselor",
    REJECT_COUNSELOR: "reject_counselor",
    SUSPEND_COUNSELOR: "suspend_counselor",
    REACTIVATE_COUNSELOR: "reactivate_counselor",
  },
  SESSION_MANAGEMENT: {
    CANCEL_SESSION: "cancel_session",
    REFUND_SESSION: "refund_session",
    RESOLVE_DISPUTE: "resolve_dispute",
  },
  CONTENT_MANAGEMENT: {
    APPROVE_CONTENT: "approve_content",
    REJECT_CONTENT: "reject_content",
    FEATURE_CONTENT: "feature_content",
    HIDE_CONTENT: "hide_content",
  },
  SYSTEM: {
    UPDATE_SETTINGS: "update_settings",
    BACKUP_DATA: "backup_data",
    MAINTENANCE_MODE: "maintenance_mode",
  },
} as const;

// Report categories
export const REPORT_CATEGORIES = [
  { value: "inappropriate-content", label: "Inappropriate Content" },
  { value: "harassment", label: "Harassment" },
  { value: "spam", label: "Spam" },
  { value: "fake-profile", label: "Fake Profile" },
  { value: "professional-misconduct", label: "Professional Misconduct" },
  { value: "payment-issue", label: "Payment Issue" },
  { value: "technical-issue", label: "Technical Issue" },
  { value: "other", label: "Other" },
] as const;

// User roles with permissions
export const USER_ROLES = [
  { value: "user", label: "User", color: "gray" },
  { value: "counselor", label: "Counselor", color: "blue" },
  { value: "admin", label: "Admin", color: "purple" },
  { value: "superadmin", label: "Super Admin", color: "red" },
] as const;

// Status options
export const STATUS_OPTIONS = {
  USER: [
    { value: "active", label: "Active", color: "green" },
    { value: "inactive", label: "Inactive", color: "gray" },
    { value: "suspended", label: "Suspended", color: "red" },
  ],
  COUNSELOR: [
    { value: "pending", label: "Pending Review", color: "yellow" },
    { value: "approved", label: "Approved", color: "green" },
    { value: "rejected", label: "Rejected", color: "red" },
    { value: "suspended", label: "Suspended", color: "orange" },
  ],
  SESSION: [
    { value: "scheduled", label: "Scheduled", color: "blue" },
    { value: "in-progress", label: "In Progress", color: "green" },
    { value: "completed", label: "Completed", color: "gray" },
    { value: "cancelled", label: "Cancelled", color: "red" },
    { value: "no-show", label: "No Show", color: "orange" },
  ],
  REPORT: [
    { value: "pending", label: "Pending", color: "yellow" },
    { value: "investigating", label: "Investigating", color: "blue" },
    { value: "resolved", label: "Resolved", color: "green" },
    { value: "dismissed", label: "Dismissed", color: "gray" },
    { value: "escalated", label: "Escalated", color: "red" },
  ],
} as const;

export type AdminActionType =
  (typeof ADMIN_ACTIONS)[keyof typeof ADMIN_ACTIONS][keyof (typeof ADMIN_ACTIONS)[keyof typeof ADMIN_ACTIONS]];
export type ReportCategory = (typeof REPORT_CATEGORIES)[number]["value"];
export type UserRole = (typeof USER_ROLES)[number]["value"];
export type UserStatus = (typeof STATUS_OPTIONS.USER)[number]["value"];
export type CounselorStatus =
  (typeof STATUS_OPTIONS.COUNSELOR)[number]["value"];
export type SessionStatus = (typeof STATUS_OPTIONS.SESSION)[number]["value"];
export type ReportStatus = (typeof STATUS_OPTIONS.REPORT)[number]["value"];

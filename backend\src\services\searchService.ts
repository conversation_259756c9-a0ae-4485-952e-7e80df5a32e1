import { Resource } from "@/models/Resource";
import { logger } from "@/utils/logger";

export interface SearchQuery {
  query: string;
  filters?: {
    type?: string[];
    category?: string;
    difficulty?: string[];
    tags?: string[];
    mentalHealthTopics?: string[];
    isPremium?: boolean;
    minRating?: number;
  };
  options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  };
}

export interface SearchResult {
  content: any[];
  suggestions: string[];
  facets: {
    types: { name: string; count: number }[];
    categories: { name: string; count: number }[];
    difficulties: { name: string; count: number }[];
    tags: { name: string; count: number }[];
    topics: { name: string; count: number }[];
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  searchTime: number;
}

export interface AutocompleteResult {
  suggestions: string[];
  categories: string[];
  tags: string[];
  topics: string[];
}

export class SearchService {
  /**
   * Perform advanced search with faceted results
   */
  static async search(searchQuery: SearchQuery): Promise<SearchResult> {
    const startTime = Date.now();

    try {
      const { query, filters = {}, options = {} } = searchQuery;

      const {
        page = 1,
        limit = 20,
        sortBy = "relevance",
        sortOrder = "desc",
      } = options;

      // Build search pipeline
      const pipeline: any[] = [];

      // Match stage - base filters
      const matchStage: any = {
        isPublished: true,
      };

      // Apply filters
      if (filters.type?.length) matchStage.type = { $in: filters.type };
      if (filters.category) matchStage.category = filters.category;
      if (filters.difficulty?.length)
        matchStage.difficulty = { $in: filters.difficulty };
      if (filters.tags?.length) matchStage.tags = { $in: filters.tags };
      if (filters.mentalHealthTopics?.length)
        matchStage.mentalHealthTopics = { $in: filters.mentalHealthTopics };
      if (filters.isPremium !== undefined)
        matchStage.isPremium = filters.isPremium;
      if (filters.minRating)
        matchStage["statistics.averageRating"] = { $gte: filters.minRating };

      // Text search
      if (query && query.trim()) {
        const searchTerms = this.processSearchQuery(query);
        matchStage.$or = [
          { title: { $regex: searchTerms.regex, $options: "i" } },
          { description: { $regex: searchTerms.regex, $options: "i" } },
          { content: { $regex: searchTerms.regex, $options: "i" } },
          { tags: { $in: searchTerms.terms } },
          { mentalHealthTopics: { $in: searchTerms.terms } },
          { "seoData.keywords": { $in: searchTerms.terms } },
        ];
      }

      pipeline.push({ $match: matchStage });

      // Add relevance scoring for text search
      if (query && query.trim()) {
        pipeline.push({
          $addFields: {
            relevanceScore: {
              $add: [
                // Title match gets highest score
                {
                  $cond: [
                    {
                      $regexMatch: {
                        input: "$title",
                        regex: query,
                        options: "i",
                      },
                    },
                    100,
                    0,
                  ],
                },
                // Description match gets medium score
                {
                  $cond: [
                    {
                      $regexMatch: {
                        input: "$description",
                        regex: query,
                        options: "i",
                      },
                    },
                    50,
                    0,
                  ],
                },
                // Tag/topic match gets lower score
                {
                  $cond: [
                    {
                      $or: [
                        { $in: [query, "$tags"] },
                        { $in: [query, "$mentalHealthTopics"] },
                      ],
                    },
                    25,
                    0,
                  ],
                },
                // Content match gets lowest score
                {
                  $cond: [
                    {
                      $regexMatch: {
                        input: "$content",
                        regex: query,
                        options: "i",
                      },
                    },
                    10,
                    0,
                  ],
                },
                // Boost by rating and popularity
                { $multiply: ["$statistics.averageRating", 5] },
                {
                  $multiply: [
                    { $log10: { $add: ["$statistics.views", 1] } },
                    2,
                  ],
                },
              ],
            },
          },
        });
      }

      // Sorting
      const sortStage: any = {};
      if (sortBy === "relevance" && query) {
        sortStage.relevanceScore = -1;
        sortStage["statistics.averageRating"] = -1;
      } else if (sortBy === "rating") {
        sortStage["statistics.averageRating"] = sortOrder === "asc" ? 1 : -1;
      } else if (sortBy === "views") {
        sortStage["statistics.views"] = sortOrder === "asc" ? 1 : -1;
      } else if (sortBy === "date") {
        sortStage.publishedAt = sortOrder === "asc" ? 1 : -1;
      } else {
        sortStage.publishedAt = -1;
      }

      pipeline.push({ $sort: sortStage });

      // Facet stage for getting both results and facets
      pipeline.push({
        $facet: {
          results: [
            { $skip: (page - 1) * limit },
            { $limit: limit },
            {
              $lookup: {
                from: "users",
                localField: "author",
                foreignField: "_id",
                as: "author",
                pipeline: [
                  {
                    $project: { firstName: 1, lastName: 1, profilePicture: 1 },
                  },
                ],
              },
            },
            { $unwind: "$author" },
          ],
          totalCount: [{ $count: "count" }],
          facets: [
            {
              $group: {
                _id: null,
                types: { $push: "$type" },
                categories: { $push: "$category" },
                difficulties: { $push: "$difficulty" },
                allTags: { $push: "$tags" },
                allTopics: { $push: "$mentalHealthTopics" },
              },
            },
          ],
        },
      });

      const [searchResults] = await Resource.aggregate(pipeline);

      // Process facets
      const facets = this.processFacets(searchResults.facets[0] || {});

      // Generate suggestions
      const suggestions = await this.generateSuggestions(query);

      const total = searchResults.totalCount[0]?.count || 0;
      const searchTime = Date.now() - startTime;

      return {
        content: searchResults.results,
        suggestions,
        facets,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
        searchTime,
      };
    } catch (error) {
      logger.error("Search error:", error);
      throw error;
    }
  }

  /**
   * Get autocomplete suggestions
   */
  static async getAutocomplete(
    query: string,
    limit: number = 10
  ): Promise<AutocompleteResult> {
    try {
      if (!query || query.length < 2) {
        return {
          suggestions: [],
          categories: [],
          tags: [],
          topics: [],
        };
      }

      const regex = new RegExp(query, "i");

      // Get title suggestions
      const titleSuggestions = await Resource.find({
        isPublished: true,
        title: { $regex: regex },
      })
        .select("title")
        .limit(limit)
        .lean();

      // Get category suggestions
      const categories = await Resource.distinct("category", {
        isPublished: true,
        category: { $regex: regex },
      });

      // Get tag suggestions
      const tags = await Resource.aggregate([
        { $match: { isPublished: true } },
        { $unwind: "$tags" },
        { $match: { tags: { $regex: regex } } },
        { $group: { _id: "$tags", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: limit },
      ]);

      // Get topic suggestions
      const topics = await Resource.aggregate([
        { $match: { isPublished: true } },
        { $unwind: "$mentalHealthTopics" },
        { $match: { mentalHealthTopics: { $regex: regex } } },
        { $group: { _id: "$mentalHealthTopics", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: limit },
      ]);

      return {
        suggestions: titleSuggestions.map((item) => item.title).slice(0, limit),
        categories: categories.slice(0, limit),
        tags: tags.map((item) => item._id).slice(0, limit),
        topics: topics.map((item) => item._id).slice(0, limit),
      };
    } catch (error) {
      logger.error("Autocomplete error:", error);
      return {
        suggestions: [],
        categories: [],
        tags: [],
        topics: [],
      };
    }
  }

  /**
   * Get similar content
   */
  static async getSimilarContent(
    contentId: string,
    limit: number = 5
  ): Promise<any[]> {
    try {
      const content = await Resource.findById(contentId);

      if (!content) {
        return [];
      }

      // Find similar content based on category, tags, and topics
      const similar = await Resource.find({
        _id: { $ne: contentId },
        isPublished: true,
        $or: [
          { category: content.category },
          { tags: { $in: content.tags } },
          { mentalHealthTopics: { $in: content.mentalHealthTopics } },
        ],
      })
        .populate("author", "firstName lastName profilePicture")
        .sort({ "statistics.averageRating": -1, "statistics.views": -1 })
        .limit(limit);

      return similar;
    } catch (error) {
      logger.error("Get similar content error:", error);
      return [];
    }
  }

  /**
   * Track search query for analytics
   */
  static async trackSearch(
    query: string,
    userId?: string,
    resultsCount: number = 0
  ): Promise<void> {
    try {
      // TODO: Implement search analytics tracking
      // This could store search queries, user patterns, and popular searches
      logger.debug(
        `Search tracked: "${query}" - ${resultsCount} results - User: ${
          userId || "anonymous"
        }`
      );
    } catch (error) {
      logger.error("Track search error:", error);
    }
  }

  /**
   * Get popular searches
   */
  static async getPopularSearches(limit: number = 10): Promise<string[]> {
    try {
      // TODO: Implement based on search analytics
      // For now, return some common mental health search terms
      return [
        "anxiety",
        "depression",
        "stress management",
        "mindfulness",
        "self-care",
        "therapy techniques",
        "coping strategies",
        "mental wellness",
        "emotional regulation",
        "relationship advice",
      ].slice(0, limit);
    } catch (error) {
      logger.error("Get popular searches error:", error);
      return [];
    }
  }

  /**
   * Process search query to extract terms and create regex
   */
  private static processSearchQuery(query: string): {
    terms: string[];
    regex: string;
  } {
    const cleanQuery = query.trim().toLowerCase();
    const terms = cleanQuery.split(/\s+/).filter((term) => term.length > 2);

    // Create regex that matches any of the terms
    const regexPattern = terms
      .map((term) => `(?=.*${term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`)
      .join("");

    return {
      terms,
      regex: regexPattern || cleanQuery,
    };
  }

  /**
   * Process facets from aggregation results
   */
  private static processFacets(facetData: any): any {
    const facets: {
      types: { name: string; count: number }[];
      categories: { name: string; count: number }[];
      difficulties: { name: string; count: number }[];
      tags: { name: string; count: number }[];
      topics: { name: string; count: number }[];
    } = {
      types: [],
      categories: [],
      difficulties: [],
      tags: [],
      topics: [],
    };

    if (!facetData) return facets;

    // Process types
    if (facetData.types) {
      const typeCounts = this.countItems(facetData.types);
      facets.types = Object.entries(typeCounts).map(([name, count]) => ({
        name,
        count,
      }));
    }

    // Process categories
    if (facetData.categories) {
      const categoryCounts = this.countItems(facetData.categories);
      facets.categories = Object.entries(categoryCounts).map(
        ([name, count]) => ({ name, count })
      );
    }

    // Process difficulties
    if (facetData.difficulties) {
      const difficultyCounts = this.countItems(facetData.difficulties);
      facets.difficulties = Object.entries(difficultyCounts).map(
        ([name, count]) => ({ name, count })
      );
    }

    // Process tags
    if (facetData.allTags) {
      const flatTags = facetData.allTags.flat();
      const tagCounts = this.countItems(flatTags);
      facets.tags = Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 20);
    }

    // Process topics
    if (facetData.allTopics) {
      const flatTopics = facetData.allTopics.flat();
      const topicCounts = this.countItems(flatTopics);
      facets.topics = Object.entries(topicCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 20);
    }

    return facets;
  }

  /**
   * Count items in array
   */
  private static countItems(items: any[]): Record<string, number> {
    return items.reduce((counts, item) => {
      if (item) {
        counts[item] = (counts[item] || 0) + 1;
      }
      return counts;
    }, {});
  }

  /**
   * Generate search suggestions based on query
   */
  private static async generateSuggestions(query: string): Promise<string[]> {
    try {
      if (!query || query.length < 3) {
        return [];
      }

      // Get suggestions from similar titles and tags
      const suggestions = await Resource.aggregate([
        {
          $match: {
            isPublished: true,
            $or: [
              { title: { $regex: query, $options: "i" } },
              { tags: { $regex: query, $options: "i" } },
              { mentalHealthTopics: { $regex: query, $options: "i" } },
            ],
          },
        },
        {
          $project: {
            suggestions: {
              $concatArrays: [["$title"], "$tags", "$mentalHealthTopics"],
            },
          },
        },
        { $unwind: "$suggestions" },
        {
          $match: {
            suggestions: { $regex: query, $options: "i" },
          },
        },
        {
          $group: {
            _id: "$suggestions",
            count: { $sum: 1 },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 5 },
      ]);

      return suggestions.map((item) => item._id);
    } catch (error) {
      logger.error("Generate suggestions error:", error);
      return [];
    }
  }
}

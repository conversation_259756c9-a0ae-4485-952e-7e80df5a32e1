"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";
import Link from "next/link";
import { bookingAPI } from "@/lib/booking";

interface Session {
  id: string;
  counselorName: string;
  counselorId: string;
  date: string;
  time: string;
  duration: number;
  type: "video" | "audio" | "chat";
  status: "upcoming" | "completed" | "cancelled";
  amount: number;
  notes?: string;
  meetingLink?: string;
}

export default function SessionsPage() {
  const router = useRouter();
  const { user, isAuthenticated, isGuest, checkAuth, isLoading, tokens } =
    useAuthStore();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<
    "all" | "upcoming" | "completed" | "cancelled"
  >("all");
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [cancellationReason, setCancellationReason] = useState("");
  const [cancelling, setCancelling] = useState(false);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (isAuthenticated || isGuest) {
      loadSessionsData();
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  const handleCancelSession = async () => {
    if (
      !selectedSession ||
      !tokens?.accessToken ||
      !cancellationReason.trim()
    ) {
      return;
    }

    try {
      setCancelling(true);
      await bookingAPI.cancelBooking(
        selectedSession.id,
        cancellationReason,
        tokens.accessToken
      );

      // Update the session status locally
      setSessions((prev) =>
        prev.map((session) =>
          session.id === selectedSession.id
            ? { ...session, status: "cancelled" as const }
            : session
        )
      );

      // Close modal and reset state
      setShowCancelModal(false);
      setSelectedSession(null);
      setCancellationReason("");

      // Show success message
      alert("Session cancelled successfully");
    } catch (error) {
      console.error("Error cancelling session:", error);
      alert(
        error instanceof Error ? error.message : "Failed to cancel session"
      );
    } finally {
      setCancelling(false);
    }
  };

  const openCancelModal = (session: Session) => {
    setSelectedSession(session);
    setShowCancelModal(true);
  };

  const closeCancelModal = () => {
    setShowCancelModal(false);
    setSelectedSession(null);
    setCancellationReason("");
  };

  const loadSessionsData = async () => {
    try {
      setLoading(true);

      // Mock data - replace with actual API calls
      const mockSessions: Session[] = isGuest
        ? []
        : [
            {
              id: "1",
              counselorName: "Dr. Sarah Johnson",
              counselorId: "counselor-1",
              date: "2024-01-15",
              time: "14:00",
              duration: 50,
              type: "video",
              status: "upcoming",
              amount: 120,
              meetingLink: "https://meet.theramea.com/session-1",
            },
            {
              id: "2",
              counselorName: "Michael Chen",
              counselorId: "counselor-2",
              date: "2024-01-18",
              time: "10:00",
              duration: 50,
              type: "video",
              status: "upcoming",
              amount: 120,
              meetingLink: "https://meet.theramea.com/session-2",
            },
            {
              id: "3",
              counselorName: "Dr. Emily Rodriguez",
              counselorId: "counselor-3",
              date: "2024-01-10",
              time: "15:00",
              duration: 50,
              type: "video",
              status: "completed",
              amount: 120,
              notes:
                "Great session, made good progress on anxiety management techniques",
            },
            {
              id: "4",
              counselorName: "Dr. Sarah Johnson",
              counselorId: "counselor-1",
              date: "2024-01-08",
              time: "14:00",
              duration: 50,
              type: "audio",
              status: "completed",
              amount: 100,
            },
          ];

      setSessions(mockSessions);
    } catch (error) {
      console.error("Error loading sessions data:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredSessions = sessions.filter((session) => {
    if (filter === "all") return true;
    return session.status === filter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "upcoming":
        return "text-blue-600 bg-blue-100";
      case "completed":
        return "text-green-600 bg-green-100";
      case "cancelled":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return "🎥";
      case "audio":
        return "🎧";
      case "chat":
        return "💬";
      default:
        return "📞";
    }
  };

  const formatDate = (date: string, time: string) => {
    const sessionDate = new Date(`${date}T${time}:00`);
    return sessionDate.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Sessions</h1>
              <p className="text-gray-600 mt-2">
                Manage your therapy sessions and appointments
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-purple-600 hover:text-purple-700 font-medium flex items-center"
              >
                ← Back to Dashboard
              </Link>
              {!isGuest && (
                <Link
                  href="/counselors"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Book New Session
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: "all", label: "All Sessions" },
              { key: "upcoming", label: "Upcoming" },
              { key: "completed", label: "Completed" },
              { key: "cancelled", label: "Cancelled" },
            ].map((filterOption) => (
              <button
                key={filterOption.key}
                onClick={() => setFilter(filterOption.key as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filter === filterOption.key
                    ? "bg-purple-600 text-white"
                    : "bg-white text-gray-600 hover:bg-purple-50 hover:text-purple-600 border border-gray-200"
                }`}
              >
                {filterOption.label}
              </button>
            ))}
          </div>
        </div>

        {/* Sessions List */}
        <div className="space-y-4">
          {filteredSessions.length > 0 ? (
            filteredSessions.map((session) => (
              <div
                key={session.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">{getTypeIcon(session.type)}</div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        Session with {session.counselorName}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{formatDate(session.date, session.time)}</span>
                        <span>• {session.duration} minutes</span>
                        <span>• ${session.amount}</span>
                      </div>
                      {session.notes && (
                        <p className="text-sm text-gray-600 mt-2">
                          {session.notes}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
                        session.status
                      )}`}
                    >
                      {session.status.charAt(0).toUpperCase() +
                        session.status.slice(1)}
                    </span>
                    {session.status === "upcoming" && session.meetingLink && (
                      <Link
                        href={session.meetingLink}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Join Session
                      </Link>
                    )}
                    {session.status === "upcoming" && (
                      <button
                        onClick={() => openCancelModal(session)}
                        className="text-red-600 hover:text-red-700 text-sm font-medium"
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">📅</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {isGuest
                  ? "Sign up to book sessions"
                  : filter === "all"
                  ? "No sessions found"
                  : `No ${filter} sessions`}
              </h3>
              <p className="text-gray-600 mb-4">
                {isGuest
                  ? "Create an account to book sessions with our counselors."
                  : filter === "all"
                  ? "Book your first session to get started with therapy."
                  : `You don't have any ${filter} sessions.`}
              </p>
              {isGuest ? (
                <Link
                  href="/auth/signup"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Sign Up
                </Link>
              ) : (
                <Link
                  href="/counselors"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Find a Counselor
                </Link>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Cancel Session Modal */}
      {showCancelModal && selectedSession && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Cancel Session
              </h3>
              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700 font-medium">
                  Session with {selectedSession.counselorName}
                </p>
                <p className="text-sm text-gray-600">
                  {formatDate(selectedSession.date, selectedSession.time)}
                </p>
                <p className="text-sm text-gray-600">
                  Duration: {selectedSession.duration} minutes • $
                  {selectedSession.amount}
                </p>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Please provide a reason for cancelling this session. Depending
                on when you cancel, you may be eligible for a full or partial
                refund.
              </p>
              <textarea
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
                rows={4}
                className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 p-3"
                placeholder="Enter cancellation reason..."
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={closeCancelModal}
                  disabled={cancelling}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Keep Session
                </button>
                <button
                  onClick={handleCancelSession}
                  disabled={!cancellationReason.trim() || cancelling}
                  className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {cancelling ? "Cancelling..." : "Cancel Session"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

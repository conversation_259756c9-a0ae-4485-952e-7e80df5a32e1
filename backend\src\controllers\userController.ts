import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { UserService } from "@/services/userService";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { validateFile, FILE_TYPES, FILE_SIZE_LIMITS } from "@/utils/cloudinary";

export class UserController {
  /**
   * Get current user profile
   */
  static async getProfile(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const user = await UserService.getUserProfile(req.user._id.toString());

      // Remove sensitive fields
      const userProfile = user.toObject();
      delete userProfile.password;
      delete userProfile.emailVerificationToken;
      delete userProfile.passwordResetToken;

      res.json({
        success: true,
        data: { user: userProfile },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user profile by ID (admin or public info)
   */
  static async getUserById(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;
      const user = await UserService.getUserProfile(userId);

      // Return limited info for non-admin users
      if (!req.user || !["admin", "superadmin"].includes(req.user.role)) {
        const publicProfile = {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          username: user.username,
          profilePicture: user.profilePicture,
          role: user.role,
          createdAt: user.createdAt,
        };

        return res.json({
          success: true,
          data: { user: publicProfile },
        });
      }

      // Full profile for admins
      const userProfile = user.toObject();
      delete userProfile.password;
      delete userProfile.emailVerificationToken;
      delete userProfile.passwordResetToken;

      res.json({
        success: true,
        data: { user: userProfile },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const updateData = req.body;
      const user = await UserService.updateProfile(
        req.user._id.toString(),
        updateData
      );

      // Remove sensitive fields
      const userProfile = user.toObject();
      delete userProfile.password;
      delete userProfile.emailVerificationToken;
      delete userProfile.passwordResetToken;

      res.json({
        success: true,
        message: "Profile updated successfully",
        data: { user: userProfile },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Upload profile picture
   */
  static async uploadProfilePicture(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      if (!req.file) {
        return next(createError("Profile picture file is required", 400));
      }

      // Validate file
      validateFile(
        req.file,
        FILE_TYPES.IMAGES,
        FILE_SIZE_LIMITS.PROFILE_PICTURE
      );

      const imageUrl = await UserService.uploadProfilePicture(
        req.user._id.toString(),
        req.file
      );

      res.json({
        success: true,
        message: "Profile picture uploaded successfully",
        data: { profilePicture: imageUrl },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete profile picture
   */
  static async deleteProfilePicture(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      await UserService.deleteProfilePicture(req.user._id.toString());

      res.json({
        success: true,
        message: "Profile picture deleted successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get users list (admin only)
   */
  static async getUsers(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "createdAt",
        sortOrder = "desc",
        role,
        isActive,
        isEmailVerified,
        areasOfInterest,
        country,
        search,
      } = req.query;

      const filters: any = {};
      if (role) filters.role = role;
      if (isActive !== undefined) filters.isActive = isActive === "true";
      if (isEmailVerified !== undefined)
        filters.isEmailVerified = isEmailVerified === "true";
      if (areasOfInterest)
        filters.areasOfInterest = Array.isArray(areasOfInterest)
          ? areasOfInterest
          : [areasOfInterest];
      if (country) filters.country = country;
      if (search) filters.search = search;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as "asc" | "desc",
      };

      const result = await UserService.getUsers(filters, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Deactivate user account
   */
  static async deactivateAccount(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      await UserService.deactivateAccount(req.user._id.toString());

      res.json({
        success: true,
        message: "Account deactivated successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reactivate user account (admin only)
   */
  static async reactivateAccount(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { userId } = req.params;
      await UserService.reactivateAccount(userId);

      res.json({
        success: true,
        message: "Account reactivated successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete user account permanently
   */
  static async deleteAccount(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { userId } = req.params;
      const targetUserId = userId || req.user._id.toString();

      // Users can only delete their own account unless they're admin
      if (
        targetUserId !== req.user._id.toString() &&
        !["admin", "superadmin"].includes(req.user.role)
      ) {
        return next(createError("You can only delete your own account", 403));
      }

      await UserService.deleteAccount(targetUserId);

      res.json({
        success: true,
        message: "Account deleted successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user statistics (admin only)
   */
  static async getUserStats(req: Request, res: Response, next: NextFunction) {
    try {
      const stats = await UserService.getUserStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user role (admin only)
   */
  static async updateUserRole(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;
      const { role } = req.body;

      if (!role) {
        return next(createError("Role is required", 400));
      }

      // Prevent non-superadmin from creating superadmin
      if (role === "superadmin" && req.user?.role !== "superadmin") {
        return next(
          createError("Only superadmin can assign superadmin role", 403)
        );
      }

      const user = await UserService.updateUserRole(userId, role);

      // Remove sensitive fields
      const userProfile = user.toObject();
      delete userProfile.password;
      delete userProfile.emailVerificationToken;
      delete userProfile.passwordResetToken;

      res.json({
        success: true,
        message: "User role updated successfully",
        data: { user: userProfile },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Bulk user operations (admin only)
   */
  static async bulkUserOperations(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { operation, userIds } = req.body;

      if (!operation || !userIds || !Array.isArray(userIds)) {
        return next(
          createError("Operation and userIds array are required", 400)
        );
      }

      const results = [];

      for (const userId of userIds) {
        try {
          switch (operation) {
            case "activate":
              await UserService.reactivateAccount(userId);
              results.push({
                userId,
                status: "success",
                operation: "activated",
              });
              break;
            case "deactivate":
              await UserService.deactivateAccount(userId);
              results.push({
                userId,
                status: "success",
                operation: "deactivated",
              });
              break;
            case "delete":
              await UserService.deleteAccount(userId);
              results.push({ userId, status: "success", operation: "deleted" });
              break;
            default:
              results.push({
                userId,
                status: "error",
                message: "Invalid operation",
              });
          }
        } catch (error) {
          logger.error(
            `Bulk operation ${operation} failed for user ${userId}:`,
            error
          );
          results.push({
            userId,
            status: "error",
            message: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      res.json({
        success: true,
        message: "Bulk operation completed",
        data: { results },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user app settings
   */
  static async getAppSettings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const user = await UserService.getUserProfile(req.user._id.toString());

      res.json({
        success: true,
        data: {
          appSettings: user.appSettings || {
            theme: "system",
            accentColor: "purple",
            fontSize: "medium",
            reducedMotion: false,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user app settings
   */
  static async updateAppSettings(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { theme, accentColor, fontSize, reducedMotion } = req.body;

      const user = await UserService.updateUserAppSettings(
        req.user._id.toString(),
        { theme, accentColor, fontSize, reducedMotion }
      );

      res.json({
        success: true,
        message: "App settings updated successfully",
        data: { appSettings: user.appSettings },
      });
    } catch (error) {
      next(error);
    }
  }
}

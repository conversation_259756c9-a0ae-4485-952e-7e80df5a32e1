import mongoose from "mongoose";
import { Session, ISession } from "@/models/Session";
import { Counselor } from "@/models/Counselor";
import { User } from "@/models/User";
import { PaymentService } from "@/services/paymentService";
import { AvailabilityService } from "@/services/availabilityService";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { sendEmail } from "@/utils/email";

export interface BookingRequest {
  counselorId: string;
  userId: string;
  scheduledAt: Date;
  duration: number; // in minutes
  sessionType: "individual" | "group" | "couples" | "family";
  notes?: string;
  isUrgent?: boolean;
  preferredLanguage?: string;
}

export interface BookingFilters {
  userId?: string;
  counselorId?: string;
  status?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  sessionType?: string;
}

export interface PricingCalculation {
  baseAmount: number;
  platformFee: number;
  totalAmount: number;
  counselorEarnings: number;
  currency: string;
  breakdown: {
    sessionCost: number;
    platformFee: number;
    paymentProcessingFee: number;
    total: number;
  };
}

export class BookingService {
  /**
   * Create a new booking
   */
  static async createBooking(bookingData: BookingRequest): Promise<ISession> {
    try {
      console.log("=== BOOKING SERVICE DEBUG ===");
      console.log(
        "Raw booking data received:",
        JSON.stringify(bookingData, null, 2)
      );
      console.log("counselorId:", bookingData.counselorId);
      console.log("counselorId type:", typeof bookingData.counselorId);
      console.log(
        "Is counselorId valid ObjectId?",
        mongoose.Types.ObjectId.isValid(bookingData.counselorId)
      );

      // Validate counselor
      const counselor = await Counselor.findById(
        bookingData.counselorId
      ).populate("userId");

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      if (counselor.verification.status !== "approved") {
        throw createError("Counselor is not approved", 400);
      }

      if (!counselor.settings.acceptingNewClients) {
        throw createError("Counselor is not accepting new clients", 400);
      }

      // Validate client
      const client = await User.findById(bookingData.userId);
      if (!client) {
        throw createError("Client not found", 404);
      }

      // Check availability
      const isAvailable = await AvailabilityService.isSlotAvailable(
        bookingData.counselorId,
        bookingData.scheduledAt,
        bookingData.scheduledAt.toTimeString().substring(0, 5),
        bookingData.duration
      );

      if (!isAvailable) {
        throw createError("Selected time slot is not available", 400);
      }

      // Calculate pricing
      const pricing = await this.calculatePricing(
        bookingData.counselorId,
        bookingData.duration,
        bookingData.isUrgent
      );

      // Generate payment reference
      const paymentReference =
        PaymentService.generatePaymentReference("SESSION");

      console.log("=== SESSION CREATION DEBUG ===");
      console.log("About to create session with:");
      console.log("- userId:", bookingData.userId);
      console.log("- counselorId:", bookingData.counselorId);
      console.log("- counselorId type:", typeof bookingData.counselorId);
      console.log("- counselorId is null?", bookingData.counselorId === null);
      console.log(
        "- counselorId is undefined?",
        bookingData.counselorId === undefined
      );
      console.log(
        "- counselorId stringified:",
        JSON.stringify(bookingData.counselorId)
      );
      console.log("- scheduledAt:", bookingData.scheduledAt);
      console.log("- duration:", bookingData.duration);
      console.log("- sessionType:", bookingData.sessionType);

      // Convert counselorId to ObjectId if it's a string
      const counselorObjectId =
        typeof bookingData.counselorId === "string"
          ? new mongoose.Types.ObjectId(bookingData.counselorId)
          : bookingData.counselorId;

      console.log("- counselorObjectId:", counselorObjectId);
      console.log("- counselorObjectId type:", typeof counselorObjectId);

      // Create session
      const session = new Session({
        userId: bookingData.userId,
        counselorId: counselorObjectId,
        scheduledAt: bookingData.scheduledAt,
        scheduledDate: bookingData.scheduledAt, // Required field
        duration: bookingData.duration,
        pricePerMinute: pricing.baseAmount / bookingData.duration, // Required field
        totalCost: pricing.totalAmount, // Required field
        type: bookingData.sessionType,
        status: counselor.settings.requiresApproval
          ? "pending_approval"
          : "scheduled",
        paymentStatus: "pending",
        paymentId: paymentReference,
        pricing: {
          totalAmount: pricing.totalAmount,
          platformFee: pricing.platformFee,
          currency: pricing.currency,
        },
        payment: {
          reference: paymentReference,
          status: "pending",
          method: "paystack",
        },
        notes: {
          clientNotes: bookingData.notes || "",
        },
        preferences: {
          language: bookingData.preferredLanguage || "en",
        },
      });

      await session.save();

      console.log("=== SESSION SAVED DEBUG ===");
      console.log("Session saved successfully:");
      console.log("- Session ID:", session._id);
      console.log("- Saved counselorId:", session.counselorId);
      console.log("- Saved counselorId type:", typeof session.counselorId);
      console.log(
        "- Full session object:",
        JSON.stringify(session.toObject(), null, 2)
      );

      // Send notifications
      await this.sendBookingNotifications(session, counselor, client);

      logger.info(
        `Booking created: ${session._id} for counselor: ${bookingData.counselorId}`
      );
      return session;
    } catch (error) {
      logger.error("Create booking error:", error);
      throw error;
    }
  }

  /**
   * Initialize payment for booking
   */
  static async initializePayment(sessionId: string): Promise<any> {
    try {
      const session = await Session.findById(sessionId)
        .populate("userId")
        .populate("counselorId");
        console.log(session, "session list")

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.payment.status !== "pending") {
        throw createError("Payment already processed or not required", 400);
      }

      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Initialize payment with Paystack
      const paymentData = await PaymentService.initializePayment({
        amount: session.pricing.totalAmount,
        email: client.email,
        currency: session.pricing.currency as "NGN" | "USD",
        reference: session.payment.reference,
        callback_url: `${process.env.FRONTEND_URL}/booking/payment/callback`,
        metadata: {
          sessionId: session._id.toString(),
          userId: client._id.toString(),
          counselorId: counselor._id.toString(),
          sessionType: session.type,
          scheduledAt: session.scheduledDate.toISOString(),
        },
      });

      // Update session with payment details
      session.payment.authorizationUrl = paymentData.authorization_url;
      session.payment.accessCode = paymentData.access_code;
      await session.save();

      logger.info(`Payment initialized for session: ${sessionId}`);
      return paymentData;
    } catch (error) {
      logger.error("Initialize payment error:", error);
      throw error;
    }
  }

  /**
   * Verify and complete payment
   */
  static async verifyPayment(reference: string): Promise<ISession> {
    try {
      // Find session by payment reference
      const session = await Session.findOne({ "payment.reference": reference })
        .populate("userId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      // Verify payment with Paystack
      const paymentVerification = await PaymentService.verifyPayment(reference);

      if (paymentVerification.status === "success") {
        // Update session payment status
        session.payment.status = "completed";
        session.payment.paidAt = new Date(paymentVerification.paid_at);
        session.payment.transactionId = paymentVerification.reference;
        session.payment.gateway_response = paymentVerification.gateway_response;
        session.payment.fees = paymentVerification.fees / 100; // Convert from kobo/cents

        // Update session status
        if (session.status === "pending_payment") {
          session.status = "scheduled";
        }

        await session.save();

        // Update counselor earnings
        await this.updateCounselorEarnings(
          session.counselorId.toString(),
          session.pricing.totalAmount - session.pricing.platformFee
        );

        // Send confirmation notifications
        await this.sendPaymentConfirmationNotifications(session);

        logger.info(
          `Payment verified and completed for session: ${session._id}`
        );
        return session;
      } else {
        // Payment failed
        session.payment.status = "failed";
        session.payment.gateway_response = paymentVerification.gateway_response;
        session.status = "cancelled";
        await session.save();

        throw createError("Payment verification failed", 400);
      }
    } catch (error) {
      logger.error("Verify payment error:", error);
      throw error;
    }
  }

  /**
   * Get bookings with filters
   */
  static async getBookings(filters: BookingFilters = {}, options: any = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "scheduledAt",
        sortOrder = "desc",
      } = options;

      // Build query
      const query: any = {};

      if (filters.userId) query.userId = filters.userId;
      if (filters.counselorId) query.counselorId = filters.counselorId;
      if (filters.status?.length) query.status = { $in: filters.status };
      if (filters.sessionType) query.type = filters.sessionType;

      if (filters.dateFrom || filters.dateTo) {
        query.scheduledAt = {};
        if (filters.dateFrom) query.scheduledAt.$gte = filters.dateFrom;
        if (filters.dateTo) query.scheduledAt.$lte = filters.dateTo;
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === "asc" ? 1 : -1;

      const [sessions, total] = await Promise.all([
        Session.find(query)
          .populate("userId", "firstName lastName email profilePicture")
          .populate("counselorId", "userId specializations")
          .sort(sortOptions)
          .skip(skip)
          .limit(limit),
        Session.countDocuments(query),
      ]);

      return {
        sessions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get bookings error:", error);
      throw error;
    }
  }

  /**
   * Get booking by ID
   */
  static async getBooking(sessionId: string): Promise<ISession> {
    try {
      logger.info("=== BOOKING SERVICE DEBUG ===");
      logger.info("Looking for session ID:", sessionId);

      const session = await Session.findById(sessionId)
        .populate("userId", "firstName lastName email profilePicture phone")
        .populate({
          path: "counselorId",
          populate: {
            path: "userId",
            select: "firstName lastName email profilePicture",
          },
        });

      logger.info("Session found:", {
        exists: !!session,
        sessionId: session?._id,
        userId: session?.userId,
        counselorId: session?.counselorId,
        status: session?.status,
      });

      if (!session) {
        throw createError("Booking not found", 404);
      }

      return session;
    } catch (error) {
      logger.error("Get booking error:", error);
      throw error;
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(
    sessionId: string,
    cancelledBy: string,
    reason: string,
    refundAmount?: number
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId)
        .populate("userId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.status === "cancelled" || session.status === "completed") {
        throw createError("Session cannot be cancelled", 400);
      }

      // Calculate refund amount based on cancellation policy
      const refund = refundAmount || this.calculateRefundAmount(session);

      // Update session status
      session.status = "cancelled";
      session.cancellation = {
        cancelledBy: cancelledBy as any,
        cancelledAt: new Date(),
        reason,
        refundEligible: true,
        refundAmount: refund,
      };

      await session.save();

      // Process refund if applicable
      if (refund > 0 && session.payment.status === "completed") {
        await this.processRefund(session, refund);
      }

      // Send cancellation notifications
      await this.sendCancellationNotifications(session, reason);

      logger.info(`Booking cancelled: ${sessionId} by ${cancelledBy}`);
      return session;
    } catch (error) {
      logger.error("Cancel booking error:", error);
      throw error;
    }
  }

  /**
   * Reschedule booking
   */
  static async rescheduleBooking(
    sessionId: string,
    newScheduledAt: Date,
    requestedBy: string,
    reason?: string
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId)
        .populate("userId")
        .populate("counselorId");

      if (!session) {
        throw createError("Session not found", 404);
      }

      // Check if the new time slot is available
      const isAvailable = await AvailabilityService.isSlotAvailable(
        session.counselorId.toString(),
        newScheduledAt,
        newScheduledAt.toTimeString().substring(0, 5),
        session.duration
      );

      if (!isAvailable) {
        throw createError("New time slot is not available", 400);
      }

      // Store original schedule for history
      const originalScheduledAt = session.scheduledAt;

      // Update session
      session.scheduledAt = newScheduledAt;
      session.reschedule = {
        originalScheduledAt,
        newScheduledAt,
        requestedBy: requestedBy as any,
        requestedAt: new Date(),
        reason: reason || "Rescheduled by user",
      };

      await session.save();

      // Send reschedule notifications
      await this.sendRescheduleNotifications(session, originalScheduledAt);

      logger.info(`Booking rescheduled: ${sessionId} to ${newScheduledAt}`);
      return session;
    } catch (error) {
      logger.error("Reschedule booking error:", error);
      throw error;
    }
  }

  /**
   * Calculate pricing for a session
   */
  static async calculatePricing(
    counselorId: string,
    duration: number,
    isUrgent: boolean = false
  ): Promise<PricingCalculation> {
    try {
      console.log("=== CALCULATE PRICING DEBUG ===");
      console.log("Input params:", { counselorId, duration, isUrgent });

      const counselor = await Counselor.findById(counselorId);

      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      const sessionRate = counselor.pricing.ratePerMinute;
      const baseAmount = sessionRate * duration;

      let totalAmount = baseAmount;

      // Add urgent fee (20% extra)
      let urgentFee = 0;
      if (isUrgent) {
        urgentFee = baseAmount * 0.2;
        totalAmount += urgentFee;
      }

      console.log("Calculated values:", {
        sessionRate,
        baseAmount,
        totalAmount,
        urgentFee,
      });

      // Calculate platform fee (10% of total)
      const platformFee = PaymentService.calculatePlatformFee(totalAmount, 10);
      const counselorEarnings = PaymentService.calculateCounselorEarnings(
        totalAmount,
        10
      );

      // Calculate payment processing fee (typically 1.5% + fixed fee)
      const paymentProcessingFee = Math.round(totalAmount * 0.015) + 100; // 1.5% + 100 NGN fixed fee
      const finalTotal = totalAmount + paymentProcessingFee;

      const result = {
        baseAmount,
        platformFee,
        totalAmount: finalTotal,
        counselorEarnings,
        currency: counselor.pricing.currency,
        breakdown: {
          sessionCost: baseAmount + (urgentFee || 0),
          platformFee,
          paymentProcessingFee,
          total: finalTotal,
        },
      };

      console.log("Pricing calculation result:", result);
      return result;
    } catch (error) {
      logger.error("Calculate pricing error:", error);
      throw error;
    }
  }

  /**
   * Calculate refund amount based on cancellation policy
   */
  private static calculateRefundAmount(session: ISession): number {
    const now = new Date();
    const sessionTime = new Date(session.scheduledAt);
    const hoursUntilSession =
      (sessionTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    // Refund policy:
    // - More than 24 hours: 100% refund
    // - 12-24 hours: 50% refund
    // - Less than 12 hours: No refund
    if (hoursUntilSession > 24) {
      return session.pricing.totalAmount;
    } else if (hoursUntilSession > 12) {
      return session.pricing.totalAmount * 0.5;
    } else {
      return 0;
    }
  }

  /**
   * Process refund
   */
  private static async processRefund(
    session: ISession,
    refundAmount: number
  ): Promise<void> {
    try {
      if (session.payment.transactionId) {
        await PaymentService.createRefund({
          transaction: session.payment.transactionId,
          amount: refundAmount,
          customer_note: "Session cancellation refund",
          merchant_note: `Refund for cancelled session ${session._id}`,
        });

        logger.info(
          `Refund processed: ${refundAmount} for session ${session._id}`
        );
      }
    } catch (error) {
      logger.error("Process refund error:", error);
      // Don't throw error to avoid blocking cancellation
    }
  }

  /**
   * Update counselor earnings
   */
  private static async updateCounselorEarnings(
    counselorId: string,
    earnings: number
  ): Promise<void> {
    try {
      await Counselor.findByIdAndUpdate(counselorId, {
        $inc: {
          "statistics.totalEarnings": earnings,
          "statistics.totalSessions": 1,
        },
      });
    } catch (error) {
      logger.error("Update counselor earnings error:", error);
    }
  }

  /**
   * Send booking notifications
   */
  private static async sendBookingNotifications(
    session: ISession,
    counselor: any,
    client: any
  ): Promise<void> {
    try {
      // Send email to client
      await sendEmail({
        to: client.email,
        subject: "Booking Confirmation - Theramea",
        html: `
          <h2>Booking Confirmation</h2>
          <p>Hi ${client.firstName},</p>
          <p>Your session has been booked successfully!</p>
          <p><strong>Details:</strong></p>
          <ul>
            <li>Counselor: ${counselor.userId.firstName} ${
          counselor.userId.lastName
        }</li>
            <li>Date & Time: ${session.scheduledAt.toLocaleString()}</li>
            <li>Duration: ${session.duration} minutes</li>
            <li>Amount: ${session.pricing.currency} ${
          session.pricing.totalAmount
        }</li>
          </ul>
          <p>Please complete your payment to confirm the session.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });

      // Send email to counselor
      await sendEmail({
        to: counselor.userId.email,
        subject: "New Booking Request - Theramea",
        html: `
          <h2>New Booking Request</h2>
          <p>Hi ${counselor.userId.firstName},</p>
          <p>You have a new session booking!</p>
          <p><strong>Details:</strong></p>
          <ul>
            <li>Client: ${client.firstName} ${client.lastName}</li>
            <li>Date & Time: ${session.scheduledAt.toLocaleString()}</li>
            <li>Duration: ${session.duration} minutes</li>
            <li>Type: ${session.type}</li>
          </ul>
          <p>Please log in to your dashboard to manage this booking.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });
    } catch (error) {
      logger.error("Send booking notifications error:", error);
    }
  }

  /**
   * Send payment confirmation notifications
   */
  private static async sendPaymentConfirmationNotifications(
    session: ISession
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Send confirmation to client
      await sendEmail({
        to: client.email,
        subject: "Payment Confirmed - Session Scheduled",
        html: `
          <h2>Payment Confirmed</h2>
          <p>Hi ${client.firstName},</p>
          <p>Your payment has been confirmed and your session is now scheduled!</p>
          <p><strong>Session Details:</strong></p>
          <ul>
            <li>Counselor: ${counselor.userId.firstName} ${
          counselor.userId.lastName
        }</li>
            <li>Date & Time: ${session.scheduledAt.toLocaleString()}</li>
            <li>Duration: ${session.duration} minutes</li>
          </ul>
          <p>You will receive a session link closer to the appointment time.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });
    } catch (error) {
      logger.error("Send payment confirmation notifications error:", error);
    }
  }

  /**
   * Send cancellation notifications
   */
  private static async sendCancellationNotifications(
    session: ISession,
    reason: string
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Notify both parties
      const recipients = [
        { email: client.email, name: client.firstName, role: "client" },
        {
          email: counselor.userId.email,
          name: counselor.userId.firstName,
          role: "counselor",
        },
      ];

      for (const recipient of recipients) {
        await sendEmail({
          to: recipient.email,
          subject: "Session Cancelled - Theramea",
          html: `
            <h2>Session Cancelled</h2>
            <p>Hi ${recipient.name},</p>
            <p>The session scheduled for ${session.scheduledAt.toLocaleString()} has been cancelled.</p>
            <p><strong>Reason:</strong> ${reason}</p>
            ${
              session.cancellation?.refundAmount
                ? `<p>A refund of ${session.pricing.currency} ${session.cancellation.refundAmount} will be processed within 3-5 business days.</p>`
                : ""
            }
            <p>Best regards,<br>The Theramea Team</p>
          `,
        });
      }
    } catch (error) {
      logger.error("Send cancellation notifications error:", error);
    }
  }

  /**
   * Send reschedule notifications
   */
  private static async sendRescheduleNotifications(
    session: ISession,
    originalTime: Date
  ): Promise<void> {
    try {
      const client = session.userId as any;
      const counselor = session.counselorId as any;

      // Notify both parties
      const recipients = [
        { email: client.email, name: client.firstName },
        { email: counselor.userId.email, name: counselor.userId.firstName },
      ];

      for (const recipient of recipients) {
        await sendEmail({
          to: recipient.email,
          subject: "Session Rescheduled - Theramea",
          html: `
            <h2>Session Rescheduled</h2>
            <p>Hi ${recipient.name},</p>
            <p>Your session has been rescheduled.</p>
            <p><strong>Original Time:</strong> ${originalTime.toLocaleString()}</p>
            <p><strong>New Time:</strong> ${session.scheduledAt.toLocaleString()}</p>
            <p>Please update your calendar accordingly.</p>
            <p>Best regards,<br>The Theramea Team</p>
          `,
        });
      }
    } catch (error) {
      logger.error("Send reschedule notifications error:", error);
    }
  }

  /**
   * Approve booking (counselor action)
   */
  static async approveBooking(
    sessionId: string,
    counselorId: string
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.counselorId.toString() !== counselorId) {
        throw createError("Unauthorized to approve this booking", 403);
      }

      if (session.status !== "pending_approval") {
        throw createError("Session is not pending approval", 400);
      }

      session.status =
        session.payment.status === "completed"
          ? "scheduled"
          : "pending_payment";
      session.approval = {
        status: "approved",
        approvedBy: counselorId as any,
        approvedAt: new Date(),
      };

      await session.save();

      // Send approval notification
      await this.sendApprovalNotification(session);

      logger.info(
        `Booking approved: ${sessionId} by counselor: ${counselorId}`
      );
      return session;
    } catch (error) {
      logger.error("Approve booking error:", error);
      throw error;
    }
  }

  /**
   * Reject booking (counselor action)
   */
  static async rejectBooking(
    sessionId: string,
    counselorId: string,
    reason: string
  ): Promise<ISession> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        throw createError("Session not found", 404);
      }

      if (session.counselorId.toString() !== counselorId) {
        throw createError("Unauthorized to reject this booking", 403);
      }

      if (session.status !== "pending_approval") {
        throw createError("Session is not pending approval", 400);
      }

      session.status = "cancelled";
      session.cancellation = {
        cancelledBy: counselorId as any,
        cancelledAt: new Date(),
        reason,
        refundEligible: true,
        refundAmount: session.pricing.totalAmount, // Full refund for rejection
      };

      await session.save();

      // Process full refund
      if (session.payment.status === "completed") {
        await this.processRefund(session, session.pricing.totalAmount);
      }

      // Send rejection notification
      await this.sendRejectionNotification(session, reason);

      logger.info(
        `Booking rejected: ${sessionId} by counselor: ${counselorId}`
      );
      return session;
    } catch (error) {
      logger.error("Reject booking error:", error);
      throw error;
    }
  }

  /**
   * Send approval notification
   */
  private static async sendApprovalNotification(
    session: ISession
  ): Promise<void> {
    try {
      const client = session.userId as any;

      await sendEmail({
        to: client.email,
        subject: "Session Approved - Theramea",
        html: `
          <h2>Session Approved</h2>
          <p>Hi ${client.firstName},</p>
          <p>Great news! Your session has been approved by the counselor.</p>
          <p><strong>Session Details:</strong></p>
          <ul>
            <li>Date & Time: ${session.scheduledAt.toLocaleString()}</li>
            <li>Duration: ${session.duration} minutes</li>
          </ul>
          ${
            session.payment.status !== "completed"
              ? "<p>Please complete your payment to confirm the session.</p>"
              : "<p>Your session is now confirmed!</p>"
          }
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });
    } catch (error) {
      logger.error("Send approval notification error:", error);
    }
  }

  /**
   * Send rejection notification
   */
  private static async sendRejectionNotification(
    session: ISession,
    reason: string
  ): Promise<void> {
    try {
      const client = session.userId as any;

      await sendEmail({
        to: client.email,
        subject: "Session Request Not Approved - Theramea",
        html: `
          <h2>Session Request Update</h2>
          <p>Hi ${client.firstName},</p>
          <p>Unfortunately, your session request could not be approved at this time.</p>
          <p><strong>Reason:</strong> ${reason}</p>
          <p>A full refund will be processed within 3-5 business days if payment was made.</p>
          <p>Please feel free to book with another counselor or try a different time slot.</p>
          <p>Best regards,<br>The Theramea Team</p>
        `,
      });
    } catch (error) {
      logger.error("Send rejection notification error:", error);
    }
  }
}

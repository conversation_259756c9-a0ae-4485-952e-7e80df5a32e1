"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useHydratedAuth } from "@/hooks/useHydratedAuth";
import Header from "@/components/layout/Header";
import Hero from "../components/home/<USER>";
import Features from "../components/home/<USER>";
import Testimonials from "../components/home/<USER>";
import CTA from "../components/home/<USER>";
import Footer from "../components/layout/Footer";

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, isGuest, isReady, checkAuth } = useHydratedAuth();

  useEffect(() => {
    if (isReady) {
      checkAuth();
    }
  }, [checkAuth, isReady]);

  useEffect(() => {
    if (isReady && isAuthenticated) {
      if (isGuest) {
        router.push("/explore");
      } else {
        router.push("/dashboard");
      }
    }
  }, [isAuthenticated, isGuest, router, isReady]);

  // Show loading until ready
  if (!isReady) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (isAuthenticated) {
    return null; // Will redirect to appropriate page based on user type
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <Hero />
      <Features />
      <Testimonials />
      <CTA />
      <Footer />
    </div>
  );
}

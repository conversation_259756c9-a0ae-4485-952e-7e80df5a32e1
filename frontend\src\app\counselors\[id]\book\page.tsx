"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useHydratedAuth } from "@/hooks/useHydratedAuth";
import { useAuthStore } from "@/store/authStore";
import { counselorAPI, bookingAPI } from "@/lib/counselor";
import { Counselor, BookingRequest } from "@/types/counselor";
import Header from "@/components/layout/Header";
import BookingForm from "@/components/booking/BookingForm";
import CounselorSummary from "@/components/booking/CounselorSummary";
import BookingSummary from "@/components/booking/BookingSummary";
import {
  CalendarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ArrowLeftIcon,
} from "@heroicons/react/24/outline";

export default function BookSessionPage() {
  const router = useRouter();
  const params = useParams();
  const counselorId = params.id as string;

  const { isAuthenticated, isGuest, user, isReady } = useHydratedAuth();
  const { tokens } = useAuthStore();
  const [counselor, setCounselor] = useState<Counselor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState<Partial<BookingRequest>>({
    counselorId,
    sessionType: "individual",
    duration: 60,
  });

  const token = tokens?.accessToken;

  useEffect(() => {
    if (!isReady) return;

    if (!isAuthenticated && !isGuest) {
      router.push("/auth/login");
      return;
    }

    if (isGuest) {
      // Show upgrade prompt for guests
      router.push(
        "/auth/signup?redirect=" + encodeURIComponent(window.location.pathname)
      );
      return;
    }

    if (counselorId && token) {
      fetchCounselor();
    }
  }, [isAuthenticated, isGuest, counselorId, token, router, isReady]);

  const fetchCounselor = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await counselorAPI.getCounselor(counselorId, token!);
      setCounselor(response.data.counselor);

      if (!response.data.counselor.settings.acceptingNewClients) {
        setError("This counselor is not currently accepting new clients.");
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to load counselor information"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBookingDataChange = (data: Partial<BookingRequest>) => {
    setBookingData((prev) => ({ ...prev, ...data }));
  };

  const handleNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleBookingSubmit = async () => {
    try {
      if (!user || !token) {
        throw new Error("Authentication required");
      }

      const completeBookingData: BookingRequest = {
        ...(bookingData as BookingRequest),
        userId: user._id,
      };

      console.log("=== BOOKING SUBMISSION DEBUG ===");
      console.log("Final booking data being sent:", completeBookingData);
      console.log(
        "counselorId in final data:",
        completeBookingData.counselorId
      );

      const response = await bookingAPI.createBooking(
        completeBookingData,
        token
      );

      // Redirect to payment page
      router.push(`/booking/${response.data.session._id}/payment`);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create booking");
    }
  };

  if (!isReady || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (error || !counselor) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Unable to Book Session
            </h1>
            <p className="text-gray-600 mb-6">
              {error || "Counselor information could not be loaded."}
            </p>
            <button
              onClick={() => router.back()}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md font-medium"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  const steps = [
    { number: 1, title: "Session Details", icon: CalendarIcon },
    { number: 2, title: "Review & Confirm", icon: ClockIcon },
    { number: 3, title: "Payment", icon: CurrencyDollarIcon },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center text-purple-600 hover:text-purple-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Profile
          </button>

          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Book a Session
          </h1>
          <p className="text-gray-600">
            Schedule your counseling session with {counselor.user?.firstName}{" "}
            {counselor.user?.lastName}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.number;
              const isCompleted = currentStep > step.number;

              return (
                <div key={step.number} className="flex items-center">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isActive
                        ? "border-purple-600 bg-purple-600 text-white"
                        : isCompleted
                        ? "border-green-600 bg-green-600 text-white"
                        : "border-gray-300 bg-white text-gray-500"
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                  </div>
                  <div className="ml-3">
                    <p
                      className={`text-sm font-medium ${
                        isActive
                          ? "text-purple-600"
                          : isCompleted
                          ? "text-green-600"
                          : "text-gray-500"
                      }`}
                    >
                      Step {step.number}
                    </p>
                    <p
                      className={`text-sm ${
                        isActive
                          ? "text-purple-600"
                          : isCompleted
                          ? "text-green-600"
                          : "text-gray-500"
                      }`}
                    >
                      {step.title}
                    </p>
                  </div>

                  {index < steps.length - 1 && (
                    <div
                      className={`w-16 h-0.5 mx-4 ${
                        isCompleted ? "bg-green-600" : "bg-gray-300"
                      }`}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Form */}
          <div className="lg:col-span-2">
            {currentStep === 1 && (
              <BookingForm
                counselor={counselor}
                bookingData={bookingData}
                onChange={handleBookingDataChange}
                onNext={handleNextStep}
              />
            )}

            {currentStep === 2 && (
              <BookingSummary
                counselor={counselor}
                bookingData={bookingData as BookingRequest}
                onPrev={handlePrevStep}
                onConfirm={handleBookingSubmit}
              />
            )}
          </div>

          {/* Right Column - Counselor Summary */}
          <div className="lg:col-span-1">
            <CounselorSummary counselor={counselor} bookingData={bookingData} />
          </div>
        </div>
      </div>
    </div>
  );
}

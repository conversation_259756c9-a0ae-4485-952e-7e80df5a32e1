const axios = require("axios");

// Test counselor data
const testCounselors = [
  {
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    password: "securepassword123",
    specializations: ["Anxiety", "Depression", "Trauma"],
    bio: "Dr. <PERSON> is a licensed clinical psychologist with over 10 years of experience helping clients overcome anxiety and depression. She specializes in cognitive-behavioral therapy and trauma-informed care.",
    experience: 10,
    qualifications: [
      "PhD in Clinical Psychology",
      "Licensed Clinical Psychologist",
      "Trauma-Informed Care Certification",
    ],
    languages: ["English", "Spanish"],
    sessionTypes: ["Video", "Chat", "Phone"],
    availability: {
      monday: [{ start: "09:00", end: "17:00" }],
      tuesday: [{ start: "09:00", end: "17:00" }],
      wednesday: [{ start: "09:00", end: "17:00" }],
      thursday: [{ start: "09:00", end: "17:00" }],
      friday: [{ start: "09:00", end: "15:00" }],
    },
    pricing: {
      video: 15000, // ₦150
      chat: 10000, // ₦100
      phone: 12000, // ₦120
    },
    profilePicture:
      "https://images.unsplash.com/photo-**********-11d035aa65de?w=400&h=400&fit=crop&crop=face",
    isActive: true,
    isVerified: true,
  },
  {
    name: "Dr. <PERSON> Johnson",
    email: "<EMAIL>",
    password: "securepassword123",
    specializations: [
      "Relationship Counseling",
      "Family Therapy",
      "Stress Management",
    ],
    bio: "Dr. <PERSON> Johnson is a licensed marriage and family therapist with 8 years of experience. He helps couples and families build stronger relationships and develop healthy communication patterns.",
    experience: 8,
    qualifications: [
      "MSW in Clinical Social Work",
      "Licensed Marriage and Family Therapist",
      "Family Systems Therapy Certification",
    ],
    languages: ["English"],
    sessionTypes: ["Video", "Chat"],
    availability: {
      monday: [{ start: "10:00", end: "18:00" }],
      tuesday: [{ start: "10:00", end: "18:00" }],
      wednesday: [{ start: "10:00", end: "18:00" }],
      thursday: [{ start: "10:00", end: "18:00" }],
      saturday: [{ start: "09:00", end: "14:00" }],
    },
    pricing: {
      video: 18000, // ₦180
      chat: 12000, // ₦120
    },
    profilePicture:
      "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face",
    isActive: true,
    isVerified: true,
  },
];

async function createTestCounselors() {
  try {
    console.log("Creating test counselors...");

    for (let i = 0; i < testCounselors.length; i++) {
      const counselor = testCounselors[i];
      console.log(`\nCreating counselor ${i + 1}: ${counselor.name}`);

      try {
        const response = await axios.post(
          "http://localhost:5000/api/auth/register/counselor",
          counselor,
          {
            headers: {
              "Content-Type": "application/json",
            },
            timeout: 10000,
          }
        );

        console.log(`✓ Successfully created: ${counselor.name}`);
        console.log(`  ID: ${response.data.user?.id || "Unknown"}`);
      } catch (error) {
        if (error.response) {
          console.log(
            `✗ Failed to create ${counselor.name}: ${error.response.status} - ${
              error.response.data?.message || error.response.statusText
            }`
          );
          if (error.response.data?.details) {
            console.log(
              `  Details: ${JSON.stringify(error.response.data.details)}`
            );
          }
        } else if (error.request) {
          console.log(
            `✗ Network error creating ${counselor.name}: ${error.message}`
          );
        } else {
          console.log(`✗ Error creating ${counselor.name}: ${error.message}`);
        }
      }
    }

    console.log("\n✓ Test counselor creation completed!");

    // Test the API endpoint
    console.log("\nTesting counselors API...");
    try {
      const response = await axios.get("http://localhost:5000/api/counselors", {
        timeout: 10000,
      });
      console.log(`✓ API Response: ${response.status}`);
      console.log(
        `✓ Counselors found: ${response.data.data?.counselors?.length || 0}`
      );
      if (response.data.data?.counselors?.length > 0) {
        console.log("  Counselor names:");
        response.data.data.counselors.forEach((c, i) => {
          console.log(`    ${i + 1}. ${c.name} (ID: ${c._id})`);
        });
      }
    } catch (error) {
      console.log(`✗ API test failed: ${error.message}`);
    }
  } catch (error) {
    console.error("Script failed:", error.message);
    process.exit(1);
  }
}

createTestCounselors();

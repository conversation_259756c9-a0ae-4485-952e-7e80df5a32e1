// Test script to verify registration endpoint
const http = require("http");

function testRegistration() {
  const postData = JSON.stringify({
    firstName: "Test",
    lastName: "User",
    username: "testuser123",
    email: "<EMAIL>",
    password: "TestPassword123",
    areasOfInterest: ["anxiety", "stress"],
  });

  const options = {
    hostname: "localhost",
    port: 5000,
    path: "/api/auth/register",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Content-Length": Buffer.byteLength(postData),
    },
  };

  console.log("🧪 Testing registration endpoint...");
  console.log(
    "URL:",
    `http://${options.hostname}:${options.port}${options.path}`
  );
  console.log("Data:", postData);

  const req = http.request(options, (res) => {
    console.log(`\n📊 Response Status: ${res.statusCode}`);
    console.log("📋 Response Headers:", res.headers);

    let data = "";
    res.on("data", (chunk) => {
      data += chunk;
    });

    res.on("end", () => {
      console.log("\n📄 Response Body:");
      try {
        const jsonData = JSON.parse(data);
        console.log(JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log(data);
      }
    });
  });

  req.on("error", (error) => {
    console.error("❌ Request failed:", error.message);
    console.error("🔍 This usually means:");
    console.error("   1. Backend server is not running");
    console.error("   2. Backend is not listening on port 5000");
    console.error("   3. Network connectivity issue");
  });

  req.write(postData);
  req.end();
}

// Also test health endpoint
function testHealth() {
  const options = {
    hostname: "localhost",
    port: 5000,
    path: "/api/health",
    method: "GET",
  };

  console.log("🏥 Testing health endpoint...");

  const req = http.request(options, (res) => {
    let data = "";
    res.on("data", (chunk) => {
      data += chunk;
    });

    res.on("end", () => {
      console.log(`✅ Health check: ${res.statusCode}`);
      console.log(data);
      console.log("\n" + "=".repeat(50));
      testRegistration();
    });
  });

  req.on("error", (error) => {
    console.error("❌ Health check failed:", error.message);
    console.error("Backend is not accessible. Please check if it's running.");
  });

  req.end();
}

testHealth();

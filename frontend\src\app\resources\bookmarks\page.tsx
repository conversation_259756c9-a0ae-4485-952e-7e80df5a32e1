"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Header from "@/components/layout/Header";
import ResourceCard from "@/components/resources/ResourceCard";
import SignupPromptModal from "@/components/modals/SignupPromptModal";
import { useAuthStore } from "@/store/authStore";
import { resourcesAPI } from "@/lib/resources";
import { Resource } from "@/types/resources";

export default function BookmarksPage() {
  const {
    tokens,
    isAuthenticated,
    isGuest,
    isLoading: authLoading,
  } = useAuthStore();
  const [bookmarks, setBookmarks] = useState<Resource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalBookmarks, setTotalBookmarks] = useState(0);
  const router = useRouter();

  useEffect(() => {
    // Wait for auth to be determined
    if (authLoading) {
      setIsLoading(true);
      return;
    }

    // Check authentication status
    if (!isAuthenticated || isGuest) {
      setIsLoading(false);
      setShowSignupModal(true);
      return;
    }

    // User is authenticated, fetch bookmarks
    fetchBookmarks();
  }, [isAuthenticated, isGuest, authLoading, currentPage]);

  const fetchBookmarks = async () => {
    if (!tokens?.accessToken) return;

    try {
      setIsLoading(true);
      const response = await resourcesAPI.getUserBookmarks(
        tokens.accessToken,
        currentPage,
        12 // 12 per page for nice grid layout
      );

      if (response.success) {
        setBookmarks(response.data.content);
        setTotalPages(response.data.pagination.pages);
        setTotalBookmarks(response.data.pagination.total);
      }
    } catch (error) {
      console.error("Error fetching bookmarks:", error);
      setBookmarks([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveBookmark = async (resourceId: string) => {
    if (!tokens?.accessToken) return;

    try {
      await resourcesAPI.unbookmarkResource(resourceId, tokens.accessToken);
      // Remove from local state
      setBookmarks((prev) =>
        prev.filter((bookmark) => bookmark._id !== resourceId)
      );
      setTotalBookmarks((prev) => prev - 1);
    } catch (error) {
      console.error("Error removing bookmark:", error);
    }
  };

  const handleSignupModalClose = () => {
    setShowSignupModal(false);
    router.push("/resources"); // Redirect to main resources page
  };

  // Show signup modal for unauthenticated users
  if (!authLoading && (!isAuthenticated || isGuest)) {
    return (
      <>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="text-6xl mb-4">🔒</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Authentication Required
              </h2>
              <p className="text-gray-600 mb-4">
                Please sign up or log in to view your bookmarks.
              </p>
            </div>
          </div>
        </div>

        <SignupPromptModal
          isOpen={showSignupModal}
          onClose={handleSignupModalClose}
          feature="bookmark"
        />
      </>
    );
  }

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
            >
              <svg
                className="w-5 h-5 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Bookmarks</h1>
              <p className="text-gray-600">
                Resources you've bookmarked for later reading
              </p>
            </div>
          </div>

          {/* Stats */}
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <span className="flex items-center space-x-1">
              <svg
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                />
              </svg>
              <span>{totalBookmarks} bookmarks</span>
            </span>
            <span>•</span>
            <span>Updated {new Date().toLocaleDateString()}</span>
          </div>
        </div>

        {/* Content */}
        {bookmarks.length > 0 ? (
          <>
            {/* Resources Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {bookmarks.map((resource) => (
                <ResourceCard
                  key={resource._id}
                  resource={resource}
                  onBookmark={(resourceId, isBookmarked) => {
                    if (!isBookmarked) {
                      handleRemoveBookmark(resourceId);
                    }
                  }}
                  onLike={(resourceId, isLiked) => {
                    console.log(
                      `Resource ${resourceId} ${isLiked ? "liked" : "unliked"}`
                    );
                  }}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>

                <span className="px-4 py-2 text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            )}
          </>
        ) : (
          /* Empty State */
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No bookmarks yet
            </h3>
            <p className="text-gray-600 mb-6">
              Start bookmarking resources to save them for later reading.
            </p>
            <button
              onClick={() => router.push("/resources")}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Browse Resources
            </button>
          </div>
        )}
      </main>
    </div>
  );
}

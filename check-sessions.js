// Test script to check sessions in database
const mongoose = require("mongoose");

async function checkSessions() {
  try {
    await mongoose.connect("mongodb://localhost:27017/theramea");
    console.log("Connected to MongoDB");

    // Define a simple Session schema for querying
    const sessionSchema = new mongoose.Schema({}, { strict: false });
    const Session = mongoose.model("Session", sessionSchema);

    // Check for specific session
    const specificSession = await Session.findById("689e688440af152213173417");
    console.log(
      "Specific session (689e688440af152213173417):",
      specificSession
    );

    // Get recent sessions
    const recentSessions = await Session.find({})
      .sort({ _id: -1 })
      .limit(5)
      .lean();

    console.log("\nRecent sessions:");
    recentSessions.forEach((session) => {
      console.log({
        _id: session._id,
        userId: session.userId,
        counselorId: session.counselorId,
        status: session.status,
        paymentStatus: session.paymentStatus,
        createdAt: session.createdAt || "unknown",
      });
    });

    // Check if there are any sessions for user 687fe3b739da96aef19699d3
    const userSessions = await Session.find({
      userId: new mongoose.Types.ObjectId("687fe3b739da96aef19699d3"),
    }).lean();

    console.log("\nSessions for user 687fe3b739da96aef19699d3:");
    userSessions.forEach((session) => {
      console.log({
        _id: session._id,
        userId: session.userId,
        counselorId: session.counselorId,
        status: session.status,
        paymentStatus: session.paymentStatus,
      });
    });
  } catch (error) {
    console.error("Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

checkSessions();

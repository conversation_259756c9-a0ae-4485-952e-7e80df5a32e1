import { chatAPI } from "@/lib/chat";
import { ChatRoom as ChatRoomType } from "@/types/chat";

export interface ChatroomData {
  id: string;
  name: string;
  description: string;
  category: string;
  activeUsers: number;
  totalMessages: number;
  lastActivity: string;
  isActive: boolean;
  tags: string[];
}

const mockChatrooms: ChatroomData[] = [
  {
    id: "stress-support",
    name: "Daily Stress Support",
    description:
      "Share your daily stressors and find coping strategies together",
    category: "stress",
    activeUsers: 23,
    totalMessages: 1247,
    lastActivity: "2 minutes ago",
    isActive: true,
    tags: ["stress", "coping", "daily-life"],
  },
  {
    id: "student-life",
    name: "Student Life Hub",
    description:
      "Academic pressure, social challenges, and campus life discussions",
    category: "student",
    activeUsers: 18,
    totalMessages: 892,
    lastActivity: "5 minutes ago",
    isActive: true,
    tags: ["academic", "college", "pressure"],
  },
  {
    id: "relationship-talks",
    name: "Relationship Talks",
    description: "Navigate dating, friendships, and family relationships",
    category: "relationships",
    activeUsers: 31,
    totalMessages: 2156,
    lastActivity: "1 minute ago",
    isActive: true,
    tags: ["dating", "friendship", "communication"],
  },
  {
    id: "work-burnout",
    name: "Work Burnout Recovery",
    description: "Dealing with workplace stress and finding work-life balance",
    category: "work",
    activeUsers: 15,
    totalMessages: 634,
    lastActivity: "8 minutes ago",
    isActive: true,
    tags: ["burnout", "workplace", "balance"],
  },
  {
    id: "family-dynamics",
    name: "Family Dynamics",
    description:
      "Discuss family relationships, parenting, and household challenges",
    category: "family",
    activeUsers: 22,
    totalMessages: 1893,
    lastActivity: "3 minutes ago",
    isActive: true,
    tags: ["family", "parenting", "relationships"],
  },
  {
    id: "grief-support",
    name: "Grief & Loss Support",
    description: "A compassionate space for those dealing with loss and grief",
    category: "grief",
    activeUsers: 12,
    totalMessages: 567,
    lastActivity: "12 minutes ago",
    isActive: true,
    tags: ["grief", "loss", "healing"],
  },
];

export const chatroomService = {
  /**
   * Get chatrooms data - tries API first, falls back to mock data
   * @param token - Authentication token (optional for public preview)
   * @param filters - Filters to apply
   * @param pagination - Pagination options
   */
  async getChatrooms(
    token?: string,
    filters?: {
      category?: string;
      topic?: string;
      search?: string;
    },
    pagination?: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }
  ): Promise<ChatroomData[]> {
    try {
      // Try API first
      const response = await chatAPI.getChatRooms(
        filters || {},
        {
          page: pagination?.page || 1,
          limit: pagination?.limit || 12,
          sortBy: pagination?.sortBy || "statistics.lastActivityAt",
          sortOrder: pagination?.sortOrder || "desc",
        },
        token
      );

      // Transform API data to our standardized interface
      return response.data.chatRooms.map((room: ChatRoomType) => ({
        id: room._id,
        name: room.name,
        description: room.description,
        category: room.category,
        activeUsers: room.currentParticipants || 0,
        totalMessages: room.statistics?.totalMessages || 0,
        lastActivity: room.statistics?.lastActivityAt
          ? new Date(room.statistics.lastActivityAt).toLocaleString()
          : "Recently",
        isActive: room.isActive,
        tags: room.tags || [],
      }));
    } catch (error) {
      console.log("API call failed, using mock data:", error);

      // Apply basic filtering to mock data if needed
      let filteredData = [...mockChatrooms];

      if (filters?.category && filters.category !== "all") {
        filteredData = filteredData.filter(
          (room) => room.category === filters.category
        );
      }

      if (filters?.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredData = filteredData.filter(
          (room) =>
            room.name.toLowerCase().includes(searchTerm) ||
            room.description.toLowerCase().includes(searchTerm) ||
            room.tags.some((tag) => tag.toLowerCase().includes(searchTerm))
        );
      }

      return filteredData;
    }
  },

  /**
   * Get a specific chatroom by ID
   * @param roomId - Room ID
   * @param token - Authentication token (optional)
   */
  async getChatroom(
    roomId: string,
    token?: string
  ): Promise<ChatroomData | null> {
    try {
      // Try API first
      const response = await chatAPI.getChatRoom(roomId, token);
      const room = response.data.chatRoom;

      return {
        id: room._id,
        name: room.name,
        description: room.description,
        category: room.category,
        activeUsers: room.currentParticipants || 0,
        totalMessages: room.statistics?.totalMessages || 0,
        lastActivity: room.statistics?.lastActivityAt
          ? new Date(room.statistics.lastActivityAt).toLocaleString()
          : "Recently",
        isActive: room.isActive,
        tags: room.tags || [],
      };
    } catch (error) {
      console.log("API call failed, using mock data for room:", roomId);

      // Fallback to mock data
      return mockChatrooms.find((room) => room.id === roomId) || null;
    }
  },
};

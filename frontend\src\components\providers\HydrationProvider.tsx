"use client";

import { useEffect } from "react";
import { useAuthStore } from "@/store/authStore";

interface HydrationProviderProps {
  children: React.ReactNode;
}

export default function HydrationProvider({
  children,
}: HydrationProviderProps) {
  const hasHydrated = useAuthStore((state) => state.hasHydrated);
  const setHydrated = useAuthStore((state) => state.setHydrated);

  useEffect(() => {
    // Mark the store as hydrated after client-side rendering
    setHydrated();
  }, [setHydrated]);

  if (!hasHydrated) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return <>{children}</>;
}

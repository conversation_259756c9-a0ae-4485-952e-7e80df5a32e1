import mongoose, { Document, Schema } from "mongoose";

export interface IChatRoom extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  topic: string;
  category: string;
  isActive: boolean;
  isModerated: boolean;
  maxParticipants: number;
  currentParticipants: number;
  moderators: mongoose.Types.ObjectId[];
  participants: {
    userId?: mongoose.Types.ObjectId; // undefined for anonymous users
    anonymousId?: string; // for guest users
    joinedAt: Date;
    lastSeen: Date;
    isOnline: boolean;
    role: "participant" | "moderator" | "readonly";
    displayName: string;
    isMuted: boolean;
    mutedUntil?: Date;
  }[];
  settings: {
    allowAnonymous: boolean;
    requireApproval: boolean;
    allowFileSharing: boolean;
    messageRetentionDays: number;
    slowModeDelay: number; // seconds between messages
    profanityFilter: boolean;
  };
  rules: string[];
  tags: string[];
  statistics: {
    totalMessages: number;
    totalParticipants: number;
    averageSessionDuration: number; // in minutes
    peakParticipants: number;
    lastActivityAt: Date;
  };
  schedule: {
    isScheduled: boolean;
    startTime?: Date;
    endTime?: Date;
    recurringPattern?: "daily" | "weekly" | "monthly";
    timezone: string;
  };
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const chatRoomSchema = new Schema<IChatRoom>(
  {
    name: {
      type: String,
      required: [true, "Chat room name is required"],
      trim: true,
      maxlength: [100, "Chat room name cannot exceed 100 characters"],
    },
    description: {
      type: String,
      required: [true, "Chat room description is required"],
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    topic: {
      type: String,
      required: true,
      enum: [
        "general-support",
        "anxiety-depression",
        "relationships",
        "family-issues",
        "work-stress",
        "student-life",
        "grief-loss",
        "addiction-recovery",
        "self-care",
        "mental-health-awareness",
        "crisis-support",
        "lgbtq-support",
        "parenting",
        "chronic-illness",
        "eating-disorders",
      ],
    },
    category: {
      type: String,
      required: true,
      enum: [
        "support-groups",
        "educational",
        "peer-chat",
        "crisis-support",
        "special-topics",
      ],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isModerated: {
      type: Boolean,
      default: true,
    },
    maxParticipants: {
      type: Number,
      default: 50,
      min: [2, "Chat room must allow at least 2 participants"],
      max: [100, "Chat room cannot exceed 100 participants"],
    },
    currentParticipants: {
      type: Number,
      default: 0,
      min: 0,
    },
    moderators: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    participants: [
      {
        userId: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
        anonymousId: String,
        joinedAt: {
          type: Date,
          default: Date.now,
        },
        lastSeen: {
          type: Date,
          default: Date.now,
        },
        isOnline: {
          type: Boolean,
          default: true,
        },
        role: {
          type: String,
          enum: ["participant", "moderator", "readonly"],
          default: "participant",
        },
        displayName: {
          type: String,
          required: true,
          trim: true,
          maxlength: [50, "Display name cannot exceed 50 characters"],
        },
        isMuted: {
          type: Boolean,
          default: false,
        },
        mutedUntil: Date,
      },
    ],
    settings: {
      allowAnonymous: {
        type: Boolean,
        default: true,
      },
      requireApproval: {
        type: Boolean,
        default: false,
      },
      allowFileSharing: {
        type: Boolean,
        default: false,
      },
      messageRetentionDays: {
        type: Number,
        default: 30,
        min: 1,
        max: 365,
      },
      slowModeDelay: {
        type: Number,
        default: 0,
        min: 0,
        max: 300, // 5 minutes max
      },
      profanityFilter: {
        type: Boolean,
        default: true,
      },
    },
    rules: [
      {
        type: String,
        maxlength: [200, "Each rule cannot exceed 200 characters"],
      },
    ],
    tags: [
      {
        type: String,
        lowercase: true,
        trim: true,
      },
    ],
    statistics: {
      totalMessages: {
        type: Number,
        default: 0,
      },
      totalParticipants: {
        type: Number,
        default: 0,
      },
      averageSessionDuration: {
        type: Number,
        default: 0,
      },
      peakParticipants: {
        type: Number,
        default: 0,
      },
      lastActivityAt: {
        type: Date,
        default: Date.now,
      },
    },
    schedule: {
      isScheduled: {
        type: Boolean,
        default: false,
      },
      startTime: Date,
      endTime: Date,
      recurringPattern: {
        type: String,
        enum: ["daily", "weekly", "monthly"],
      },
      timezone: {
        type: String,
        default: "Africa/Lagos",
      },
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
chatRoomSchema.index({ topic: 1, isActive: 1 });
chatRoomSchema.index({ category: 1, isActive: 1 });
chatRoomSchema.index({ tags: 1 });
chatRoomSchema.index({ "statistics.lastActivityAt": -1 });
chatRoomSchema.index({ currentParticipants: -1 });
chatRoomSchema.index({ createdAt: -1 });

// Compound indexes
chatRoomSchema.index({ isActive: 1, topic: 1 });
chatRoomSchema.index({ isActive: 1, currentParticipants: -1 });

// Virtual to populate creator details
chatRoomSchema.virtual("creator", {
  ref: "User",
  localField: "createdBy",
  foreignField: "_id",
  justOne: true,
});

// Method to add participant
chatRoomSchema.methods.addParticipant = function (participantData: any) {
  if (this.currentParticipants >= this.maxParticipants) {
    throw new Error("Chat room is full");
  }

  this.participants.push(participantData);
  this.currentParticipants += 1;
  this.statistics.totalParticipants += 1;

  if (this.currentParticipants > this.statistics.peakParticipants) {
    this.statistics.peakParticipants = this.currentParticipants;
  }
};

// Method to remove participant
chatRoomSchema.methods.removeParticipant = function (participantId: string) {
  const participantIndex = this.participants.findIndex(
    (p: any) =>
      p.userId?.toString() === participantId || p.anonymousId === participantId
  );

  if (participantIndex > -1) {
    this.participants.splice(participantIndex, 1);
    this.currentParticipants = Math.max(0, this.currentParticipants - 1);
  }
};

export const ChatRoom = mongoose.model<IChatRoom>("ChatRoom", chatRoomSchema);

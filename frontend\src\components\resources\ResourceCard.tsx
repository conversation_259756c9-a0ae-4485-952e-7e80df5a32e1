"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Resource, RESOURCE_TYPES, DIFFICULTY_LEVELS } from "@/types/resources";
import { useAuthStore } from "@/store/authStore";
import { resourcesAPI } from "@/lib/resources";
import SignupPromptModal from "@/components/modals/SignupPromptModal";

interface ResourceCardProps {
  resource: Resource;
  onBookmark?: (resourceId: string, isBookmarked: boolean) => void;
  onLike?: (resourceId: string, isLiked: boolean) => void;
}

export default function ResourceCard({
  resource,
  onBookmark,
  onLike,
}: ResourceCardProps) {
  const { user, tokens, isAuthenticated, isGuest, guestToken } = useAuthStore();

  console.log("🎯 ResourceCard rendering for:", resource.title, {
    isAuthenticated,
    isGuest,
    hasAccessToken: !!tokens?.accessToken,
    hasGuestToken: !!guestToken,
  });
  const [isBookmarked, setIsBookmarked] = useState(
    user && isAuthenticated && !isGuest
      ? resource.bookmarkedBy.includes(user._id)
      : false
  );
  const [isLiked, setIsLiked] = useState(false); // Likes are tracked in interactions, not persistently displayed
  const [loading, setLoading] = useState(false);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [modalFeature, setModalFeature] = useState<"bookmark" | "like">(
    "bookmark"
  );

  const token = tokens?.accessToken;
  const typeInfo = RESOURCE_TYPES.find((t) => t.value === resource.type);
  const difficultyInfo = DIFFICULTY_LEVELS.find(
    (d) => d.value === resource.difficulty
  );

  // Update bookmark state when user authentication changes
  useEffect(() => {
    setIsBookmarked(
      user && isAuthenticated && !isGuest
        ? resource.bookmarkedBy.includes(user._id)
        : false
    );
  }, [user, isAuthenticated, isGuest, resource.bookmarkedBy]);

  const handleBookmark = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log("🔖 Bookmark clicked - Auth state:", {
      isAuthenticated,
      isGuest,
      hasToken: !!token,
      user: user?.email || "no user",
    });

    if (!isAuthenticated || isGuest || !token) {
      console.log("🚫 Showing signup modal for bookmark");
      setModalFeature("bookmark");
      setShowSignupModal(true);
      return;
    }

    try {
      setLoading(true);
      if (isBookmarked) {
        await resourcesAPI.unbookmarkResource(resource._id, token);
        setIsBookmarked(false);
        onBookmark?.(resource._id, false);
      } else {
        await resourcesAPI.bookmarkResource(resource._id, token);
        setIsBookmarked(true);
        onBookmark?.(resource._id, true);
      }
    } catch (error) {
      console.error("Bookmark error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log("❤️ Like clicked - Auth state:", {
      isAuthenticated,
      isGuest,
      hasToken: !!token,
      user: user?.email || "no user",
    });

    if (!isAuthenticated || isGuest || !token) {
      console.log("🚫 Showing signup modal for like");
      setModalFeature("like");
      setShowSignupModal(true);
      return;
    }

    try {
      setLoading(true);
      // Show immediate feedback
      setIsLiked(true);

      await resourcesAPI.likeResource(resource._id, token);
      onLike?.(resource._id, true);

      // Reset like state after a brief moment since we can't track persistent like state
      setTimeout(() => {
        setIsLiked(false);
      }, 1000);
    } catch (error) {
      console.error("Like error:", error);
      // Reset on error
      setIsLiked(false);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = () => {
    if (resource.type === "article" && resource.estimatedReadTime) {
      return `${resource.estimatedReadTime} min read`;
    }
    if (
      (resource.type === "video" || resource.type === "audio") &&
      resource.estimatedDuration
    ) {
      return `${resource.estimatedDuration} min`;
    }
    return null;
  };

  const formatRating = (rating: number) => {
    return "★".repeat(Math.floor(rating)) + "☆".repeat(5 - Math.floor(rating));
  };

  return (
    <>
      <Link
        href={`/resources/${resource.seo.slug}`}
        className="group bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden"
      >
        {/* Thumbnail */}
        <div className="relative h-48 bg-gray-100">
          {resource.media?.thumbnailUrl ? (
            <img
              src={resource.media?.thumbnailUrl}
              alt={resource.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-100 to-pink-100">
              <div className="text-4xl">{typeInfo?.icon || "📄"}</div>
            </div>
          )}

          {/* Premium Badge */}
          {resource.isPremium && (
            <div className="absolute top-3 left-3">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <svg
                  className="w-3 h-3 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                Premium
              </span>
            </div>
          )}

          {/* Featured Badge */}
          {resource.isFeatured && (
            <div className="absolute top-3 right-3">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Featured
              </span>
            </div>
          )}

          {/* Type Badge */}
          <div className="absolute bottom-3 left-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800">
              {typeInfo?.icon} {typeInfo?.label}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors line-clamp-2 mb-2">
            {resource.title}
          </h3>

          {/* Description */}
          <p className="text-gray-600 text-sm line-clamp-3 mb-3">
            {resource.description}
          </p>

          {/* Meta Information */}
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            <div className="flex items-center space-x-3">
              {/* Difficulty */}
              {difficultyInfo && (
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full font-medium ${
                    difficultyInfo.color === "green"
                      ? "bg-green-100 text-green-800"
                      : difficultyInfo.color === "yellow"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {difficultyInfo.label}
                </span>
              )}

              {/* Duration */}
              {formatDuration() && (
                <span className="flex items-center">
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  {formatDuration()}
                </span>
              )}
            </div>

            {/* Rating */}
            {resource.statistics.totalRatings > 0 && (
              <div className="flex items-center">
                <span className="text-yellow-400 mr-1">
                  {formatRating(resource.statistics.averageRating)}
                </span>
                <span>({resource.statistics.totalRatings})</span>
              </div>
            )}
          </div>

          {/* Tags */}
          {resource.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {resource.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                >
                  #{tag}
                </span>
              ))}
              {resource.tags.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{resource.tags.length - 3} more
                </span>
              )}
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between">
            {/* Author */}
            <div className="flex items-center space-x-2">
              {resource.author.profilePicture ? (
                <img
                  src={resource.author.profilePicture}
                  alt={resource.author.name}
                  className="w-6 h-6 rounded-full"
                />
              ) : (
                <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-600">
                    {resource.author.name.charAt(0)}
                  </span>
                </div>
              )}
              <div>
                <p className="text-xs font-medium text-gray-900">
                  {resource.author.name}
                </p>
                {resource.author.credentials && (
                  <p className="text-xs text-gray-500">
                    {resource.author.credentials}
                  </p>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              {/* Views */}
              <span className="flex items-center text-xs text-gray-500">
                <svg
                  className="w-3 h-3 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                {resource.statistics.views}
              </span>

              {/* Like Button */}
              <button
                onClick={handleLike}
                disabled={loading}
                className={`p-1 rounded-full transition-colors ${
                  isLiked
                    ? "text-red-500 hover:text-red-600"
                    : "text-gray-400 hover:text-red-500"
                } disabled:opacity-50`}
                title={
                  isAuthenticated && !isGuest
                    ? "Like"
                    : "Sign up to like this resource"
                }
              >
                <svg
                  className="w-4 h-4"
                  fill={isLiked ? "currentColor" : "none"}
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </button>

              {/* Bookmark Button */}
              <button
                onClick={handleBookmark}
                disabled={loading}
                className={`p-1 rounded-full transition-colors ${
                  isBookmarked
                    ? "text-purple-500 hover:text-purple-600"
                    : "text-gray-400 hover:text-purple-500"
                } disabled:opacity-50`}
                title={
                  isAuthenticated && !isGuest
                    ? "Bookmark"
                    : "Sign up to bookmark this resource"
                }
              >
                <svg
                  className="w-4 h-4"
                  fill={isBookmarked ? "currentColor" : "none"}
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </Link>

      {/* Signup Prompt Modal */}
      <SignupPromptModal
        isOpen={showSignupModal}
        onClose={() => setShowSignupModal(false)}
        feature={modalFeature}
      />
    </>
  );
}

const http = require("http");

const req = http.request("http://localhost:5000/api/counselors", (res) => {
  let data = "";
  res.on("data", (chunk) => (data += chunk));
  res.on("end", () => {
    try {
      const json = JSON.parse(data);
      console.log("✅ API Response:");
      console.log("Status:", res.statusCode);
      console.log("Counselors found:", json.data.counselors.length);
      console.log("Pagination:", json.data.pagination);
      console.log("\n👩‍⚕️ Counselors:");
      json.data.counselors.forEach((c, i) => {
        console.log(
          `${i + 1}. ${c.firstName || "Unknown"} ${c.lastName || "Name"}`
        );
        console.log(`   Email: ${c.email || "No email"}`);
        console.log(
          `   Bio: ${c.bio ? c.bio.substring(0, 80) + "..." : "No bio"}`
        );
        console.log(
          `   Specializations: ${
            c.specializations ? c.specializations.join(", ") : "None"
          }`
        );
        console.log(
          `   Experience: ${
            c.experience ? c.experience.years + " years" : "Not specified"
          }`
        );
        console.log(
          `   Rate: ${
            c.pricing
              ? c.pricing.currency + " " + c.pricing.ratePerMinute + "/min"
              : "Not set"
          }`
        );
        console.log("");
      });
    } catch (error) {
      console.error("❌ Error parsing response:", error);
      console.log("Raw response:", data);
    }
  });
});

req.on("error", (error) => {
  console.error("❌ Request error:", error);
});

req.end();

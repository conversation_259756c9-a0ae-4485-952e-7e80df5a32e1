import { IUser } from "../models/User";

// Extend Express Request interface to include user properties
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      userId?: string;
      userRole?: string;
    }
  }
}

// Override Passport's User interface to match our IUser
declare module "passport" {
  interface User extends IUser {}
}

// Extend Express Response interface for custom methods
declare module "express-serve-static-core" {
  interface Request {
    user?: IUser;
    userId?: string;
    userRole?: string;
  }
}

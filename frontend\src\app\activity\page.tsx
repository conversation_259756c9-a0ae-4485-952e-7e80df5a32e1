"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import Header from '@/components/layout/Header';
import Link from 'next/link';

interface ActivityItem {
  id: string;
  type: 'session' | 'chat' | 'resource' | 'booking' | 'profile';
  title: string;
  description: string;
  timestamp: string;
  status?: 'completed' | 'pending' | 'cancelled';
  metadata?: {
    counselorName?: string;
    sessionType?: string;
    resourceTitle?: string;
    chatroomName?: string;
  };
}

export default function ActivityPage() {
  const router = useRouter();
  const { user, isAuthenticated, isGuest, checkAuth, isLoading } = useAuthStore();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'sessions' | 'chats' | 'resources'>('all');

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push('/auth/login');
      return;
    }

    if (isAuthenticated || isGuest) {
      loadActivityData();
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  const loadActivityData = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockActivities: ActivityItem[] = [
        {
          id: '1',
          type: 'session',
          title: 'Therapy Session with Dr. Sarah Johnson',
          description: 'Completed 50-minute video session',
          timestamp: '2024-01-15T14:00:00Z',
          status: 'completed',
          metadata: {
            counselorName: 'Dr. Sarah Johnson',
            sessionType: 'video'
          }
        },
        {
          id: '2',
          type: 'chat',
          title: 'Joined Anxiety Support Group',
          description: 'Participated in group discussion',
          timestamp: '2024-01-14T19:30:00Z',
          status: 'completed',
          metadata: {
            chatroomName: 'Anxiety Support Group'
          }
        },
        {
          id: '3',
          type: 'resource',
          title: 'Read: Managing Stress in Daily Life',
          description: 'Completed reading mental health resource',
          timestamp: '2024-01-13T10:15:00Z',
          status: 'completed',
          metadata: {
            resourceTitle: 'Managing Stress in Daily Life'
          }
        },
        {
          id: '4',
          type: 'booking',
          title: 'Booked Session with Michael Chen',
          description: 'Scheduled for January 18, 2024 at 10:00 AM',
          timestamp: '2024-01-12T16:45:00Z',
          status: 'pending',
          metadata: {
            counselorName: 'Michael Chen',
            sessionType: 'video'
          }
        },
        {
          id: '5',
          type: 'profile',
          title: 'Updated Profile Information',
          description: 'Modified personal preferences and goals',
          timestamp: '2024-01-10T12:30:00Z',
          status: 'completed'
        }
      ];

      setActivities(mockActivities);
    } catch (error) {
      console.error('Error loading activity data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredActivities = activities.filter(activity => {
    if (filter === 'all') return true;
    if (filter === 'sessions') return activity.type === 'session' || activity.type === 'booking';
    if (filter === 'chats') return activity.type === 'chat';
    if (filter === 'resources') return activity.type === 'resource';
    return true;
  });

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'session':
        return '🎥';
      case 'chat':
        return '💬';
      case 'resource':
        return '📚';
      case 'booking':
        return '📅';
      case 'profile':
        return '👤';
      default:
        return '📋';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Activity History</h1>
              <p className="text-gray-600 mt-2">
                Track your journey and see all your interactions on Theramea
              </p>
            </div>
            <Link
              href="/dashboard"
              className="text-purple-600 hover:text-purple-700 font-medium flex items-center"
            >
              ← Back to Dashboard
            </Link>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'All Activity' },
              { key: 'sessions', label: 'Sessions & Bookings' },
              { key: 'chats', label: 'Chatrooms' },
              { key: 'resources', label: 'Resources' }
            ].map((filterOption) => (
              <button
                key={filterOption.key}
                onClick={() => setFilter(filterOption.key as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filter === filterOption.key
                    ? 'bg-purple-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-purple-50 hover:text-purple-600 border border-gray-200'
                }`}
              >
                {filterOption.label}
              </button>
            ))}
          </div>
        </div>

        {/* Activity List */}
        <div className="space-y-4">
          {filteredActivities.length > 0 ? (
            filteredActivities.map((activity) => (
              <div
                key={activity.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="text-2xl">{getActivityIcon(activity.type)}</div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {activity.title}
                      </h3>
                      <p className="text-gray-600 mb-2">{activity.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{formatDate(activity.timestamp)}</span>
                        {activity.metadata?.counselorName && (
                          <span>• {activity.metadata.counselorName}</span>
                        )}
                        {activity.metadata?.chatroomName && (
                          <span>• {activity.metadata.chatroomName}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  {activity.status && (
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                      {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                    </span>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">📋</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No activity found</h3>
              <p className="text-gray-600 mb-4">
                {filter === 'all' 
                  ? 'Start using Theramea to see your activity here.'
                  : `No ${filter} activity found. Try a different filter.`
                }
              </p>
              <Link
                href="/dashboard"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Go to Dashboard
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

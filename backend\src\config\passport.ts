import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { Strategy as JwtStrategy, ExtractJwt } from "passport-jwt";
import { User } from "@/models/User";
import { logger } from "@/utils/logger";

// JWT Strategy
passport.use(
  new JwtStrategy(
    {
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_SECRET!,
      issuer: "theramea",
      audience: "theramea-users",
    },
    async (payload, done) => {
      try {
        const user = await User.findById(payload.userId);
        if (user && user.isActive) {
          return done(null, user);
        }
        return done(null, false);
      } catch (error) {
        logger.error("JWT strategy error:", error);
        return done(error, false);
      }
    }
  )
);

// Google OAuth Strategy
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL:
          process.env.GOOGLE_CALLBACK_URL || "/api/auth/google/callback",
      },
      async (accessToken, refreshToken, profile, done) => {
        try {
          // Check if user already exists with this Google ID
          let user = await User.findOne({ "socialAuth.google.id": profile.id });

          if (user) {
            // User exists, update their info if needed
            if (!user.isActive) {
              return done(null, false, { message: "Account is deactivated" });
            }
            return done(null, user);
          }

          // Check if user exists with the same email
          const email = profile.emails?.[0]?.value;
          if (email) {
            user = await User.findOne({ email: email.toLowerCase() });

            if (user) {
              // Link Google account to existing user
              user.socialAuth.google = {
                id: profile.id,
                email: email,
              };
              user.isEmailVerified = true; // Google emails are pre-verified
              await user.save();
              return done(null, user);
            }
          }

          // Create new user
          if (!email) {
            return done(null, false, {
              message: "No email provided by Google",
            });
          }

          const displayName = profile.displayName || "";
          const nameParts = displayName.split(" ");
          const firstName = nameParts[0] || "User";
          const lastName = nameParts.slice(1).join(" ") || "";

          // Generate unique username
          let username = email.split("@")[0].toLowerCase();
          let usernameExists = await User.findOne({ username });
          let counter = 1;

          while (usernameExists) {
            username = `${email.split("@")[0].toLowerCase()}${counter}`;
            usernameExists = await User.findOne({ username });
            counter++;
          }

          const newUser = new User({
            firstName,
            lastName,
            username,
            email: email.toLowerCase(),
            isEmailVerified: true,
            isActive: true,
            socialAuth: {
              google: {
                id: profile.id,
                email: email,
              },
            },
            profilePicture: profile.photos?.[0]?.value,
          });

          await newUser.save();
          logger.info(`New user created via Google OAuth: ${newUser.email}`);

          return done(null, newUser);
        } catch (error) {
          logger.error("Google OAuth strategy error:", error);
          return done(error, false);
        }
      }
    )
  );
} else {
  logger.warn("Google OAuth not configured - missing client ID or secret");
}

// Serialize user for session
passport.serializeUser((user: any, done) => {
  done(null, user._id);
});

// Deserialize user from session
passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (error) {
    logger.error("User deserialization error:", error);
    done(error, null);
  }
});

export default passport;

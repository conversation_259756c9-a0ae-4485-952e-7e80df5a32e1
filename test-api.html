<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Testing Counselors API</h1>
    <div id="result"></div>
    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            try {
                resultDiv.innerHTML = 'Loading...';
                console.log('Fetching from:', 'http://localhost:5000/api/counselors');
                
                const response = await fetch('http://localhost:5000/api/counselors');
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                resultDiv.innerHTML = `
                    <h2>Success!</h2>
                    <p>Found ${data.data.counselors.length} counselors</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testAPI();
    </script>
</body>
</html>

import { Router } from "express";
import { VideoController } from "@/controllers/videoController";
import {
  sessionValidations,
  paramValidations,
  videoValidations,
} from "@/utils/validation";
import { authenticate, requireCounselor } from "@/middleware/auth";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// Session management
router.post(
  "/:sessionId/start",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.startSession
);

router.put(
  "/:sessionId/end",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.endSession
);

router.put(
  "/:sessionId/no-show",
  authenticate,
  requireCounselor,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.markNoShow
);

// Video room management
router.post(
  "/:sessionId/room",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.createRoom
);

router.get(
  "/:sessionId/token",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.generateToken
);

// Recording management
router.post(
  "/:sessionId/recording/start",
  authenticate,
  requireCounselor,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.startRecording
);

router.post(
  "/:sessionId/recording/stop",
  authenticate,
  requireCounselor,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.stopRecording
);

router.get(
  "/:sessionId/recordings",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.getRecordings
);

// Analytics and monitoring
router.get(
  "/:sessionId/analytics",
  authenticate,
  paramValidations.objectId("sessionId"),
  validateRequest,
  VideoController.getRoomAnalytics
);

// Feedback and statistics
router.post(
  "/:sessionId/feedback",
  authenticate,
  paramValidations.objectId("sessionId"),
  sessionValidations.feedback,
  validateRequest,
  VideoController.submitFeedback
);

router.get("/stats", authenticate, VideoController.getSessionStats);

router.get("/upcoming", authenticate, VideoController.getUpcomingSessions);

// Webhook endpoint
router.post(
  "/webhook/daily",
  videoValidations.webhook,
  validateRequest,
  VideoController.handleDailyWebhook
);

export default router;

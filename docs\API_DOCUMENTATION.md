# Theramea API Documentation

## Overview

The Theramea API is a RESTful API that provides endpoints for managing users, counselors, sessions, payments, and more. The API uses JSON for request and response bodies and includes comprehensive authentication and authorization.

## Base URL

```
Production: https://api.theramea.com
Development: http://localhost:5000
```

## Authentication

The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Guest Access

Some endpoints support guest access using a guest token:

```
Authorization: Guest <guest-token>
```

## Response Format

All API responses follow this structure:

```json
{
  "success": true,
  "message": "Success message",
  "data": {
    // Response data
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

Error responses:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

## Endpoints

### Authentication

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "role": "client"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "_id": "user_id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "client"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token"
    }
  }
}
```

#### POST /api/auth/login
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

#### POST /api/auth/guest
Get guest access token for anonymous users.

**Response:**
```json
{
  "success": true,
  "data": {
    "guestToken": "guest_token",
    "guestId": "guest_id"
  }
}
```

#### POST /api/auth/refresh
Refresh access token using refresh token.

#### POST /api/auth/logout
Logout user and invalidate tokens.

### Users

#### GET /api/users/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "_id": "user_id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "client",
      "profile": {
        "bio": "User bio",
        "profilePicture": "image_url"
      }
    }
  }
}
```

#### PUT /api/users/profile
Update user profile.

#### DELETE /api/users/account
Delete user account.

### Counselors

#### GET /api/counselors
Get list of counselors with filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `specialization` (string): Filter by specialization
- `rating` (number): Minimum rating filter
- `availability` (boolean): Filter by availability
- `search` (string): Search by name or specialization

**Response:**
```json
{
  "success": true,
  "data": {
    "counselors": [
      {
        "_id": "counselor_id",
        "user": {
          "firstName": "Dr. Jane",
          "lastName": "Smith",
          "email": "<EMAIL>"
        },
        "specializations": ["anxiety", "depression"],
        "experience": {
          "years": 5,
          "description": "Experienced therapist"
        },
        "pricing": {
          "ratePerMinute": 100,
          "currency": "NGN"
        },
        "statistics": {
          "averageRating": 4.8,
          "totalReviews": 150,
          "totalSessions": 500
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

#### GET /api/counselors/:id
Get specific counselor details.

#### GET /api/counselors/:id/availability
Get counselor availability for booking.

**Query Parameters:**
- `date` (string): Date in YYYY-MM-DD format
- `duration` (number): Session duration in minutes

### Sessions

#### POST /api/sessions
Create a new counseling session booking.

**Request Body:**
```json
{
  "counselorId": "counselor_id",
  "scheduledAt": "2024-01-15T10:00:00Z",
  "duration": 60,
  "sessionType": "individual",
  "notes": "Session notes",
  "isUrgent": false
}
```

#### GET /api/sessions
Get user's sessions.

#### GET /api/sessions/:id
Get specific session details.

#### PUT /api/sessions/:id/status
Update session status (counselor only).

#### POST /api/sessions/:id/join
Join a session (generates video call link).

### Payments

#### POST /api/payments/initialize
Initialize payment for a session.

**Request Body:**
```json
{
  "sessionId": "session_id",
  "paymentMethod": "card",
  "currency": "NGN"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "paymentUrl": "https://checkout.paystack.com/...",
    "reference": "payment_reference",
    "amount": 6000
  }
}
```

#### POST /api/payments/verify
Verify payment status.

#### GET /api/payments/history
Get payment history.

#### POST /api/payments/refund
Process payment refund (admin only).

### Chat

#### GET /api/chat/rooms
Get available chat rooms.

#### POST /api/chat/rooms
Create new chat room (counselor only).

#### GET /api/chat/rooms/:id
Get chat room details.

#### POST /api/chat/rooms/:id/join
Join a chat room.

#### GET /api/chat/rooms/:id/messages
Get chat room messages.

#### POST /api/chat/rooms/:id/messages
Send message to chat room.

### Admin

#### GET /api/admin/dashboard
Get admin dashboard metrics.

#### GET /api/admin/users
Get users list with admin controls.

#### GET /api/admin/counselors
Get counselors list for verification.

#### POST /api/admin/counselors/:id/approve
Approve counselor application.

#### POST /api/admin/counselors/:id/reject
Reject counselor application.

#### GET /api/admin/reports
Get user reports for moderation.

#### PUT /api/admin/reports/:id
Update report status.

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `CONFLICT` | Resource conflict |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `PAYMENT_FAILED` | Payment processing failed |
| `SESSION_EXPIRED` | Session has expired |
| `INVALID_TOKEN` | Invalid or expired token |

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **General endpoints**: 100 requests per 15 minutes
- **Authentication endpoints**: 5 requests per 15 minutes
- **Payment endpoints**: 10 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Webhooks

### Payment Webhooks

Paystack webhooks are received at `/api/webhooks/paystack`:

```json
{
  "event": "charge.success",
  "data": {
    "reference": "payment_reference",
    "amount": 6000,
    "status": "success"
  }
}
```

### Video Call Webhooks

Daily.co webhooks for session events:

```json
{
  "type": "room.exp",
  "data": {
    "room": "session_room_id",
    "participants": []
  }
}
```

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @theramea/api-client
```

### Usage Example
```typescript
import { Theramea } from '@theramea/api-client';

const client = new Theramea({
  apiKey: 'your-api-key',
  environment: 'production'
});

const counselors = await client.counselors.list({
  specialization: 'anxiety',
  page: 1,
  limit: 10
});
```

## Support

For API support, contact:
- Email: <EMAIL>
- Documentation: https://docs.theramea.com
- Status Page: https://status.theramea.com

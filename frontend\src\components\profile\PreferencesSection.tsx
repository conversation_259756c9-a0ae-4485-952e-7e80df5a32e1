'use client';

import { useState } from 'react';
import { User } from '@/types/auth';

interface PreferencesSectionProps {
  user: User;
  type?: 'all' | 'notifications' | 'privacy';
}

export default function PreferencesSection({ user, type = 'all' }: PreferencesSectionProps) {
  const [preferences, setPreferences] = useState(user.preferences);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleNotificationChange = (key: keyof typeof preferences.notifications) => {
    setPreferences(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: !prev.notifications[key],
      },
    }));
  };

  const handlePrivacyChange = (key: keyof typeof preferences.privacy) => {
    setPreferences(prev => ({
      ...prev,
      privacy: {
        ...prev.privacy,
        [key]: !prev.privacy[key],
      },
    }));
  };

  const savePreferences = async (section?: 'notifications' | 'privacy') => {
    setIsSubmitting(true);
    setMessage(null);

    try {
      const endpoint = section 
        ? `${process.env.NEXT_PUBLIC_API_URL}/users/preferences/${section}`
        : `${process.env.NEXT_PUBLIC_API_URL}/users/preferences`;
      
      const body = section 
        ? preferences[section]
        : preferences;

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Preferences updated successfully!' });
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.message || 'Failed to update preferences' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An error occurred while updating your preferences' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetPreferences = async () => {
    if (!confirm('Are you sure you want to reset all preferences to default?')) {
      return;
    }

    setIsSubmitting(true);
    setMessage(null);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/preferences/reset`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(data.data.preferences);
        setMessage({ type: 'success', text: 'Preferences reset to default successfully!' });
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.message || 'Failed to reset preferences' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An error occurred while resetting your preferences' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {message && (
        <div className={`rounded-md p-4 ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-700 border border-green-200' 
            : 'bg-red-50 text-red-700 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {(type === 'all' || type === 'notifications') && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Notification Preferences
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Email Notifications</label>
                <p className="text-sm text-gray-500">Receive notifications via email</p>
              </div>
              <button
                type="button"
                onClick={() => handleNotificationChange('email')}
                className={`${
                  preferences.notifications.email ? 'bg-purple-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    preferences.notifications.email ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Push Notifications</label>
                <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
              </div>
              <button
                type="button"
                onClick={() => handleNotificationChange('push')}
                className={`${
                  preferences.notifications.push ? 'bg-purple-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    preferences.notifications.push ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Session Reminders</label>
                <p className="text-sm text-gray-500">Get reminded about upcoming counseling sessions</p>
              </div>
              <button
                type="button"
                onClick={() => handleNotificationChange('sessionReminders')}
                className={`${
                  preferences.notifications.sessionReminders ? 'bg-purple-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    preferences.notifications.sessionReminders ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Chat Messages</label>
                <p className="text-sm text-gray-500">Get notified about new chat messages</p>
              </div>
              <button
                type="button"
                onClick={() => handleNotificationChange('chatMessages')}
                className={`${
                  preferences.notifications.chatMessages ? 'bg-purple-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    preferences.notifications.chatMessages ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>
          </div>

          {type === 'notifications' && (
            <div className="mt-6">
              <button
                onClick={() => savePreferences('notifications')}
                disabled={isSubmitting}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Saving...' : 'Save Notification Settings'}
              </button>
            </div>
          )}
        </div>
      )}

      {(type === 'all' || type === 'privacy') && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Privacy Settings
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Show Online Status</label>
                <p className="text-sm text-gray-500">Let others see when you're online</p>
              </div>
              <button
                type="button"
                onClick={() => handlePrivacyChange('showOnlineStatus')}
                className={`${
                  preferences.privacy.showOnlineStatus ? 'bg-purple-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    preferences.privacy.showOnlineStatus ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Allow Direct Messages</label>
                <p className="text-sm text-gray-500">Allow other users to send you direct messages</p>
              </div>
              <button
                type="button"
                onClick={() => handlePrivacyChange('allowDirectMessages')}
                className={`${
                  preferences.privacy.allowDirectMessages ? 'bg-purple-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    preferences.privacy.allowDirectMessages ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
            </div>
          </div>

          {type === 'privacy' && (
            <div className="mt-6">
              <button
                onClick={() => savePreferences('privacy')}
                disabled={isSubmitting}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Saving...' : 'Save Privacy Settings'}
              </button>
            </div>
          )}
        </div>
      )}

      {type === 'all' && (
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={resetPreferences}
            disabled={isSubmitting}
            className="text-red-600 hover:text-red-700 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Reset to Default
          </button>
          <button
            onClick={() => savePreferences()}
            disabled={isSubmitting}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Saving...' : 'Save All Preferences'}
          </button>
        </div>
      )}
    </div>
  );
}

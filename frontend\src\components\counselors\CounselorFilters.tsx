"use client";

import { <PERSON><PERSON><PERSON>ounselor } from "@/types/counselor";

interface CounselorFiltersProps {
  counselors: ApiCounselor[];
  selectedSpecialization: string;
  setSelectedSpecialization: (spec: string) => void;
  priceRange: number[];
  setPriceRange: (range: number[]) => void;
  onClearFilters: () => void;
}

export default function CounselorFilters({
  counselors,
  selectedSpecialization,
  setSelectedSpecialization,
  priceRange,
  setPriceRange,
  onClearFilters,
}: CounselorFiltersProps) {
  // Get unique specializations from counselors data
  const getAvailableSpecializations = () => {
    const allSpecs = counselors.flatMap(
      (counselor) => counselor.specializations
    );
    const uniqueSpecs = [...new Set(allSpecs)].sort();
    return ["all", ...uniqueSpecs];
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 lg:sticky lg:top-8">
      <h3 className="font-semibold text-gray-900 mb-4">Filters</h3>

      {/* Specialization Filter */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Specialization
        </label>
        <select
          value={selectedSpecialization}
          onChange={(e) => setSelectedSpecialization(e.target.value)}
          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        >
          {getAvailableSpecializations().map((spec) => (
            <option key={spec} value={spec}>
              {spec === "all"
                ? "All Specializations"
                : spec.charAt(0).toUpperCase() +
                  spec.slice(1).replace(/\s+/g, " ")}
            </option>
          ))}
        </select>
      </div>

      {/* Price Range */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Hourly Rate: ₦{priceRange[0].toLocaleString()} - ₦
          {priceRange[1].toLocaleString()}
        </label>
        <div className="space-y-2">
          <input
            type="range"
            min="3000"
            max="15000"
            value={priceRange[1]}
            onChange={(e) =>
              setPriceRange([priceRange[0], parseInt(e.target.value)])
            }
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>₦3,000</span>
            <span>₦15,000+</span>
          </div>
        </div>
      </div>

      {/* Availability */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Availability
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <span className="ml-2 text-sm text-gray-700">Available today</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <span className="ml-2 text-sm text-gray-700">Evening hours</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <span className="ml-2 text-sm text-gray-700">
              Weekend availability
            </span>
          </label>
        </div>
      </div>

      {/* Languages */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Languages
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              defaultChecked
            />
            <span className="ml-2 text-sm text-gray-700">English</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <span className="ml-2 text-sm text-gray-700">Spanish</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <span className="ml-2 text-sm text-gray-700">French</span>
          </label>
        </div>
      </div>

      {/* Clear Filters Button */}
      <button
        onClick={onClearFilters}
        className="w-full text-purple-600 hover:text-purple-700 font-medium text-sm py-2 border border-purple-200 rounded-lg hover:bg-purple-50 transition-colors"
      >
        Clear all filters
      </button>
    </div>
  );
}

// Booking API functions
import {
  Session,
  BookingRequest,
  BookingResponse,
  AvailabilitySlot,
  BookingFilters,
  PaginatedBookings,
  SessionFeedback,
} from "@/types/booking";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

class BookingAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  // Create a new booking
  async createBooking(
    bookingData: BookingRequest,
    token: string
  ): Promise<BookingResponse> {
    const response = await fetch(`${API_BASE_URL}/sessions`, {
      method: "POST",
      headers: this.getHeaders(token),
      body: JSON.stringify(bookingData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create booking");
    }

    return response.json();
  }

  // Get user's bookings
  async getUserBookings(
    filters: BookingFilters = {},
    page: number = 1,
    limit: number = 10,
    token: string
  ): Promise<{
    success: boolean;
    data: PaginatedBookings;
  }> {
    const queryParams = new URLSearchParams();
    queryParams.append("page", page.toString());
    queryParams.append("limit", limit.toString());

    if (filters.status) queryParams.append("status", filters.status);
    if (filters.counselorId)
      queryParams.append("counselorId", filters.counselorId);
    if (filters.search) queryParams.append("search", filters.search);
    if (filters.dateRange) {
      queryParams.append("startDate", filters.dateRange.start);
      queryParams.append("endDate", filters.dateRange.end);
    }

    const response = await fetch(`${API_BASE_URL}/sessions?${queryParams}`, {
      method: "GET",
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch bookings");
    }

    return response.json();
  }

  // Get a specific booking
  async getBooking(
    bookingId: string,
    token?: string
  ): Promise<{
    success: boolean;
    data: { session: Session };
  }> {
    const response = await fetch(`${API_BASE_URL}/sessions/${bookingId}`, {
      method: "GET",
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch booking");
    }

    return response.json();
  }

  // Cancel a booking
  async cancelBooking(
    bookingId: string,
    reason: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/sessions/${bookingId}/cancel`,
      {
        method: "PUT",
        headers: this.getHeaders(token),
        body: JSON.stringify({ reason }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to cancel booking");
    }

    return response.json();
  }

  // Reschedule a booking
  async rescheduleBooking(
    bookingId: string,
    newScheduledAt: string,
    reason?: string,
    token?: string
  ): Promise<{
    success: boolean;
    message: string;
    data: { session: Session };
  }> {
    const response = await fetch(
      `${API_BASE_URL}/sessions/${bookingId}/reschedule`,
      {
        method: "PUT",
        headers: this.getHeaders(token),
        body: JSON.stringify({ scheduledAt: newScheduledAt, reason }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to reschedule booking");
    }

    return response.json();
  }

  // Submit session feedback
  async submitFeedback(
    sessionId: string,
    feedback: SessionFeedback,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/sessions/${sessionId}/feedback`,
      {
        method: "POST",
        headers: this.getHeaders(token),
        body: JSON.stringify(feedback),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to submit feedback");
    }

    return response.json();
  }

  // Get counselor availability
  async getCounselorAvailability(
    counselorId: string,
    startDate: string,
    endDate: string,
    timezone?: string,
    token?: string
  ): Promise<{
    success: boolean;
    data: { slots: AvailabilitySlot[] };
  }> {
    const queryParams = new URLSearchParams();
    queryParams.append("startDate", startDate);
    queryParams.append("endDate", endDate);
    if (timezone) queryParams.append("timezone", timezone);

    const response = await fetch(
      `${API_BASE_URL}/counselors/${counselorId}/availability?${queryParams}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch availability");
    }

    return response.json();
  }
}

export const bookingAPI = new BookingAPI();

import { Router } from "express";
import { UserController } from "@/controllers/userController";
import { PreferencesController } from "@/controllers/preferencesController";
import {
  userValidations,
  userUpdateValidations,
  paramValidations,
} from "@/utils/validation";
import {
  authenticate,
  requireAdmin,
  requireSuperAdmin,
} from "@/middleware/auth";
import { uploadProfilePicture, handleUploadError } from "@/middleware/upload";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// User profile routes
router.get("/profile", authenticate, UserController.getProfile);

router.put(
  "/profile",
  authenticate,
  userValidations.updateProfile,
  validateRequest,
  UserController.updateProfile
);

router.post(
  "/profile/picture",
  authenticate,
  uploadProfilePicture,
  handleUploadError,
  UserController.uploadProfilePicture
);

router.delete(
  "/profile/picture",
  authenticate,
  UserController.deleteProfilePicture
);

// User preferences routes
router.get("/preferences", authenticate, PreferencesController.getPreferences);

router.put(
  "/preferences",
  authenticate,
  PreferencesController.updateAllPreferences
);

router.put(
  "/preferences/notifications",
  authenticate,
  PreferencesController.updateNotificationPreferences
);

router.put(
  "/preferences/privacy",
  authenticate,
  PreferencesController.updatePrivacyPreferences
);

router.post(
  "/preferences/reset",
  authenticate,
  PreferencesController.resetPreferences
);

router.get(
  "/preferences/export",
  authenticate,
  PreferencesController.exportPreferences
);

router.post(
  "/preferences/import",
  authenticate,
  PreferencesController.importPreferences
);

// User app settings routes
router.get("/app-settings", authenticate, UserController.getAppSettings);

router.put("/app-settings", authenticate, UserController.updateAppSettings);

// Account management
router.post(
  "/deactivate",
  authenticate,
  userUpdateValidations.deactivate,
  validateRequest,
  UserController.deactivateAccount
);

router.delete(
  "/account/:userId?",
  authenticate,
  paramValidations.objectId("userId"),
  validateRequest,
  UserController.deleteAccount
);

// Public user info
router.get(
  "/:userId",
  paramValidations.objectId("userId"),
  validateRequest,
  UserController.getUserById
);

// Admin routes
router.get("/", authenticate, requireAdmin, UserController.getUsers);

router.get(
  "/stats/overview",
  authenticate,
  requireAdmin,
  UserController.getUserStats
);

router.put(
  "/:userId/reactivate",
  authenticate,
  requireAdmin,
  paramValidations.objectId("userId"),
  validateRequest,
  UserController.reactivateAccount
);

router.put(
  "/:userId/role",
  authenticate,
  requireAdmin,
  paramValidations.objectId("userId"),
  userUpdateValidations.role,
  validateRequest,
  UserController.updateUserRole
);

router.post(
  "/bulk-operations",
  authenticate,
  requireSuperAdmin,
  userUpdateValidations.bulkOperations,
  validateRequest,
  UserController.bulkUserOperations
);

export default router;

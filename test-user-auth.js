const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

async function testUserAuth() {
  const passwordsToTry = [
    "password123",
    "Password123",
    "securepassword123",
    "TestPassword123",
    "test123",
    "admin123",
  ];

  for (const password of passwordsToTry) {
    try {
      console.log(`\n=== Testing with password: ${password} ===`);

      // Test login with the user who owns the session
      const loginData = {
        email: "<EMAIL>",
        password: password,
      };

      console.log(`Attempting to login as: ${loginData.email}`);

      const loginResponse = await axios.post(
        `${API_BASE_URL}/auth/login`,
        loginData
      );

      if (loginResponse.data.success) {
        const authToken = loginResponse.data.data.tokens.accessToken;
        console.log("✅ Login successful!");
        console.log(
          `User: ${loginResponse.data.data.user.firstName} ${loginResponse.data.data.user.lastName}`
        );
        console.log(`User ID: ${loginResponse.data.data.user._id}`);
        console.log(`Auth Token: ${authToken.substring(0, 50)}...`);

        // Now test the payment initialization with this token
        console.log("\n=== Testing Payment Initialization ===");
        const sessionId = "689e8ac358caf361f78ce110";

        const paymentResponse = await axios.post(
          `${API_BASE_URL}/sessions/${sessionId}/payment/initialize`,
          {},
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        console.log("✅ Payment initialization successful!");
        console.log(
          "Payment data:",
          JSON.stringify(paymentResponse.data, null, 2)
        );
        return; // Success, exit function
      } else {
        console.log("❌ Login failed");
        console.log(loginResponse.data);
      }
    } catch (error) {
      if (
        error.response?.status === 401 &&
        error.response?.data?.error?.message?.includes(
          "Invalid email or password"
        )
      ) {
        console.log(`❌ Password '${password}' is incorrect`);
        continue; // Try next password
      }

      console.error("❌ Error:", error.response?.data || error.message);

      if (
        error.response?.status === 403 &&
        error.response?.data?.error?.message?.includes("Unauthorized to pay")
      ) {
        console.log(
          "\n✅ Authentication worked but payment authorization failed."
        );
        console.log(
          "This confirms the password is correct but there's still an authorization issue."
        );
        return;
      }
    }
  }

  console.log(
    "\n❌ None of the passwords worked. The user might have a different password."
  );
}

testUserAuth();

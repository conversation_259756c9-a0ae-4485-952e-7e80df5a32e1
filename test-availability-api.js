const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

async function testAvailabilityAPI() {
  try {
    console.log("Testing counselor availability API...\n");

    // First, get the list of counselors
    const counselorsResponse = await axios.get(`${API_BASE_URL}/counselors`);
    const counselors = counselorsResponse.data.data.counselors;

    console.log(`Found ${counselors.length} counselors`);

    if (counselors.length === 0) {
      console.log("No counselors found");
      return;
    }

    // Test availability for the first counselor
    const counselor = counselors[0];
    console.log(
      `\nTesting availability for counselor: ${
        counselor.basicInfo?.firstName || "Unknown"
      } ${counselor.basicInfo?.lastName || ""}`
    );
    console.log(`Counselor ID: ${counselor._id}`);

    // Get availability for the next 7 days
    const today = new Date();
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);

    const startDate = today.toISOString().split("T")[0];
    const endDate = nextWeek.toISOString().split("T")[0];

    console.log(`Checking availability from ${startDate} to ${endDate}`);

    const availabilityResponse = await axios.get(
      `${API_BASE_URL}/counselors/${counselor._id}/availability`,
      {
        params: {
          startDate,
          endDate,
          duration: 60,
          timezone: "Africa/Lagos",
        },
      }
    );

    const availability = availabilityResponse.data;
    console.log("\nAvailability Response:");
    console.log(JSON.stringify(availability, null, 2));

    if (availability.success && availability.data.availableSlots) {
      const slots = availability.data.availableSlots;
      console.log(`\nFound ${slots.length} days with availability:`);

      slots.forEach((day) => {
        console.log(
          `${day.date} (${day.dayOfWeek}): ${day.slots.length} slots`
        );
        day.slots.forEach((slot) => {
          console.log(
            `  ${slot.startTime} - ${slot.endTime} (Available: ${slot.available})`
          );
        });
      });
    } else {
      console.log("No availability data found");
    }
  } catch (error) {
    console.error(
      "❌ Error testing availability API:",
      error.response?.data || error.message
    );
  }
}

testAvailabilityAPI();

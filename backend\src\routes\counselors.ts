import { Router } from "express";
import { CounselorController } from "@/controllers/counselorController";
import {
  counselorValidations,
  counselorUpdateValidations,
  paramValidations,
} from "@/utils/validation";
import {
  authenticate,
  requireCounselor,
  requireAdmin,
  optionalAuth,
} from "@/middleware/auth";
import {
  uploadVerificationDocuments,
  handleUploadError,
} from "@/middleware/upload";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// Public routes
router.get("/", optionalAuth, CounselorController.getCounselors);

router.get(
  "/:counselorId",
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.getCounselorProfile
);

router.get(
  "/:counselorId/availability",
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.getAvailableSlots
);

router.get(
  "/:counselorId/weekly-availability",
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.getWeeklyAvailability
);

// Like/Unlike counselor (authenticated users only)
router.post(
  "/:counselorId/like",
  authenticate,
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.likeCounselor
);

router.delete(
  "/:counselorId/like",
  authenticate,
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.unlikeCounselor
);

// Get user's liked counselors
router.get("/user/liked", authenticate, CounselorController.getLikedCounselors);

// Counselor registration and profile management
router.post(
  "/register",
  authenticate,
  counselorValidations.register,
  validateRequest,
  CounselorController.registerCounselor
);

router.get(
  "/me/profile",
  authenticate,
  requireCounselor,
  CounselorController.getMyCounselorProfile
);

router.put(
  "/me/profile",
  authenticate,
  requireCounselor,
  counselorUpdateValidations.profile,
  validateRequest,
  CounselorController.updateCounselorProfile
);

router.put(
  "/me/availability",
  authenticate,
  requireCounselor,
  counselorValidations.updateAvailability,
  validateRequest,
  CounselorController.updateAvailability
);

router.post(
  "/me/verification-documents",
  authenticate,
  requireCounselor,
  uploadVerificationDocuments,
  handleUploadError,
  CounselorController.uploadVerificationDocuments
);

router.get(
  "/me/stats",
  authenticate,
  requireCounselor,
  CounselorController.getCounselorStats
);

router.put(
  "/me/settings",
  authenticate,
  requireCounselor,
  counselorUpdateValidations.settings,
  validateRequest,
  CounselorController.updateSettings
);

router.get(
  "/me/detailed-stats",
  authenticate,
  requireCounselor,
  CounselorController.getDetailedStats
);

router.post(
  "/me/block-slots",
  authenticate,
  requireCounselor,
  counselorUpdateValidations.blockSlots,
  validateRequest,
  CounselorController.blockTimeSlots
);

router.post(
  "/me/unblock-slots",
  authenticate,
  requireCounselor,
  counselorUpdateValidations.blockSlots,
  validateRequest,
  CounselorController.unblockTimeSlots
);

// Admin routes
router.get(
  "/admin/pending",
  authenticate,
  requireAdmin,
  CounselorController.getPendingApplications
);

router.put(
  "/:counselorId/approve",
  authenticate,
  requireAdmin,
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.approveCounselor
);

router.put(
  "/:counselorId/reject",
  authenticate,
  requireAdmin,
  paramValidations.objectId("counselorId"),
  validateRequest,
  CounselorController.rejectCounselor
);

export default router;

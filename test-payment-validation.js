const axios = require("axios");

const BASE_URL = "http://localhost:5000/api";

// Test user credentials
const TEST_USER = {
  email: "<EMAIL>",
  password: "password123",
};

let authToken = null;

// Step 1: Authenticate and get a valid token
async function authenticateUser() {
  try {
    console.log("🔐 Authenticating user...");

    const response = await axios.post(
      `${BASE_URL}/auth/login`,
      {
        email: TEST_USER.email,
        password: TEST_USER.password,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.success && response.data.data.tokens) {
      authToken = response.data.data.tokens.accessToken;
      console.log("✅ Authentication successful!");
      console.log(
        `User: ${response.data.data.user.firstName} ${response.data.data.user.lastName}`
      );
      return true;
    }
  } catch (error) {
    console.error("❌ Authentication failed:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    } else {
      console.error("Error:", error.message);
    }
    return false;
  }
}

// Test payment initialization validation directly with the session ID from the original error
async function testPaymentInitializationDirectly() {
  const sessionId = "689e7d8b8d814e4aae4767af"; // From the original error

  console.log(
    `\n💳 Testing payment initialization validation for session: ${sessionId}`
  );

  const testCases = [
    {
      name: "Original issue - Empty request body",
      data: {},
      shouldSucceed: false,
      expectedErrors: ["amount", "currency", "paymentMethod"],
    },
    {
      name: "Valid payment data (Card - NGN)",
      data: {
        amount: 5000, // 50.00 NGN in kobo
        currency: "NGN",
        paymentMethod: "card",
      },
      shouldSucceed: true,
    },
    {
      name: "Valid payment data (Bank Transfer - USD)",
      data: {
        amount: 2500, // 25.00 USD in cents
        currency: "USD",
        paymentMethod: "bank_transfer",
      },
      shouldSucceed: true,
    },
    {
      name: "Valid payment data (USSD - NGN)",
      data: {
        amount: 10000, // 100.00 NGN in kobo
        currency: "NGN",
        paymentMethod: "ussd",
      },
      shouldSucceed: true,
    },
    {
      name: "Invalid amount (zero)",
      data: {
        amount: 0,
        currency: "NGN",
        paymentMethod: "card",
      },
      shouldSucceed: false,
      expectedErrors: ["amount"],
    },
    {
      name: "Invalid amount (negative)",
      data: {
        amount: -100,
        currency: "NGN",
        paymentMethod: "card",
      },
      shouldSucceed: false,
      expectedErrors: ["amount"],
    },
    {
      name: "Invalid currency",
      data: {
        amount: 5000,
        currency: "EUR",
        paymentMethod: "card",
      },
      shouldSucceed: false,
      expectedErrors: ["currency"],
    },
    {
      name: "Invalid payment method",
      data: {
        amount: 5000,
        currency: "NGN",
        paymentMethod: "crypto",
      },
      shouldSucceed: false,
      expectedErrors: ["paymentMethod"],
    },
    {
      name: "Missing amount",
      data: {
        currency: "NGN",
        paymentMethod: "card",
      },
      shouldSucceed: false,
      expectedErrors: ["amount"],
    },
    {
      name: "Missing currency",
      data: {
        amount: 5000,
        paymentMethod: "card",
      },
      shouldSucceed: false,
      expectedErrors: ["currency"],
    },
    {
      name: "Missing payment method",
      data: {
        amount: 5000,
        currency: "NGN",
      },
      shouldSucceed: false,
      expectedErrors: ["paymentMethod"],
    },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n🧪 Testing: ${testCase.name}`);
      console.log(`📤 Request data:`, testCase.data);

      const response = await axios.post(
        `${BASE_URL}/sessions/${sessionId}/payment/initialize`,
        testCase.data,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (testCase.shouldSucceed) {
        console.log(`✅ ${testCase.name}: SUCCESS`);
        console.log("📦 Response data:", {
          status: response.status,
          message: response.data.message,
          hasAuthUrl: !!response.data.data?.authorization_url,
          reference: response.data.data?.reference,
        });
      } else {
        console.log(
          `⚠️ ${testCase.name}: Unexpectedly succeeded (should have failed)`
        );
        console.log("📦 Response:", response.data);
      }
    } catch (error) {
      if (
        !testCase.shouldSucceed &&
        error.response &&
        error.response.status === 400
      ) {
        console.log(`✅ ${testCase.name}: CORRECTLY REJECTED`);

        if (error.response.data.errors) {
          const errorFields = error.response.data.errors.map((e) => e.field);
          console.log("🔍 Validation errors found:");
          error.response.data.errors.forEach((err) => {
            console.log(`   - ${err.field}: ${err.message}`);
          });

          if (testCase.expectedErrors) {
            const missingExpected = testCase.expectedErrors.filter(
              (field) => !errorFields.includes(field)
            );
            const unexpectedErrors = errorFields.filter(
              (field) => !testCase.expectedErrors.includes(field)
            );

            if (missingExpected.length === 0 && unexpectedErrors.length === 0) {
              console.log("✅ Error fields match expectations");
            } else {
              if (missingExpected.length > 0) {
                console.log(
                  `⚠️ Missing expected errors: ${missingExpected.join(", ")}`
                );
              }
              if (unexpectedErrors.length > 0) {
                console.log(
                  `⚠️ Unexpected errors: ${unexpectedErrors.join(", ")}`
                );
              }
            }
          }
        }
      } else {
        console.log(`❌ ${testCase.name}: UNEXPECTED ERROR`);
        if (error.response) {
          console.log("Status:", error.response.status);
          console.log("Error data:", error.response.data);

          // Check if it's a session not found error
          if (error.response.status === 404) {
            console.log(
              "💡 This session ID might not exist. The validation is working correctly, but the session doesn't exist."
            );
          }

          // Check if it's an authorization error
          if (error.response.status === 403 || error.response.status === 401) {
            console.log(
              "💡 Authorization issue - user might not own this session or session might not exist."
            );
          }
        } else {
          console.log("Error:", error.message);
        }
      }
    }
  }
}

// Function to show the fix needed for frontend
function showFrontendFix() {
  console.log("\n🔧 FRONTEND FIX NEEDED:");
  console.log("========================");
  console.log(
    "The current frontend payment initialization is missing required fields."
  );
  console.log("\nCurrent frontend code (in PaymentAPI):");
  console.log(`
async initializePayment(sessionId: string, token: string) {
  const response = await fetch(
    \`\${API_BASE_URL}/sessions/\${sessionId}/payment/initialize\`,
    {
      method: "POST",
      headers: this.getHeaders(token),
      // ❌ Missing body with required fields
    }
  );
}`);

  console.log("\n✅ Fixed frontend code should be:");
  console.log(`
async initializePayment(
  sessionId: string, 
  token: string,
  paymentData: {
    amount: number;      // Amount in kobo (NGN) or cents (USD)
    currency: 'NGN' | 'USD';
    paymentMethod: 'card' | 'bank_transfer' | 'ussd';
  }
) {
  const response = await fetch(
    \`\${API_BASE_URL}/sessions/\${sessionId}/payment/initialize\`,
    {
      method: "POST",
      headers: this.getHeaders(token),
      body: JSON.stringify(paymentData),  // ✅ Include required fields
    }
  );
}`);

  console.log("\n📋 Required validation rules:");
  console.log("- amount: Must be > 0 (in smallest currency unit)");
  console.log("- currency: Must be 'NGN' or 'USD'");
  console.log("- paymentMethod: Must be 'card', 'bank_transfer', or 'ussd'");
  console.log("- Authorization: Valid Bearer token required");
  console.log("- Session ownership: User must own the session being paid for");
}

// Main execution
async function runPaymentValidationTests() {
  console.log("🚀 Payment Initialization Validation Tests");
  console.log("==========================================\n");

  // Step 1: Authenticate
  const isAuthenticated = await authenticateUser();
  if (!isAuthenticated) {
    console.log("\n❌ Cannot proceed without authentication.");
    return;
  }

  // Step 2: Test payment validation
  await testPaymentInitializationDirectly();

  // Step 3: Show the fix
  showFrontendFix();

  console.log("\n✨ Payment validation tests completed!");
}

// Run the tests
runPaymentValidationTests().catch(console.error);

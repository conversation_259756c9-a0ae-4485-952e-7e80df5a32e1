"use client";

import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";

interface RevenueChartProps {
  data: any[];
  period: "week" | "month" | "quarter" | "year";
}

export default function RevenueChart({ data, period }: RevenueChartProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatTooltip = (value: number, name: string) => {
    return [formatCurrency(value), name];
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Revenue Trends</h3>
        <div className="text-sm text-gray-500 capitalize">{period}ly view</div>
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data}>
            <defs>
              <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="period"
              tick={{ fontSize: 12 }}
              tickLine={{ stroke: "#e5e7eb" }}
            />
            <YAxis
              tickFormatter={formatCurrency}
              tick={{ fontSize: 12 }}
              tickLine={{ stroke: "#e5e7eb" }}
            />
            <Tooltip
              formatter={formatTooltip}
              labelStyle={{ color: "#374151" }}
              contentStyle={{
                backgroundColor: "#ffffff",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
              }}
            />
            <Area
              type="monotone"
              dataKey="revenue"
              stroke="#8B5CF6"
              strokeWidth={2}
              fill="url(#revenueGradient)"
              dot={{ fill: "#8B5CF6", strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: "#8B5CF6", strokeWidth: 2 }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <p className="text-sm text-gray-500">Total Revenue</p>
          <p className="text-lg font-semibold text-gray-900">
            {formatCurrency(
              data.reduce((sum, item) => sum + (item.revenue || 0), 0)
            )}
          </p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">Average</p>
          <p className="text-lg font-semibold text-gray-900">
            {formatCurrency(
              data.reduce((sum, item) => sum + (item.revenue || 0), 0) /
                data.length
            )}
          </p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">Highest</p>
          <p className="text-lg font-semibold text-gray-900">
            {formatCurrency(Math.max(...data.map((item) => item.revenue || 0)))}
          </p>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">Lowest</p>
          <p className="text-lg font-semibold text-gray-900">
            {formatCurrency(Math.min(...data.map((item) => item.revenue || 0)))}
          </p>
        </div>
      </div>
    </div>
  );
}

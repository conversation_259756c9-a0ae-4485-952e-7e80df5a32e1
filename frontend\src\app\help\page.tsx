import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import {
  Search,
  MessageCircle,
  Phone,
  Mail,
  Clock,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";

const categories = [
  {
    title: "Getting Started",
    icon: MessageCircle,
    articles: [
      { title: "How to create an account", href: "#create-account" },
      { title: "Finding the right counselor", href: "#find-counselor" },
      { title: "Scheduling your first session", href: "#first-session" },
      { title: "Platform features overview", href: "#features" },
    ],
  },
  {
    title: "Billing & Payments",
    icon: Search,
    articles: [
      { title: "Understanding pricing", href: "#pricing" },
      { title: "Payment methods accepted", href: "#payment-methods" },
      { title: "Refund policy", href: "#refunds" },
      { title: "Insurance coverage", href: "#insurance" },
    ],
  },
  {
    title: "Technical Support",
    icon: Phone,
    articles: [
      { title: "Video call troubleshooting", href: "#video-issues" },
      { title: "Audio problems", href: "#audio-issues" },
      { title: "Mobile app support", href: "#mobile-app" },
      { title: "Browser compatibility", href: "#browser-support" },
    ],
  },
  {
    title: "Privacy & Security",
    icon: Mail,
    articles: [
      { title: "HIPAA compliance", href: "#hipaa" },
      { title: "Data protection", href: "#data-protection" },
      { title: "Account security", href: "#account-security" },
      { title: "Reporting concerns", href: "#reporting" },
    ],
  },
];

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-gray-50">
            <Header />
      {/* Hero Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            Help Center
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Find answers to your questions and get support when you need it
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search for help articles..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-lg text-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent"
            />
          </div>
        </div>
      </section>

      {/* Emergency Contact */}
      <section className="bg-red-50 border-l-4 border-red-400 p-6 mx-4 mb-8">
        <div className="flex items-center">
          <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-red-800">
              Crisis Support
            </h3>
            <p className="text-red-700">
              If you're experiencing a mental health crisis, please call our
              24/7 crisis line:
              <strong className="ml-2">1-800-CRISIS (1-************)</strong> or
              <Link href="/crisis" className="text-red-800 underline ml-2">
                visit our crisis support page
              </Link>
            </p>
          </div>
        </div>
      </section>

      {/* Help Categories */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {categories.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center mb-4">
                  <category.icon className="h-8 w-8 text-purple-600 mr-3" />
                  <h2 className="text-2xl font-bold text-gray-800">
                    {category.title}
                  </h2>
                </div>
                <ul className="space-y-3">
                  {category.articles.map((article, articleIndex) => (
                    <li key={articleIndex}>
                      <a
                        href={article.href}
                        className="text-purple-600 hover:text-purple-800 hover:underline"
                      >
                        {article.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8 text-gray-800">
            Still Need Help?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Our support team is here to help you with any questions or concerns
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <Phone className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Phone Support</h3>
              <p className="text-gray-600 mb-4">Mon-Fri, 8AM-8PM EST</p>
              <p className="text-lg font-semibold">1-800-THERAPY</p>
            </div>

            <div className="text-center">
              <Mail className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Email Support</h3>
              <p className="text-gray-600 mb-4">Response within 24 hours</p>
              <a
                href="mailto:<EMAIL>"
                className="text-lg font-semibold text-purple-600 hover:underline"
              >
                <EMAIL>
              </a>
            </div>

            <div className="text-center">
              <Clock className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Live Chat</h3>
              <p className="text-gray-600 mb-4">Available 24/7</p>
              <button className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                Start Chat
              </button>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
}

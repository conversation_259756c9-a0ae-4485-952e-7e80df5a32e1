'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

export default function GoogleCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { checkAuth } = useAuthStore();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const handleCallback = async () => {
      const error = searchParams.get('error');
      const token = searchParams.get('token');
      const refreshToken = searchParams.get('refreshToken');

      if (error) {
        setStatus('error');
        setMessage(getErrorMessage(error));
        return;
      }

      if (token && refreshToken) {
        try {
          // Store tokens and check auth
          localStorage.setItem('auth-storage', JSON.stringify({
            state: {
              tokens: {
                accessToken: token,
                refreshToken: refreshToken,
              },
              isAuthenticated: true,
              isGuest: false,
              guestToken: null,
            },
            version: 0,
          }));

          await checkAuth();
          setStatus('success');
          setMessage('Successfully signed in with Google!');
          
          // Redirect to dashboard after 2 seconds
          setTimeout(() => {
            router.push('/dashboard');
          }, 2000);
        } catch (error) {
          setStatus('error');
          setMessage('Failed to complete Google sign-in. Please try again.');
        }
      } else {
        setStatus('error');
        setMessage('Invalid callback parameters. Please try signing in again.');
      }
    };

    handleCallback();
  }, [searchParams, router, checkAuth]);

  const getErrorMessage = (error: string) => {
    switch (error) {
      case 'oauth_failed':
        return 'Google authentication failed. Please try again.';
      case 'access_denied':
        return 'Access was denied. Please grant permission to continue.';
      case 'invalid_request':
        return 'Invalid request. Please try signing in again.';
      default:
        return 'An error occurred during Google sign-in. Please try again.';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {status === 'loading' && (
              <>
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Completing sign-in...
                </h2>
                <p className="text-gray-600">
                  Please wait while we complete your Google sign-in.
                </p>
              </>
            )}

            {status === 'success' && (
              <>
                <CheckCircleIcon className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Sign-in Successful!
                </h2>
                <p className="text-gray-600 mb-6">
                  {message}
                </p>
                <p className="text-sm text-gray-500">
                  Redirecting to your dashboard...
                </p>
              </>
            )}

            {status === 'error' && (
              <>
                <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Sign-in Failed
                </h2>
                <p className="text-gray-600 mb-6">
                  {message}
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => router.push('/auth/login')}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    Try Again
                  </button>
                  <button
                    onClick={() => router.push('/')}
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    Go Home
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

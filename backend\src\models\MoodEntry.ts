import mongoose, { Document, Schema } from "mongoose";

export interface IMoodEntry extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  mood: number; // 1-5 scale
  note?: string;
  tags: string[]; // e.g., ['work', 'family', 'health', 'social']
  triggers?: string[]; // What caused this mood
  activities?: string[]; // What activities were done
  energy: number; // 1-5 scale
  anxiety: number; // 1-5 scale
  sleep?: {
    hours: number;
    quality: number; // 1-5 scale
  };
  weather?: {
    condition: string; // 'sunny', 'cloudy', 'rainy', 'snowy'
    temperature?: number;
  };
  location?: {
    type: 'home' | 'work' | 'school' | 'outdoors' | 'social' | 'other';
    description?: string;
  };
  socialInteraction?: {
    level: number; // 1-5 scale (1 = alone, 5 = very social)
    quality: number; // 1-5 scale
  };
  medications?: {
    taken: boolean;
    names?: string[];
    notes?: string;
  };
  symptoms?: string[]; // Physical or mental symptoms
  gratitude?: string[]; // Things user is grateful for
  goals?: {
    achieved: string[];
    working_on: string[];
  };
  isPrivate: boolean;
  metadata: {
    source: 'manual' | 'reminder' | 'onboarding' | 'session_followup';
    deviceType?: 'mobile' | 'desktop' | 'tablet';
    timeZone?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const moodEntrySchema = new Schema<IMoodEntry>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  mood: {
    type: Number,
    required: true,
    min: [1, 'Mood must be between 1 and 5'],
    max: [5, 'Mood must be between 1 and 5']
  },
  note: {
    type: String,
    maxlength: [1000, 'Note cannot exceed 1000 characters'],
    trim: true
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [50, 'Tag cannot exceed 50 characters']
  }],
  triggers: [{
    type: String,
    trim: true,
    maxlength: [100, 'Trigger cannot exceed 100 characters']
  }],
  activities: [{
    type: String,
    trim: true,
    maxlength: [100, 'Activity cannot exceed 100 characters']
  }],
  energy: {
    type: Number,
    required: true,
    min: [1, 'Energy must be between 1 and 5'],
    max: [5, 'Energy must be between 1 and 5'],
    default: 3
  },
  anxiety: {
    type: Number,
    required: true,
    min: [1, 'Anxiety must be between 1 and 5'],
    max: [5, 'Anxiety must be between 1 and 5'],
    default: 3
  },
  sleep: {
    hours: {
      type: Number,
      min: [0, 'Sleep hours cannot be negative'],
      max: [24, 'Sleep hours cannot exceed 24']
    },
    quality: {
      type: Number,
      min: [1, 'Sleep quality must be between 1 and 5'],
      max: [5, 'Sleep quality must be between 1 and 5']
    }
  },
  weather: {
    condition: {
      type: String,
      enum: ['sunny', 'cloudy', 'rainy', 'snowy', 'stormy', 'foggy', 'windy']
    },
    temperature: {
      type: Number,
      min: [-50, 'Temperature too low'],
      max: [60, 'Temperature too high']
    }
  },
  location: {
    type: {
      type: String,
      enum: ['home', 'work', 'school', 'outdoors', 'social', 'other']
    },
    description: {
      type: String,
      maxlength: [100, 'Location description cannot exceed 100 characters']
    }
  },
  socialInteraction: {
    level: {
      type: Number,
      min: [1, 'Social interaction level must be between 1 and 5'],
      max: [5, 'Social interaction level must be between 1 and 5']
    },
    quality: {
      type: Number,
      min: [1, 'Social interaction quality must be between 1 and 5'],
      max: [5, 'Social interaction quality must be between 1 and 5']
    }
  },
  medications: {
    taken: {
      type: Boolean,
      default: false
    },
    names: [{
      type: String,
      trim: true,
      maxlength: [100, 'Medication name cannot exceed 100 characters']
    }],
    notes: {
      type: String,
      maxlength: [500, 'Medication notes cannot exceed 500 characters']
    }
  },
  symptoms: [{
    type: String,
    trim: true,
    maxlength: [100, 'Symptom cannot exceed 100 characters']
  }],
  gratitude: [{
    type: String,
    trim: true,
    maxlength: [200, 'Gratitude entry cannot exceed 200 characters']
  }],
  goals: {
    achieved: [{
      type: String,
      trim: true,
      maxlength: [200, 'Goal cannot exceed 200 characters']
    }],
    working_on: [{
      type: String,
      trim: true,
      maxlength: [200, 'Goal cannot exceed 200 characters']
    }]
  },
  isPrivate: {
    type: Boolean,
    default: true
  },
  metadata: {
    source: {
      type: String,
      enum: ['manual', 'reminder', 'onboarding', 'session_followup'],
      required: true,
      default: 'manual'
    },
    deviceType: {
      type: String,
      enum: ['mobile', 'desktop', 'tablet']
    },
    timeZone: {
      type: String,
      default: 'UTC'
    }
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
moodEntrySchema.index({ userId: 1, createdAt: -1 });
moodEntrySchema.index({ userId: 1, mood: 1 });
moodEntrySchema.index({ userId: 1, tags: 1 });
moodEntrySchema.index({ createdAt: -1 });
moodEntrySchema.index({ 'metadata.source': 1 });

// Compound indexes for analytics
moodEntrySchema.index({ userId: 1, createdAt: -1, mood: 1 });
moodEntrySchema.index({ userId: 1, energy: 1, anxiety: 1 });

// Virtual to populate user details
moodEntrySchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

// Ensure virtual fields are serialized
moodEntrySchema.set('toJSON', { virtuals: true });
moodEntrySchema.set('toObject', { virtuals: true });

// Pre-save middleware to validate data consistency
moodEntrySchema.pre('save', function(next) {
  // Ensure at least one of the main mood indicators is provided
  if (!this.mood && !this.energy && !this.anxiety) {
    return next(new Error('At least mood, energy, or anxiety level must be provided'));
  }
  
  // Validate tags array length
  if (this.tags && this.tags.length > 10) {
    return next(new Error('Cannot have more than 10 tags'));
  }
  
  // Validate triggers array length
  if (this.triggers && this.triggers.length > 5) {
    return next(new Error('Cannot have more than 5 triggers'));
  }
  
  // Validate activities array length
  if (this.activities && this.activities.length > 10) {
    return next(new Error('Cannot have more than 10 activities'));
  }
  
  next();
});

export const MoodEntry = mongoose.model<IMoodEntry>('MoodEntry', moodEntrySchema);
export default MoodEntry;

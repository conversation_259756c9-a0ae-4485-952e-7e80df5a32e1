import { Star, Quote } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    location: "New York, NY",
    rating: 5,
    text: "<PERSON><PERSON><PERSON> changed my life. The counselors are incredibly understanding and the platform makes it so easy to get help when I need it.",
    avatar: "/api/placeholder/64/64",
  },
  {
    name: "<PERSON>",
    location: "Los Angeles, CA",
    rating: 5,
    text: "I was skeptical about online therapy, but the video sessions feel just as personal as in-person meetings. Highly recommend!",
    avatar: "/api/placeholder/64/64",
  },
  {
    name: "<PERSON>",
    location: "Chicago, IL",
    rating: 5,
    text: "The group sessions have been amazing. It's comforting to know I'm not alone in my struggles. The community is so supportive.",
    avatar: "/api/placeholder/64/64",
  },
];

export default function Testimonials() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            What Our Users Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what real people are saying
            about their experience with Theramea.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-xl relative">
              <Quote className="h-8 w-8 text-purple-300 mb-4" />
              <p className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.text}"
              </p>

              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 text-yellow-400 fill-current"
                  />
                ))}
              </div>

              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-200 rounded-full flex items-center justify-center mr-4">
                  <span className="text-purple-700 font-semibold">
                    {testimonial.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="font-semibold text-gray-900">
                    {testimonial.name}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {testimonial.location}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

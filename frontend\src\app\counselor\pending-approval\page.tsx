'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { counselorPortalAPI } from '@/lib/counselorPortal';
import Header from '@/components/layout/Header';
import { 
  ClockIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  DocumentTextIcon,
  EnvelopeIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

interface ApplicationStatus {
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  estimatedReviewTime?: string;
}

export default function PendingApprovalPage() {
  const router = useRouter();
  const { isAuthenticated, user, tokens } = useAuthStore();
  const [applicationStatus, setApplicationStatus] = useState<ApplicationStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    fetchApplicationStatus();
  }, [isAuthenticated, router]);

  const fetchApplicationStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await counselorPortalAPI.getApplicationStatus(tokens!.accessToken);
      setApplicationStatus(response.data);

      // If approved, redirect to counselor dashboard
      if (response.data.status === 'approved') {
        router.push('/counselor');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load application status');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (!applicationStatus) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              No Application Found
            </h1>
            <p className="text-gray-600 mb-6">
              We couldn't find your counselor application. Please submit an application to get started.
            </p>
            <button
              onClick={() => router.push('/counselor/register')}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md font-medium"
            >
              Apply as Counselor
            </button>
          </div>
        </div>
      </div>
    );
  }

  const getStatusIcon = () => {
    switch (applicationStatus.status) {
      case 'pending':
        return <ClockIcon className="h-16 w-16 text-yellow-500" />;
      case 'approved':
        return <CheckCircleIcon className="h-16 w-16 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-16 w-16 text-red-500" />;
      default:
        return <ClockIcon className="h-16 w-16 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    switch (applicationStatus.status) {
      case 'pending':
        return {
          title: 'Application Under Review',
          description: 'Thank you for applying to become a counselor on Theramea. Our team is currently reviewing your application and documents.',
          color: 'text-yellow-800',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200'
        };
      case 'approved':
        return {
          title: 'Application Approved!',
          description: 'Congratulations! Your counselor application has been approved. You can now access your counselor dashboard.',
          color: 'text-green-800',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'rejected':
        return {
          title: 'Application Rejected',
          description: 'Unfortunately, your counselor application was not approved at this time. Please see the details below.',
          color: 'text-red-800',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      default:
        return {
          title: 'Application Status Unknown',
          description: 'We are unable to determine your application status at this time.',
          color: 'text-gray-800',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const statusInfo = getStatusMessage();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          {getStatusIcon()}
          <h1 className="mt-4 text-3xl font-bold text-gray-900">
            {statusInfo.title}
          </h1>
        </div>

        <div className={`${statusInfo.bgColor} ${statusInfo.borderColor} border rounded-lg p-6 mb-8`}>
          <p className={`${statusInfo.color} text-center text-lg`}>
            {statusInfo.description}
          </p>
        </div>

        {/* Application Details */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Application Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Submitted</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(applicationStatus.submittedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <p className={`mt-1 text-sm font-medium capitalize ${
                applicationStatus.status === 'pending' ? 'text-yellow-600' :
                applicationStatus.status === 'approved' ? 'text-green-600' :
                'text-red-600'
              }`}>
                {applicationStatus.status}
              </p>
            </div>

            {applicationStatus.reviewedAt && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Reviewed</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(applicationStatus.reviewedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            )}

            {applicationStatus.estimatedReviewTime && applicationStatus.status === 'pending' && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Estimated Review Time</label>
                <p className="mt-1 text-sm text-gray-900">
                  {applicationStatus.estimatedReviewTime}
                </p>
              </div>
            )}
          </div>

          {applicationStatus.rejectionReason && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="text-sm font-medium text-red-900 mb-2">Rejection Reason</h3>
              <p className="text-sm text-red-700">{applicationStatus.rejectionReason}</p>
            </div>
          )}
        </div>

        {/* Next Steps */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">What's Next?</h2>
          
          {applicationStatus.status === 'pending' && (
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <ClockIcon className="h-6 w-6 text-yellow-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Wait for Review</h3>
                  <p className="text-sm text-gray-600">
                    Our team typically reviews applications within 3-5 business days. We'll notify you via email once the review is complete.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <DocumentTextIcon className="h-6 w-6 text-blue-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Prepare for Onboarding</h3>
                  <p className="text-sm text-gray-600">
                    While you wait, you can review our counselor guidelines and prepare for your first sessions.
                  </p>
                </div>
              </div>
            </div>
          )}

          {applicationStatus.status === 'approved' && (
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <CheckCircleIcon className="h-6 w-6 text-green-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Access Your Dashboard</h3>
                  <p className="text-sm text-gray-600">
                    You can now access your counselor dashboard to set up your profile, availability, and start accepting clients.
                  </p>
                  <button
                    onClick={() => router.push('/counselor')}
                    className="mt-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Go to Dashboard
                  </button>
                </div>
              </div>
            </div>
          )}

          {applicationStatus.status === 'rejected' && (
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <XCircleIcon className="h-6 w-6 text-red-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Review Feedback</h3>
                  <p className="text-sm text-gray-600">
                    Please review the rejection reason above and address any issues mentioned.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <DocumentTextIcon className="h-6 w-6 text-blue-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Reapply</h3>
                  <p className="text-sm text-gray-600">
                    You can submit a new application after addressing the feedback. Please wait at least 30 days before reapplying.
                  </p>
                  <button
                    onClick={() => router.push('/counselor/register')}
                    className="mt-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Submit New Application
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Contact Support */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Need Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center space-x-3">
              <EnvelopeIcon className="h-6 w-6 text-purple-600" />
              <div>
                <h3 className="font-medium text-gray-900">Email Support</h3>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <PhoneIcon className="h-6 w-6 text-purple-600" />
              <div>
                <h3 className="font-medium text-gray-900">Phone Support</h3>
                <p className="text-sm text-gray-600">+234 (0) ************</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

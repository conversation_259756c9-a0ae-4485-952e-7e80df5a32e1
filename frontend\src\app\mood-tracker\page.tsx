"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import Header from "@/components/layout/Header";
import Link from "next/link";
import { moodAPI, MoodEntry, CreateMoodEntryData } from "@/lib/mood";
import MoodAnalytics from "@/components/mood/MoodAnalytics";
import MoodSuccessModal from "@/components/mood/MoodSuccessModal";

export default function MoodTrackerPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isAuthenticated, isGuest, checkAuth, isLoading, tokens } =
    useAuthStore();
  const [activeTab, setActiveTab] = useState<"entry" | "history" | "analytics">(
    "entry"
  );
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [lastSubmittedEntry, setLastSubmittedEntry] =
    useState<CreateMoodEntryData | null>(null);

  // Mood entry form state
  const [moodData, setMoodData] = useState<CreateMoodEntryData>({
    mood: 3,
    energy: 3,
    anxiety: 3,
    note: "",
    tags: [],
    triggers: [],
    activities: [],
    isPrivate: true,
    metadata: {
      source: "manual",
    },
  });

  // History state
  const [moodEntries, setMoodEntries] = useState<MoodEntry[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  useEffect(() => {
    checkAuth();

    // Handle tab parameter from URL
    const tabParam = searchParams.get("tab");
    if (
      tabParam === "analytics" ||
      tabParam === "history" ||
      tabParam === "entry"
    ) {
      setActiveTab(tabParam);
    }
  }, [checkAuth, searchParams]);

  useEffect(() => {
    if (!isAuthenticated && !isGuest && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (isGuest) {
      // Guests can use mood tracker but data won't be saved
      setLoading(false);
      return;
    }

    if (isAuthenticated && tokens?.accessToken) {
      setLoading(false);
      if (activeTab === "history") {
        loadMoodHistory();
      }
    }
  }, [isAuthenticated, isGuest, isLoading, router, tokens, activeTab]);

  const loadMoodHistory = async () => {
    if (!tokens?.accessToken) return;

    try {
      setHistoryLoading(true);
      const response = await moodAPI.getMoodEntries(
        { limit: 20, sortBy: "createdAt", sortOrder: "desc" },
        tokens.accessToken
      );
      setMoodEntries(response.entries);
    } catch (error) {
      console.error("Error loading mood history:", error);
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleSubmitMood = async () => {
    const currentEntry = { ...moodData }; // Store current entry before reset

    if (isGuest) {
      // For guests, show success modal and reset form
      setLastSubmittedEntry(currentEntry);
      setMoodData({
        mood: 3,
        energy: 3,
        anxiety: 3,
        note: "",
        tags: [],
        triggers: [],
        activities: [],
        isPrivate: true,
        metadata: { source: "manual" },
      });
      setShowSuccessModal(true);
      return;
    }

    if (!tokens?.accessToken) {
      alert("Please log in to save your mood entry.");
      return;
    }

    try {
      setSubmitting(true);
      await moodAPI.createMoodEntry(moodData, tokens.accessToken);

      // Store entry data and reset form
      setLastSubmittedEntry(currentEntry);
      setMoodData({
        mood: 3,
        energy: 3,
        anxiety: 3,
        note: "",
        tags: [],
        triggers: [],
        activities: [],
        isPrivate: true,
        metadata: { source: "manual" },
      });

      // Show success modal
      setShowSuccessModal(true);

      // Refresh history if on history tab
      if (activeTab === "history") {
        loadMoodHistory();
      }
    } catch (error) {
      console.error("Error saving mood entry:", error);
      alert(
        error instanceof Error ? error.message : "Failed to save mood entry"
      );
    } finally {
      setSubmitting(false);
    }
  };

  const handleTagToggle = (tag: string) => {
    setMoodData((prev) => ({
      ...prev,
      tags: prev.tags?.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...(prev.tags || []), tag],
    }));
  };

  const handleTriggerToggle = (trigger: string) => {
    setMoodData((prev) => ({
      ...prev,
      triggers: prev.triggers?.includes(trigger)
        ? prev.triggers.filter((t) => t !== trigger)
        : [...(prev.triggers || []), trigger],
    }));
  };

  const handleActivityToggle = (activity: string) => {
    setMoodData((prev) => ({
      ...prev,
      activities: prev.activities?.includes(activity)
        ? prev.activities.filter((a) => a !== activity)
        : [...(prev.activities || []), activity],
    }));
  };

  const getMoodEmoji = (mood: number) => {
    const options = moodAPI.getMoodOptions();
    return options.find((option) => option.value === mood)?.emoji || "😐";
  };

  const getMoodLabel = (mood: number) => {
    const options = moodAPI.getMoodOptions();
    return options.find((option) => option.value === mood)?.label || "Neutral";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Mood Tracker</h1>
              <p className="text-gray-600 mt-2">
                Track your daily mood, energy, and well-being
              </p>
            </div>
            <Link
              href="/dashboard"
              className="text-purple-600 hover:text-purple-700 font-medium flex items-center"
            >
              ← Back to Dashboard
            </Link>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { id: "entry", name: "New Entry", icon: "✏️" },
                { id: "history", name: "History", icon: "📊" },
                { id: "analytics", name: "Analytics", icon: "📈" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === "entry" && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              How are you feeling today?
            </h2>

            {/* Mood Scale */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Overall Mood
              </label>
              <div className="flex justify-between items-center">
                {moodAPI.getMoodOptions().map((option) => (
                  <button
                    key={option.value}
                    onClick={() =>
                      setMoodData((prev) => ({ ...prev, mood: option.value }))
                    }
                    className={`flex flex-col items-center p-3 rounded-lg transition-all duration-200 ${
                      moodData.mood === option.value
                        ? "bg-purple-100 border-2 border-purple-500 scale-110"
                        : "bg-gray-50 border-2 border-transparent hover:bg-purple-50 hover:border-purple-300"
                    }`}
                  >
                    <span className="text-4xl mb-2">{option.emoji}</span>
                    <span className={`text-sm font-medium ${option.color}`}>
                      {option.label}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* Energy Level */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Energy Level
              </label>
              <div className="flex space-x-4">
                {[1, 2, 3, 4, 5].map((level) => (
                  <button
                    key={level}
                    onClick={() =>
                      setMoodData((prev) => ({ ...prev, energy: level }))
                    }
                    className={`flex-1 py-3 px-4 rounded-lg border-2 transition-colors ${
                      moodData.energy === level
                        ? "border-purple-500 bg-purple-100 text-purple-700"
                        : "border-gray-200 bg-gray-50 text-gray-600 hover:border-purple-300"
                    }`}
                  >
                    {level}
                  </button>
                ))}
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>Very Low</span>
                <span>Very High</span>
              </div>
            </div>

            {/* Anxiety Level */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Anxiety Level
              </label>
              <div className="flex space-x-4">
                {[1, 2, 3, 4, 5].map((level) => (
                  <button
                    key={level}
                    onClick={() =>
                      setMoodData((prev) => ({ ...prev, anxiety: level }))
                    }
                    className={`flex-1 py-3 px-4 rounded-lg border-2 transition-colors ${
                      moodData.anxiety === level
                        ? "border-purple-500 bg-purple-100 text-purple-700"
                        : "border-gray-200 bg-gray-50 text-gray-600 hover:border-purple-300"
                    }`}
                  >
                    {level}
                  </button>
                ))}
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>Very Calm</span>
                <span>Very Anxious</span>
              </div>
            </div>

            {/* Tags */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Tags (optional)
              </label>
              <div className="flex flex-wrap gap-2">
                {moodAPI.getCommonTags().map((tag) => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      moodData.tags?.includes(tag)
                        ? "bg-purple-600 text-white"
                        : "bg-gray-100 text-gray-600 hover:bg-purple-100 hover:text-purple-600"
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>

            {/* Triggers */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                What triggered this mood? (optional)
              </label>
              <p className="text-xs text-gray-500 mb-3">
                Select what might have influenced how you're feeling today
              </p>
              <div className="flex flex-wrap gap-2">
                {moodAPI.getCommonTriggers().map((trigger) => (
                  <button
                    key={trigger}
                    onClick={() => handleTriggerToggle(trigger)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                      moodData.triggers?.includes(trigger)
                        ? "bg-orange-100 text-orange-800 border-orange-300"
                        : "bg-white text-gray-600 border-gray-200 hover:bg-orange-50 hover:border-orange-200"
                    }`}
                  >
                    {trigger}
                  </button>
                ))}
              </div>
            </div>

            {/* Activities */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                What activities did you do today? (optional)
              </label>
              <p className="text-xs text-gray-500 mb-3">
                Select activities that might have influenced your mood
              </p>
              <div className="flex flex-wrap gap-2">
                {moodAPI.getCommonActivities().map((activity) => (
                  <button
                    key={activity}
                    onClick={() => handleActivityToggle(activity)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                      moodData.activities?.includes(activity)
                        ? "bg-green-100 text-green-800 border-green-300"
                        : "bg-white text-gray-600 border-gray-200 hover:bg-green-50 hover:border-green-200"
                    }`}
                  >
                    {activity}
                  </button>
                ))}
              </div>
            </div>

            {/* Note */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes (optional)
              </label>
              <textarea
                value={moodData.note}
                onChange={(e) =>
                  setMoodData((prev) => ({ ...prev, note: e.target.value }))
                }
                rows={4}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="How are you feeling? What's on your mind?"
                maxLength={1000}
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {moodData.note?.length || 0}/1000
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSubmitMood}
                disabled={submitting}
                className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-8 py-3 rounded-lg font-medium transition-colors"
              >
                {submitting ? "Saving..." : "Save Entry"}
              </button>
            </div>
          </div>
        )}

        {activeTab === "history" && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Mood History
              </h2>
            </div>

            {isGuest ? (
              <div className="text-center py-12">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Sign up to view history
                </h3>
                <p className="text-gray-600 mb-4">
                  Create an account to save and view your mood tracking history.
                </p>
                <Link
                  href="/auth/signup"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            ) : historyLoading ? (
              <div className="p-6">
                <div className="animate-pulse space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
                  ))}
                </div>
              </div>
            ) : moodEntries.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {moodEntries.map((entry) => (
                  <div
                    key={entry._id}
                    className="p-6 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-3xl">
                          {getMoodEmoji(entry.mood)}
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            {getMoodLabel(entry.mood)}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>{formatDate(entry.createdAt)}</span>
                            <span>Energy: {entry.energy}/5</span>
                            <span>Anxiety: {entry.anxiety}/5</span>
                          </div>
                          {entry.note && (
                            <p className="text-sm text-gray-600 mt-2">
                              {entry.note}
                            </p>
                          )}
                          {/* Tags */}
                          {entry.tags && entry.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              <span className="text-xs text-gray-400 mr-2">
                                Tags:
                              </span>
                              {entry.tags.map((tag, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded-full"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}

                          {/* Triggers */}
                          {entry.triggers && entry.triggers.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              <span className="text-xs text-gray-400 mr-2">
                                Triggers:
                              </span>
                              {entry.triggers.map((trigger, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full"
                                >
                                  {trigger}
                                </span>
                              ))}
                            </div>
                          )}

                          {/* Activities */}
                          {entry.activities && entry.activities.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              <span className="text-xs text-gray-400 mr-2">
                                Activities:
                              </span>
                              {entry.activities.map((activity, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full"
                                >
                                  {activity}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-4xl mb-4">📝</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No mood entries yet
                </h3>
                <p className="text-gray-600 mb-4">
                  Start tracking your mood to see your history here.
                </p>
                <button
                  onClick={() => setActiveTab("entry")}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Create First Entry
                </button>
              </div>
            )}
          </div>
        )}

        {activeTab === "analytics" && (
          <div>
            {isGuest ? (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-center py-12">
                  <div className="text-4xl mb-4">📈</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Sign up to view analytics
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Create an account to see detailed insights about your mood
                    patterns.
                  </p>
                  <Link
                    href="/auth/signup"
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              </div>
            ) : tokens?.accessToken ? (
              <MoodAnalytics token={tokens.accessToken} />
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-center py-12">
                  <div className="text-4xl mb-4">🔐</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Authentication Required
                  </h3>
                  <p className="text-gray-600">
                    Please log in to view your mood analytics.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Success Modal */}
      {lastSubmittedEntry && showSuccessModal && (
        <MoodSuccessModal
          key={`mood-modal-${Date.now()}`}
          isOpen={showSuccessModal}
          onClose={() => {
            console.log("Parent onClose called");
            setShowSuccessModal(false);
            setLastSubmittedEntry(null);
          }}
          onViewAnalytics={() => {
            console.log("Parent onViewAnalytics called");
            setActiveTab("analytics");
          }}
          moodEntry={lastSubmittedEntry}
          isGuest={isGuest}
          token={tokens?.accessToken}
        />
      )}
    </div>
  );
}

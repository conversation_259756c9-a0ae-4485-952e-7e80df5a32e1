# Readonly Chat Feature for Guest Users

## Overview

Guest users now have **readonly access** to chat rooms, meaning they can view messages but cannot send messages. This ensures that anonymous users can observe conversations without actively participating.

## Implementation Details

### Backend Changes

1. **Extended ChatRoom Model**

   - Added `readonly` to the participant role enum: `['participant', 'moderator', 'readonly']`
   - Updated TypeScript interfaces to include the readonly role

2. **Updated ChatService**

   - Guest users (users without `userId`) are automatically assigned the `readonly` role when joining rooms
   - Added validation in `sendMessage()` to prevent readonly participants from sending messages
   - Error message: "Read-only access: You cannot send messages in this room"

3. **Type Safety**
   - Updated all relevant TypeScript interfaces to include the `readonly` role option

### Frontend Changes

1. **Enhanced ChatRoom Component**

   - Added `isReadonly` state to track if current user has readonly access
   - Automatically detects readonly status after fetching room data by checking participant role
   - Disables message input for readonly users
   - Shows informative readonly notice banner

2. **Updated ChatRoomCard Component**

   - Added readonly notice for guest users in rooms that allow anonymous access
   - Visual indicator that guest access is read-only

3. **Message Input Behavior**
   - Disabled for readonly users
   - Updated placeholder text to indicate readonly status
   - Prevents typing indicators for readonly users

## User Experience

### For Guest Users

- **Clear Visual Feedback**: Amber-colored notice explaining readonly access
- **Disabled Interface**: Message input is visually disabled with appropriate placeholder text
- **Informative Messages**: Clear explanation of limitations before joining rooms

### For Registered Users

- **No Changes**: Full functionality remains the same
- **Role Visibility**: Can see which participants are readonly in participant lists

## Security & Validation

- **Server-side Enforcement**: All readonly restrictions are enforced at the backend level
- **Socket Integration**: Readonly validation works through both REST API and WebSocket connections
- **Error Handling**: Graceful error messages for readonly users attempting to send messages

## Testing

The feature can be tested by:

1. Creating an anonymous/guest session
2. Joining a chat room that allows anonymous users
3. Verifying that:
   - Messages can be viewed
   - Message input is disabled
   - Readonly notice is displayed
   - Attempting to send messages through API returns appropriate error

## Future Enhancements

Potential improvements could include:

- Admin setting to control readonly vs full guest access per room
- Temporary readonly status for moderated users
- Readonly role for registered users (e.g., during events)
- Enhanced participant list showing user roles

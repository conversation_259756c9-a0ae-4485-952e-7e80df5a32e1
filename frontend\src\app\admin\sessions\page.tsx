"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import AdminLayout from '@/components/admin/AdminLayout';

interface Session {
  id: string;
  clientName: string;
  counselorName: string;
  date: string;
  time: string;
  duration: number;
  type: 'video' | 'audio' | 'chat';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  amount: number;
  notes?: string;
}

export default function AdminSessionsPage() {
  const router = useRouter();
  const { user, isAuthenticated, checkAuth, isLoading } = useAuthStore();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'scheduled' | 'completed' | 'cancelled'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push('/auth/login');
      return;
    }

    if (isAuthenticated && user?.role === 'admin') {
      loadSessionsData();
    } else if (isAuthenticated && user?.role !== 'admin') {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, user, router]);

  const loadSessionsData = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockSessions: Session[] = [
        {
          id: '1',
          clientName: 'John Doe',
          counselorName: 'Dr. Sarah Johnson',
          date: '2024-01-15',
          time: '14:00',
          duration: 50,
          type: 'video',
          status: 'completed',
          amount: 120,
          notes: 'Session completed successfully'
        },
        {
          id: '2',
          clientName: 'Jane Smith',
          counselorName: 'Michael Chen',
          date: '2024-01-15',
          time: '16:00',
          duration: 50,
          type: 'video',
          status: 'scheduled',
          amount: 120
        },
        {
          id: '3',
          clientName: 'Bob Wilson',
          counselorName: 'Dr. Emily Rodriguez',
          date: '2024-01-14',
          time: '10:00',
          duration: 50,
          type: 'audio',
          status: 'no-show',
          amount: 120,
          notes: 'Client did not attend'
        },
        {
          id: '4',
          clientName: 'Alice Brown',
          counselorName: 'Dr. Sarah Johnson',
          date: '2024-01-14',
          time: '15:00',
          duration: 50,
          type: 'video',
          status: 'cancelled',
          amount: 0,
          notes: 'Cancelled by client 2 hours before'
        }
      ];

      setSessions(mockSessions);
    } catch (error) {
      console.error('Error loading sessions data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredSessions = sessions.filter(session => {
    const matchesFilter = filter === 'all' || session.status === filter;
    const matchesSearch = searchTerm === '' || 
      session.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.counselorName.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'scheduled':
        return 'text-blue-600 bg-blue-100';
      case 'in-progress':
        return 'text-purple-600 bg-purple-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      case 'no-show':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return '🎥';
      case 'audio':
        return '🎧';
      case 'chat':
        return '💬';
      default:
        return '📞';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Session Management</h1>
            <p className="text-gray-600">Monitor and manage all therapy sessions</p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search by client or counselor name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div className="flex gap-2">
              {[
                { key: 'all', label: 'All Sessions' },
                { key: 'scheduled', label: 'Scheduled' },
                { key: 'completed', label: 'Completed' },
                { key: 'cancelled', label: 'Cancelled' }
              ].map((filterOption) => (
                <button
                  key={filterOption.key}
                  onClick={() => setFilter(filterOption.key as any)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    filter === filterOption.key
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-purple-50 hover:text-purple-600'
                  }`}
                >
                  {filterOption.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Sessions List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Sessions ({filteredSessions.length})
            </h2>
          </div>
          
          {filteredSessions.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredSessions.map((session) => (
                <div key={session.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">{getTypeIcon(session.type)}</div>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {session.clientName} → {session.counselorName}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span>{new Date(session.date).toLocaleDateString()} at {session.time}</span>
                          <span>• {session.duration} minutes</span>
                          <span>• ${session.amount}</span>
                        </div>
                        {session.notes && (
                          <p className="text-sm text-gray-600 mt-2">{session.notes}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                        {session.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <button className="text-gray-400 hover:text-gray-600">
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">📅</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No sessions found</h3>
              <p className="text-gray-600">
                {filter === 'all' 
                  ? 'No sessions have been scheduled yet.'
                  : `No ${filter} sessions found. Try a different filter.`
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}

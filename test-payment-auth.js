const axios = require("axios");

const API_BASE_URL = "http://localhost:5000/api";

// You'll need to replace this with a valid JWT token from the frontend
// You can get this from the browser's localStorage or developer tools
const AUTH_TOKEN = "your-jwt-token-here";

async function testPaymentAuth() {
  try {
    console.log("Testing payment authorization...\n");

    const sessionId = "689e8ac358caf361f78ce110";

    // First, let's see what user is authenticated with the current token
    try {
      const profileResponse = await axios.get(`${API_BASE_URL}/users/profile`, {
        headers: {
          Authorization: `Bearer ${AUTH_TOKEN}`,
          "Content-Type": "application/json",
        },
      });

      console.log("Authenticated User:");
      console.log(`ID: ${profileResponse.data.data.user._id}`);
      console.log(
        `Name: ${profileResponse.data.data.user.firstName} ${profileResponse.data.data.user.lastName}`
      );
      console.log(`Email: ${profileResponse.data.data.user.email}`);
    } catch (error) {
      console.log("❌ Failed to get user profile - token might be invalid");
      console.log(
        "Please update the AUTH_TOKEN variable with a valid JWT token from the frontend"
      );
      return;
    }

    // Now try the payment initialization
    console.log("\nTrying payment initialization...");

    const paymentResponse = await axios.post(
      `${API_BASE_URL}/sessions/${sessionId}/payment/initialize`,
      {}, // Empty body as per the route
      {
        headers: {
          Authorization: `Bearer ${AUTH_TOKEN}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ Payment initialization successful:");
    console.log(JSON.stringify(paymentResponse.data, null, 2));
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);

    if (error.response?.status === 403) {
      console.log(
        "\n💡 The issue is that the authenticated user is not the owner of this booking."
      );
      console.log(
        "Expected session owner: 687fe3b739da96aef19699d3 (Victor Okoroji - <EMAIL>)"
      );
      console.log(
        "Make sure you're logged in as the correct user in the frontend."
      );
    }
  }
}

// Instructions for getting the token
console.log("=== HOW TO GET AUTH TOKEN ===");
console.log("1. Open the frontend in your browser (http://localhost:3000)");
console.log("2. Log in as Victor Okoroji (<EMAIL>)");
console.log("3. Open browser Developer Tools (F12)");
console.log("4. Go to Application/Storage tab > Local Storage");
console.log('5. Look for a key like "auth_token" or "accessToken"');
console.log("6. Copy the value and replace AUTH_TOKEN variable in this script");
console.log("7. Run this script again\n");

testPaymentAuth();

"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { bookingAPI } from "@/lib/booking";
import { paymentAPI } from "@/lib/payment";
import { PaymentError } from "@/types/payment";
import Header from "@/components/layout/Header";
import PaymentForm from "@/components/payment/PaymentForm";
import BookingPaymentSummary from "@/components/payment/BookingPaymentSummary";
import { Session } from "@/types/booking";
import {
  CreditCardIcon,
  ShieldCheckIcon,
  ClockIcon,
  ArrowLeftIcon,
} from "@heroicons/react/24/outline";

export default function BookingPaymentPage() {
  const router = useRouter();
  const params = useParams();
  const sessionId = params.id as string;

  const { isAuthenticated, user, tokens, isLoading } = useAuthStore();
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  // Helper function to safely format dates
  const formatSafeDate = (
    dateString: string | undefined,
    formatter: (date: Date) => string
  ) => {
    if (!dateString) return "Not available";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid date";
      return formatter(date);
    } catch (error) {
      console.error("Date formatting error:", error);
      return "Invalid date";
    }
  };

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (sessionId && tokens?.accessToken) {
      fetchSession();
    }
  }, [isAuthenticated, isLoading, sessionId, tokens, router]);

  const fetchSession = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("=== FRONTEND DEBUG ===");
      console.log("Session ID from URL:", sessionId);
      console.log("User data:", user);
      console.log("Access token exists:", !!tokens?.accessToken);
  

      const response = await bookingAPI.getBooking(
        sessionId,
        tokens!.accessToken
      );

      console.log("API Response:", response);

      // Check if session has counselor information
      const sessionData = response.data.session;
      if (!sessionData.counselorId && !sessionData.counselor) {
        setError(
          "This session is missing counselor information. Please go back to the counselor's page and create a new booking with proper counselor selection."
        );
        return;
      }

      setSession(sessionData);

      // Debug: Log session data and counselor information
      console.log("=== SESSION DATA DEBUG ===");
      console.log("Full session object:", sessionData);
      console.log("session.counselorId:", sessionData.counselorId);
      console.log("session.counselorId type:", typeof sessionData.counselorId);
      console.log("session.counselor:", sessionData.counselor);
      console.log("session.counselor?._id:", sessionData.counselor?._id);

      // Extract counselor ID properly
      const extractedCounselorId =
        typeof sessionData.counselorId === "string"
          ? sessionData.counselorId
          : (sessionData.counselorId as any)?._id ||
            sessionData.counselor?._id ||
            "";

      console.log(
        "Extracted counselorId for PaymentForm:",
        extractedCounselorId
      );

      // Extract counselor name properly
      const extractedCounselorName =
        (sessionData.counselorId as any)?.userId?.firstName &&
        (sessionData.counselorId as any)?.userId?.lastName
          ? `${(sessionData.counselorId as any).userId.firstName} ${
              (sessionData.counselorId as any).userId.lastName
            }`
          : sessionData.counselor?.user?.firstName &&
            sessionData.counselor?.user?.lastName
          ? `${sessionData.counselor.user.firstName} ${sessionData.counselor.user.lastName}`
          : "Counselor";

      console.log("Extracted counselor name:", extractedCounselorName);

      // If session is already paid, redirect to confirmation
      if (sessionData.status === "completed") {
        router.push(`/booking/${sessionId}/confirmation`);
      }
    } catch (err) {
      console.error("=== FRONTEND ERROR ===");
      console.error("Error details:", err);
      console.error(
        "Error message:",
        err instanceof Error ? err.message : "Unknown error"
      );

      setError(
        err instanceof Error ? err.message : "Failed to load session details"
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = async (paymentData: any) => {
    try {
      setPaymentProcessing(true);

      const response = await paymentAPI.verifyPayment(paymentData.reference);

      if (response.success) {
        router.push(`/booking/${sessionId}/confirmation`);
      } else {
        setError("Payment confirmation failed. Please contact support.");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Payment processing failed"
      );
    } finally {
      setPaymentProcessing(false);
    }
  };

  const handlePaymentError = (error: PaymentError) => {
    setError(error.message);
    setPaymentProcessing(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Payment Error
            </h1>
            <p className="text-gray-600 mb-6">
              {error || "Session details could not be loaded."}
            </p>
            <div className="space-x-4">
              <button
                onClick={() => router.push("/counselors")}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md font-medium"
              >
                Book New Session
              </button>
              <button
                onClick={() => router.push("/dashboard")}
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md font-medium"
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center text-purple-600 hover:text-purple-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Booking
          </button>

          <div className="flex items-center space-x-3">
            <CreditCardIcon className="h-8 w-8 text-purple-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Complete Your Payment
              </h1>
              <p className="text-gray-600">
                Secure payment for your counseling session
              </p>
            </div>
          </div>
        </div>

        {/* Payment Timeout Warning */}
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <ClockIcon className="h-5 w-5 text-yellow-600" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">
                Complete payment within 15 minutes
              </h3>
              <p className="text-sm text-yellow-700">
                Your session reservation will expire if payment is not completed
                within 15 minutes.
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Payment Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-3 mb-6">
                <ShieldCheckIcon className="h-6 w-6 text-green-600" />
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Secure Payment
                  </h2>
                  <p className="text-sm text-gray-600">
                    Your payment information is encrypted and secure
                  </p>
                </div>
              </div>

              <PaymentForm
                sessionId={sessionId}
                counselorId={
                  // Handle both string and populated object cases
                  typeof session.counselorId === "string"
                    ? session.counselorId
                    : (session.counselorId as any)?._id ||
                      session.counselor?._id ||
                      ""
                }
                amount={session.totalAmount || session.pricing.totalAmount}
                currency={
                  (session.currency || session.pricing.currency) as
                    | "NGN"
                    | "USD"
                }
                counselorName={
                  // Handle populated counselor data
                  (session.counselorId as any)?.userId?.firstName &&
                  (session.counselorId as any)?.userId?.lastName
                    ? `${(session.counselorId as any).userId.firstName} ${
                        (session.counselorId as any).userId.lastName
                      }`
                    : session.counselor?.user?.firstName &&
                      session.counselor?.user?.lastName
                    ? `${session.counselor.user.firstName} ${session.counselor.user.lastName}`
                    : "Counselor"
                }
                sessionDetails={{
                  date: formatSafeDate(session.scheduledAt, (date) =>
                    date.toLocaleDateString()
                  ),
                  time: formatSafeDate(session.scheduledAt, (date) =>
                    date.toLocaleTimeString()
                  ),
                  duration: session.duration,
                  type: session.type,
                }}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={() => router.push("/dashboard")}
              />
            </div>

            {/* Security Features */}
            <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-green-900 mb-2">
                🔒 Your Payment is Protected
              </h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• 256-bit SSL encryption</li>
                <li>• PCI DSS compliant payment processing</li>
                <li>• No card details stored on our servers</li>
                <li>• Secure payment gateway by Paystack</li>
                <li>• Full refund available for cancellations</li>
              </ul>
            </div>
          </div>

          {/* Right Column - Booking Summary */}
          <div className="lg:col-span-1">
            <BookingPaymentSummary session={session} />

            {/* Support */}
            <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-2">
                Need Help?
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                Having trouble with payment? Our support team is here to help.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <span>📧</span>
                  <span className="text-gray-600"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>📞</span>
                  <span className="text-gray-600">+234 (0) 123 456 7890</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>💬</span>
                  <button className="text-purple-600 hover:text-purple-700">
                    Live Chat Support
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <div className="text-red-600">⚠️</div>
              <div>
                <h3 className="text-sm font-medium text-red-800">
                  Payment Error
                </h3>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "../../../store/authStore";
import { counselorPortalAPI } from "../../../lib/counselorPortal";
import CounselorLayout from "../../../components/counselor/CounselorLayout";
import CounselorProfileForm from "../../../components/counselor/CounselorProfileForm";
import CounselorPricingForm from "../../../components/counselor/CounselorPricingForm";
import CounselorBankDetailsForm from "../../../components/counselor/CounselorBankDetailsForm";
import CounselorVerificationStatus from "../../../components/counselor/CounselorVerificationStatus";
import { Counselor } from "../../../types/counselor";
import {
  UserIcon,
  CurrencyDollarIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  CogIcon,
} from "@heroicons/react/24/outline";

export default function CounselorProfilePage() {
  const router = useRouter();
  const { isAuthenticated, user, tokens } = useAuthStore();
  const [counselor, setCounselor] = useState<Counselor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("profile");

  useEffect(() => {
    if (!isAuthenticated || user?.role !== "counselor") {
      router.push("/auth/login");
      return;
    }

    fetchCounselorProfile();
  }, [isAuthenticated, user, router]);

  const fetchCounselorProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await counselorPortalAPI.getMyProfile(
        tokens!.accessToken
      );
      setCounselor(response.data.counselor);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load profile");
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (data: Partial<Counselor>) => {
    try {
      const response = await counselorPortalAPI.updateProfile(
        data,
        tokens!.accessToken
      );
      setCounselor(response.data.counselor);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update profile");
    }
  };

  const handleBankDetailsUpdate = async (data: { bankDetails: any }) => {
    try {
      // Transform the data to fit the Counselor partial structure
      const profileData: Partial<Counselor> = {
        bankDetails: data.bankDetails,
      } as Partial<Counselor>;

      const response = await counselorPortalAPI.updateProfile(
        profileData,
        tokens!.accessToken
      );
      setCounselor(response.data.counselor);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to update bank details"
      );
    }
  };

  const tabs = [
    { id: "profile", name: "Profile", icon: UserIcon },
    { id: "pricing", name: "Pricing", icon: CurrencyDollarIcon },
    { id: "banking", name: "Banking", icon: BanknotesIcon },
    { id: "verification", name: "Verification", icon: ShieldCheckIcon },
    { id: "settings", name: "Settings", icon: CogIcon },
  ];

  if (loading) {
    return (
      <CounselorLayout>
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </CounselorLayout>
    );
  }

  if (!counselor) {
    return (
      <CounselorLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Profile not found
          </h3>
          <p className="text-gray-600">
            Unable to load your counselor profile.
          </p>
        </div>
      </CounselorLayout>
    );
  }

  return (
    <CounselorLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <UserIcon className="h-8 w-8 text-purple-600 mr-3" />
                Profile Settings
              </h1>
              <p className="mt-2 text-gray-600">
                Manage your counselor profile and account settings
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  counselor.verification.status === "approved"
                    ? "bg-green-100 text-green-800"
                    : counselor.verification.status === "pending"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                <ShieldCheckIcon className="h-4 w-4 mr-1" />
                {counselor.verification.status === "approved"
                  ? "Verified"
                  : counselor.verification.status === "pending"
                  ? "Pending Verification"
                  : "Not Verified"}
              </span>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Profile Content */}
        <div className="bg-white shadow rounded-lg">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? "border-purple-500 text-purple-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === "profile" && (
              <CounselorProfileForm
                counselor={counselor}
                onSave={handleProfileUpdate}
              />
            )}

            {activeTab === "pricing" && (
              <CounselorPricingForm
                counselor={counselor}
                onSave={handleProfileUpdate}
              />
            )}

            {activeTab === "banking" && (
              <CounselorBankDetailsForm
                bankDetails={undefined}
                onSave={handleBankDetailsUpdate}
              />
            )}

            {activeTab === "verification" && (
              <CounselorVerificationStatus
                verification={counselor.verification}
              />
            )}

            {activeTab === "settings" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Account Settings
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          Accepting New Clients
                        </h4>
                        <p className="text-sm text-gray-600">
                          Allow new clients to book sessions with you
                        </p>
                      </div>
                      <button
                        className={`${
                          counselor.settings.acceptingNewClients
                            ? "bg-purple-600"
                            : "bg-gray-200"
                        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
                      >
                        <span
                          className={`${
                            counselor.settings.acceptingNewClients
                              ? "translate-x-5"
                              : "translate-x-0"
                          } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                        />
                      </button>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          Auto-Accept Bookings
                        </h4>
                        <p className="text-sm text-gray-600">
                          Automatically accept booking requests without manual
                          approval
                        </p>
                      </div>
                      <button
                        className={`${
                          counselor.settings.autoAcceptBookings
                            ? "bg-purple-600"
                            : "bg-gray-200"
                        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
                      >
                        <span
                          className={`${
                            counselor.settings.autoAcceptBookings
                              ? "translate-x-5"
                              : "translate-x-0"
                          } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                        />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Danger Zone
                  </h3>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-red-900 mb-2">
                      Deactivate Counselor Account
                    </h4>
                    <p className="text-sm text-red-700 mb-4">
                      This will hide your profile and prevent new bookings. You
                      can reactivate later.
                    </p>
                    <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                      Deactivate Account
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </CounselorLayout>
  );
}

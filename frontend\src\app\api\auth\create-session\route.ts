import { NextRequest, NextResponse } from "next/server";
import { encrypt, SessionPayload } from "@/lib/session";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    const sessionData = await request.json();

    // Create session payload
    const sessionPayload: SessionPayload = {
      userId: sessionData.userId,
      email: sessionData.email,
      role: sessionData.role || "guest",
      isGuest: sessionData.isGuest || false,
      isEmailVerified: sessionData.isEmailVerified || false,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
    };

    // Encrypt and set session cookie
    const encryptedSession = await encrypt(sessionPayload);

    const cookieStore = await cookies();
    cookieStore.set("session", encryptedSession, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: "/",
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error creating session:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create session" },
      { status: 500 }
    );
  }
}

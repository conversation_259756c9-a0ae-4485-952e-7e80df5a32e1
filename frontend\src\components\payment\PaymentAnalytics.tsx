'use client';

import { useState, useEffect } from 'react';
import { paymentAPI } from '@/lib/payment';
import { useAuthStore } from '@/store/authStore';
import { 
  CurrencyDollarIcon, 
  TrendingUpIcon, 
  TrendingDownIcon,
  ChartBarIcon,
  CreditCardIcon,
  BanknotesIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface PaymentAnalytics {
  totalRevenue: number;
  totalTransactions: number;
  averageTransactionValue: number;
  successRate: number;
  monthlyGrowth: number;
  currency: string;
  paymentMethods: {
    method: string;
    count: number;
    percentage: number;
    revenue: number;
  }[];
  monthlyData: {
    month: string;
    revenue: number;
    transactions: number;
    successRate: number;
  }[];
  recentTrends: {
    period: string;
    revenue: number;
    change: number;
    transactions: number;
  }[];
}

interface PaymentAnalyticsProps {
  period?: 'week' | 'month' | 'quarter' | 'year';
  showDetails?: boolean;
}

export default function PaymentAnalytics({ 
  period = 'month', 
  showDetails = true 
}: PaymentAnalyticsProps) {
  const { tokens } = useAuthStore();
  const [analytics, setAnalytics] = useState<PaymentAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  useEffect(() => {
    if (tokens?.accessToken) {
      fetchAnalytics();
    }
  }, [tokens, selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await paymentAPI.getPaymentAnalytics(selectedPeriod, tokens!.accessToken);
      setAnalytics(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load payment analytics');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${(amount / 100).toLocaleString()}`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'card':
        return <CreditCardIcon className="h-5 w-5" />;
      case 'bank':
      case 'transfer':
        return <BanknotesIcon className="h-5 w-5" />;
      default:
        return <CreditCardIcon className="h-5 w-5" />;
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUpIcon className="h-4 w-4 text-green-500" />;
    } else if (change < 0) {
      return <TrendingDownIcon className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-sm text-red-700">
          {error || 'Failed to load payment analytics'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Payment Analytics</h2>
        <div className="flex items-center space-x-2">
          <CalendarIcon className="h-5 w-5 text-gray-400" />
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-sm"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analytics.totalRevenue, analytics.currency)}
              </p>
              <div className="flex items-center mt-1">
                {getChangeIcon(analytics.monthlyGrowth)}
                <span className={`text-sm ml-1 ${getChangeColor(analytics.monthlyGrowth)}`}>
                  {analytics.monthlyGrowth > 0 ? '+' : ''}{formatPercentage(analytics.monthlyGrowth)}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Transactions</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.totalTransactions.toLocaleString()}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Avg: {formatCurrency(analytics.averageTransactionValue, analytics.currency)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-bold text-sm">%</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPercentage(analytics.successRate)}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Payment success
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUpIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Growth Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.monthlyGrowth > 0 ? '+' : ''}{formatPercentage(analytics.monthlyGrowth)}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                vs last period
              </p>
            </div>
          </div>
        </div>
      </div>

      {showDetails && (
        <>
          {/* Payment Methods Breakdown */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Payment Methods
            </h3>
            <div className="space-y-4">
              {analytics.paymentMethods.map((method) => (
                <div key={method.method} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 text-gray-600">
                      {getMethodIcon(method.method)}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 capitalize">
                        {method.method}
                      </p>
                      <p className="text-sm text-gray-600">
                        {method.count} transactions
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(method.revenue, analytics.currency)}
                    </p>
                    <p className="text-sm text-gray-600">
                      {formatPercentage(method.percentage)}
                    </p>
                  </div>
                  <div className="w-24 bg-gray-200 rounded-full h-2 ml-4">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: `${method.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Monthly Trends */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Revenue Trends
            </h3>
            <div className="space-y-4">
              {analytics.monthlyData.map((data, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {data.month}
                    </p>
                    <p className="text-sm text-gray-600">
                      {data.transactions} transactions • {formatPercentage(data.successRate)} success
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(data.revenue, analytics.currency)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Trends */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Performance
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {analytics.recentTrends.map((trend, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">
                      {trend.period}
                    </h4>
                    <div className="flex items-center space-x-1">
                      {getChangeIcon(trend.change)}
                      <span className={`text-sm ${getChangeColor(trend.change)}`}>
                        {trend.change > 0 ? '+' : ''}{formatPercentage(trend.change)}
                      </span>
                    </div>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency(trend.revenue, analytics.currency)}
                  </p>
                  <p className="text-sm text-gray-600">
                    {trend.transactions} transactions
                  </p>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

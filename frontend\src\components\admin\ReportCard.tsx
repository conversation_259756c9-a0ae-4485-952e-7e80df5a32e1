"use client";

import { AdminReport } from "@/types/admin";

interface ReportCardProps {
  report: AdminReport;
  onAction: (reportId: string, action: string, reason?: string) => void;
}

export default function ReportCard({ report, onAction }: ReportCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "resolved":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "investigating":
        return "bg-blue-100 text-blue-800";
      case "dismissed":
        return "bg-gray-100 text-gray-800";
      case "escalated":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className="text-lg font-medium text-gray-900">
              {report.type.charAt(0).toUpperCase() + report.type.slice(1)}{" "}
              Report
            </h3>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                report.status
              )}`}
            >
              {report.status}
            </span>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(
                report.priority
              )}`}
            >
              {report.priority}
            </span>
          </div>
          <p className="text-sm text-gray-600 mb-3">{report.description}</p>

          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <span className="font-medium">Category:</span> {report.category}
            </div>
            <div>
              <span className="font-medium">Reporter:</span>{" "}
              {report.reporter?.email || "Anonymous"}
            </div>
            <div>
              <span className="font-medium">Created:</span>{" "}
              {new Date(report.createdAt).toLocaleDateString()}
            </div>
            {report.assignedTo && (
              <div>
                <span className="font-medium">Assigned to:</span>{" "}
                {report.assignedTo}
              </div>
            )}
          </div>

          {report.resolution && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <h4 className="font-medium text-gray-900 mb-2">Resolution</h4>
              <p className="text-sm text-gray-700 mb-2">
                {report.resolution.description}
              </p>
              <div className="text-xs text-gray-500">
                Resolved by {report.resolution.resolvedBy} on{" "}
                {new Date(report.resolution.resolvedAt).toLocaleDateString()}
              </div>
            </div>
          )}
        </div>

        {report.status === "pending" && (
          <div className="flex flex-col space-y-2 ml-4">
            <button
              onClick={() => onAction(report._id, "investigate")}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Investigate
            </button>
            <button
              onClick={() => onAction(report._id, "resolve")}
              className="px-3 py-1 text-xs bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Resolve
            </button>
            <button
              onClick={() => onAction(report._id, "dismiss")}
              className="px-3 py-1 text-xs bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Dismiss
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

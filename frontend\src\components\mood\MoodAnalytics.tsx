"use client";

import { useState, useEffect } from 'react';
import { moodAPI, MoodAnalytics as MoodAnalyticsType } from '@/lib/mood';

interface MoodAnalyticsProps {
  token: string;
}

export default function MoodAnalytics({ token }: MoodAnalyticsProps) {
  const [analytics, setAnalytics] = useState<MoodAnalyticsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  useEffect(() => {
    loadAnalytics();
  }, [period, token]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const data = await moodAPI.getMoodAnalytics(period, token);
      setAnalytics(data);
    } catch (error) {
      console.error('Error loading mood analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMoodColor = (mood: number) => {
    if (mood >= 4.5) return 'text-green-600 bg-green-100';
    if (mood >= 3.5) return 'text-green-500 bg-green-50';
    if (mood >= 2.5) return 'text-yellow-500 bg-yellow-50';
    if (mood >= 1.5) return 'text-orange-500 bg-orange-50';
    return 'text-red-500 bg-red-50';
  };

  const getEnergyColor = (energy: number) => {
    if (energy >= 4.5) return 'text-green-600';
    if (energy >= 3.5) return 'text-green-500';
    if (energy >= 2.5) return 'text-yellow-500';
    if (energy >= 1.5) return 'text-orange-500';
    return 'text-red-500';
  };

  const getAnxietyColor = (anxiety: number) => {
    if (anxiety >= 4.5) return 'text-red-600';
    if (anxiety >= 3.5) return 'text-orange-500';
    if (anxiety >= 2.5) return 'text-yellow-500';
    if (anxiety >= 1.5) return 'text-green-500';
    return 'text-green-600';
  };

  const formatPeriodLabel = (period: string) => {
    switch (period) {
      case '7d': return 'Last 7 days';
      case '30d': return 'Last 30 days';
      case '90d': return 'Last 90 days';
      case '1y': return 'Last year';
      default: return 'Last 30 days';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded-lg mb-8"></div>
          <div className="h-48 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">📊</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No data available</h3>
        <p className="text-gray-600">
          Start tracking your mood to see analytics and insights.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Analytics</h2>
        <div className="flex space-x-2">
          {[
            { key: '7d', label: '7 days' },
            { key: '30d', label: '30 days' },
            { key: '90d', label: '90 days' },
            { key: '1y', label: '1 year' }
          ].map((option) => (
            <button
              key={option.key}
              onClick={() => setPeriod(option.key as any)}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                period === option.key
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-purple-100 hover:text-purple-600'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Mood</p>
              <p className={`text-2xl font-bold ${getMoodColor(analytics.summary.averageMood).split(' ')[0]}`}>
                {analytics.summary.averageMood.toFixed(1)}
              </p>
            </div>
            <div className={`p-3 rounded-full ${getMoodColor(analytics.summary.averageMood)}`}>
              😊
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Energy</p>
              <p className={`text-2xl font-bold ${getEnergyColor(analytics.summary.averageEnergy)}`}>
                {analytics.summary.averageEnergy.toFixed(1)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              ⚡
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Anxiety</p>
              <p className={`text-2xl font-bold ${getAnxietyColor(analytics.summary.averageAnxiety)}`}>
                {analytics.summary.averageAnxiety.toFixed(1)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              🧘
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Entries</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.summary.totalEntries}
              </p>
            </div>
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              📝
            </div>
          </div>
        </div>
      </div>

      {/* Trends Chart */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Mood Trends - {formatPeriodLabel(period)}
        </h3>
        
        {analytics.trends.length > 0 ? (
          <div className="space-y-4">
            {/* Simple line chart representation */}
            <div className="h-64 flex items-end space-x-2 overflow-x-auto">
              {analytics.trends.map((trend, index) => {
                const moodHeight = (trend.avgMood / 5) * 100;
                const energyHeight = (trend.avgEnergy / 5) * 100;
                const anxietyHeight = (trend.avgAnxiety / 5) * 100;
                
                return (
                  <div key={trend._id} className="flex flex-col items-center min-w-0 flex-1">
                    <div className="flex space-x-1 mb-2 h-48">
                      {/* Mood bar */}
                      <div className="w-4 bg-gray-100 rounded-t flex flex-col justify-end">
                        <div 
                          className="bg-purple-500 rounded-t transition-all duration-300"
                          style={{ height: `${moodHeight}%` }}
                          title={`Mood: ${trend.avgMood.toFixed(1)}`}
                        ></div>
                      </div>
                      {/* Energy bar */}
                      <div className="w-4 bg-gray-100 rounded-t flex flex-col justify-end">
                        <div 
                          className="bg-yellow-500 rounded-t transition-all duration-300"
                          style={{ height: `${energyHeight}%` }}
                          title={`Energy: ${trend.avgEnergy.toFixed(1)}`}
                        ></div>
                      </div>
                      {/* Anxiety bar (inverted - lower is better) */}
                      <div className="w-4 bg-gray-100 rounded-t flex flex-col justify-end">
                        <div 
                          className="bg-blue-500 rounded-t transition-all duration-300"
                          style={{ height: `${100 - anxietyHeight}%` }}
                          title={`Anxiety: ${trend.avgAnxiety.toFixed(1)}`}
                        ></div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 text-center">
                      {new Date(trend._id).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
            
            {/* Legend */}
            <div className="flex justify-center space-x-6 text-sm">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-purple-500 rounded mr-2"></div>
                <span>Mood</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
                <span>Energy</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                <span>Calm (Low Anxiety)</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No trend data available for this period.</p>
          </div>
        )}
      </div>

      {/* Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Top Tags */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Most Common Tags</h3>
          {analytics.insights.topTags.length > 0 ? (
            <div className="space-y-3">
              {analytics.insights.topTags.map(([tag, count], index) => (
                <div key={tag} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 capitalize">{tag}</span>
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${(count / Math.max(...analytics.insights.topTags.map(([,c]) => c))) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No tags data available.</p>
          )}
        </div>

        {/* Top Triggers */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Common Triggers</h3>
          {analytics.insights.topTriggers.length > 0 ? (
            <div className="space-y-3">
              {analytics.insights.topTriggers.map(([trigger, count], index) => (
                <div key={trigger} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 capitalize">{trigger}</span>
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${(count / Math.max(...analytics.insights.topTriggers.map(([,c]) => c))) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No triggers data available.</p>
          )}
        </div>

        {/* Top Activities */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Common Activities</h3>
          {analytics.insights.topActivities.length > 0 ? (
            <div className="space-y-3">
              {analytics.insights.topActivities.map(([activity, count], index) => (
                <div key={activity} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 capitalize">{activity}</span>
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${(count / Math.max(...analytics.insights.topActivities.map(([,c]) => c))) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No activities data available.</p>
          )}
        </div>
      </div>
    </div>
  );
}

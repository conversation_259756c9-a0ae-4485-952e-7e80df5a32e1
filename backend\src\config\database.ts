import mongoose from "mongoose";
import { logger } from "@/utils/logger";
import { initializeModels, seedDefaultData } from "@/models";

export const connectDB = async (): Promise<void> => {
  try {
    const mongoURI =
      process.env.NODE_ENV === "test"
        ? process.env.MONGODB_TEST_URI
        : process.env.MONGODB_URI;

    if (!mongoURI) {
      logger.warn("MongoDB URI is not defined in environment variables. Using in-memory database for development.");
      // Use in-memory MongoDB for development
      const { MongoMemoryServer } = require('mongodb-memory-server');
      const mongod = new MongoMemoryServer();
      await mongod.start();
      const uri = mongod.getUri();

      const conn = await mongoose.connect(uri);

      logger.info(`MongoDB Connected (In-Memory): ${conn.connection.host}`);
    } else {
      const conn = await mongoose.connect(mongoURI);

      logger.info(`MongoDB Connected: ${conn.connection.host}`);
    }

    // Initialize all models
    initializeModels();

    // Seed default data in development
    if (process.env.NODE_ENV === "development") {
      try {
        await seedDefaultData();
      } catch (seedError) {
        logger.warn("Warning: Could not seed default data:", seedError);
      }
    }

    // Handle connection events
    mongoose.connection.on("error", (err) => {
      logger.error("MongoDB connection error:", err);
    });

    mongoose.connection.on("disconnected", () => {
      logger.warn("MongoDB disconnected");
    });

    mongoose.connection.on("reconnected", () => {
      logger.info("MongoDB reconnected");
    });
  } catch (error) {
    logger.error("Error connecting to MongoDB:", error);
    process.exit(1);
  }
};

export const disconnectDB = async (): Promise<void> => {
  try {
    await mongoose.connection.close();
    logger.info("MongoDB connection closed");
  } catch (error) {
    logger.error("Error closing MongoDB connection:", error);
  }
};

// Database health check
export const checkDBHealth = async (): Promise<boolean> => {
  try {
    const state = mongoose.connection.readyState;
    return state === 1; // 1 = connected
  } catch (error) {
    logger.error("Database health check failed:", error);
    return false;
  }
};

// Get database statistics
export const getDBStats = async () => {
  try {
    const db = mongoose.connection.db;
    if (!db) {
      throw new Error("Database connection not available");
    }

    const stats = await db.stats();
    const collections = await db.listCollections().toArray();

    return {
      isConnected: mongoose.connection.readyState === 1,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name,
      collections: collections.length,
      dataSize: stats.dataSize,
      storageSize: stats.storageSize,
      indexes: stats.indexes,
      objects: stats.objects,
    };
  } catch (error) {
    logger.error("Error getting database stats:", error);
    throw error;
  }
};

import mongoose, { Document, Schema } from 'mongoose';

export interface IMessage extends Document {
  _id: mongoose.Types.ObjectId;
  chatRoomId: mongoose.Types.ObjectId;
  senderId?: mongoose.Types.ObjectId; // undefined for anonymous users
  anonymousSenderId?: string; // for guest users
  senderDisplayName: string;
  content: {
    text?: string;
    type: 'text' | 'image' | 'file' | 'system' | 'emoji';
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  };
  mentions: mongoose.Types.ObjectId[];
  replyTo?: mongoose.Types.ObjectId; // reference to another message
  reactions: {
    emoji: string;
    users: {
      userId?: mongoose.Types.ObjectId;
      anonymousId?: string;
      addedAt: Date;
    }[];
    count: number;
  }[];
  isEdited: boolean;
  editHistory: {
    previousContent: string;
    editedAt: Date;
  }[];
  moderation: {
    isReported: boolean;
    reportCount: number;
    reports: {
      reportedBy: mongoose.Types.ObjectId;
      reason: 'spam' | 'harassment' | 'inappropriate' | 'off-topic' | 'other';
      description?: string;
      reportedAt: Date;
    }[];
    isHidden: boolean;
    hiddenBy?: mongoose.Types.ObjectId;
    hiddenAt?: Date;
    hiddenReason?: string;
    isDeleted: boolean;
    deletedBy?: mongoose.Types.ObjectId;
    deletedAt?: Date;
  };
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    location?: string;
    deviceType?: 'mobile' | 'desktop' | 'tablet';
  };
  readBy: {
    userId?: mongoose.Types.ObjectId;
    anonymousId?: string;
    readAt: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

const messageSchema = new Schema<IMessage>({
  chatRoomId: {
    type: Schema.Types.ObjectId,
    ref: 'ChatRoom',
    required: true
  },
  senderId: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  anonymousSenderId: {
    type: String,
    validate: {
      validator: function(this: IMessage) {
        return !!(this.senderId || this.anonymousSenderId);
      },
      message: 'Either senderId or anonymousSenderId must be provided'
    }
  },
  senderDisplayName: {
    type: String,
    required: true,
    trim: true,
    maxlength: [50, 'Display name cannot exceed 50 characters']
  },
  content: {
    text: {
      type: String,
      maxlength: [2000, 'Message cannot exceed 2000 characters']
    },
    type: {
      type: String,
      enum: ['text', 'image', 'file', 'system', 'emoji'],
      required: true,
      default: 'text'
    },
    fileUrl: String,
    fileName: String,
    fileSize: {
      type: Number,
      max: [10 * 1024 * 1024, 'File size cannot exceed 10MB'] // 10MB limit
    },
    mimeType: String
  },
  mentions: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  replyTo: {
    type: Schema.Types.ObjectId,
    ref: 'Message'
  },
  reactions: [{
    emoji: {
      type: String,
      required: true,
      maxlength: [10, 'Emoji cannot exceed 10 characters']
    },
    users: [{
      userId: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      anonymousId: String,
      addedAt: {
        type: Date,
        default: Date.now
      }
    }],
    count: {
      type: Number,
      default: 0,
      min: 0
    }
  }],
  isEdited: {
    type: Boolean,
    default: false
  },
  editHistory: [{
    previousContent: {
      type: String,
      required: true
    },
    editedAt: {
      type: Date,
      default: Date.now
    }
  }],
  moderation: {
    isReported: {
      type: Boolean,
      default: false
    },
    reportCount: {
      type: Number,
      default: 0,
      min: 0
    },
    reports: [{
      reportedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      reason: {
        type: String,
        enum: ['spam', 'harassment', 'inappropriate', 'off-topic', 'other'],
        required: true
      },
      description: {
        type: String,
        maxlength: [500, 'Report description cannot exceed 500 characters']
      },
      reportedAt: {
        type: Date,
        default: Date.now
      }
    }],
    isHidden: {
      type: Boolean,
      default: false
    },
    hiddenBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    hiddenAt: Date,
    hiddenReason: String,
    isDeleted: {
      type: Boolean,
      default: false
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    deletedAt: Date
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    location: String,
    deviceType: {
      type: String,
      enum: ['mobile', 'desktop', 'tablet']
    }
  },
  readBy: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    anonymousId: String,
    readAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
messageSchema.index({ chatRoomId: 1, createdAt: -1 });
messageSchema.index({ senderId: 1, createdAt: -1 });
messageSchema.index({ 'moderation.isReported': 1 });
messageSchema.index({ 'moderation.isHidden': 1 });
messageSchema.index({ 'moderation.isDeleted': 1 });
messageSchema.index({ mentions: 1 });
messageSchema.index({ replyTo: 1 });

// Compound indexes for efficient queries
messageSchema.index({ chatRoomId: 1, 'moderation.isDeleted': 1, createdAt: -1 });

// Virtual to populate sender details
messageSchema.virtual('sender', {
  ref: 'User',
  localField: 'senderId',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate chat room details
messageSchema.virtual('chatRoom', {
  ref: 'ChatRoom',
  localField: 'chatRoomId',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate reply message details
messageSchema.virtual('replyMessage', {
  ref: 'Message',
  localField: 'replyTo',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to validate content
messageSchema.pre('save', function(next) {
  if (this.content.type === 'text' && !this.content.text) {
    return next(new Error('Text content is required for text messages'));
  }
  
  if (this.content.type === 'file' && !this.content.fileUrl) {
    return next(new Error('File URL is required for file messages'));
  }
  
  next();
});

// Method to add reaction
messageSchema.methods.addReaction = function(emoji: string, userId?: string, anonymousId?: string) {
  let reaction = this.reactions.find((r: any) => r.emoji === emoji);
  
  if (!reaction) {
    reaction = {
      emoji,
      users: [],
      count: 0
    };
    this.reactions.push(reaction);
  }
  
  // Check if user already reacted with this emoji
  const existingReaction = reaction.users.find((u: any) => 
    (userId && u.userId?.toString() === userId) || 
    (anonymousId && u.anonymousId === anonymousId)
  );
  
  if (!existingReaction) {
    reaction.users.push({
      userId: userId ? userId as any : undefined,
      anonymousId,
      addedAt: new Date()
    });
    reaction.count += 1;
  }
};

// Method to remove reaction
messageSchema.methods.removeReaction = function(emoji: string, userId?: string, anonymousId?: string) {
  const reaction = this.reactions.find((r: any) => r.emoji === emoji);
  
  if (reaction) {
    const userIndex = reaction.users.findIndex((u: any) => 
      (userId && u.userId?.toString() === userId) || 
      (anonymousId && u.anonymousId === anonymousId)
    );
    
    if (userIndex > -1) {
      reaction.users.splice(userIndex, 1);
      reaction.count = Math.max(0, reaction.count - 1);
      
      // Remove reaction if no users left
      if (reaction.count === 0) {
        const reactionIndex = this.reactions.findIndex((r: any) => r.emoji === emoji);
        this.reactions.splice(reactionIndex, 1);
      }
    }
  }
};

export const Message = mongoose.model<IMessage>('Message', messageSchema);

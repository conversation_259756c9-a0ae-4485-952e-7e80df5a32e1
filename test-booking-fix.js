const testBookingAPI = async () => {
  const bookingData = {
    counselorId: "689de8f0a7536fe4a2f9130f",
    sessionType: "individual",
    duration: 60,
    isUrgent: true,
    notes: "i need therapy",
    preferredLanguage: "en",
    scheduledAt: "2025-08-15T10:00:00.000Z",
    // userId will be added by the backend controller from auth
  };

  try {
    const response = await fetch("http://localhost:5000/api/sessions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // You'll need to add authentication headers for the actual test
        Authorization: "Bearer YOUR_TOKEN_HERE",
      },
      body: JSON.stringify(bookingData),
    });

    const result = await response.json();
    console.log("Booking API Response:", result);
    console.log("Status:", response.status);
  } catch (error) {
    console.error("Error testing booking API:", error);
  }
};

console.log("Test booking data structure:");
console.log({
  counselorId: "689de8f0a7536fe4a2f9130f",
  sessionType: "individual",
  duration: 60,
  isUrgent: true,
  notes: "i need therapy",
  preferredLanguage: "en",
  scheduledAt: "2025-08-15T10:00:00.000Z",
});

console.log(
  "\nThe scheduledAt field will now be properly converted to a Date object in the backend controller."
);
console.log('This should fix the "toTimeString is not a function" error.');

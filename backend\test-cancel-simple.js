const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testCancellation() {
  try {
    console.log('🧪 Testing Session Cancellation API (Simple Test)...\n');

    // Authenticate once
    console.log('1. Authenticating user...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Authentication failed');
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
      return;
    }

    const authToken = loginResponse.data.data.tokens.accessToken;
    console.log('✅ Authentication successful');
    console.log('Token (first 50 chars):', authToken.substring(0, 50) + '...');

    // Test cancellation
    const sessionId = '689e5af766e30bf744d47aeb';
    console.log(`\n2. Testing cancellation for session: ${sessionId}`);
    
    const cancelResponse = await axios.put(
      `${BASE_URL}/sessions/${sessionId}/cancel`,
      {
        reason: 'Testing session cancellation API'
      },
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Session cancellation successful!');
    console.log('Response:', JSON.stringify(cancelResponse.data, null, 2));

  } catch (error) {
    console.log('❌ Error occurred:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Headers:', error.response.headers);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
      
      // If it's an authorization error, let's debug further
      if (error.response.status === 403) {
        console.log('\n🔍 Authorization Debug Info:');
        console.log('This suggests the user ID comparison is failing in the backend.');
        console.log('The session belongs to user 687fe3b739da96aef19699d3');
        console.log('The authenticated user is 687fe3b739da96aef19699d3');
        console.log('These should match, so there might be a type conversion issue.');
      }
    } else {
      console.log('Error:', error.message);
    }
  }
}

testCancellation();

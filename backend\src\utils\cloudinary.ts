import { v2 as cloudinary } from "cloudinary";
import { logger } from "@/utils/logger";
import { createError } from "@/middleware/errorHandler";

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export interface UploadOptions {
  folder?: string;
  transformation?: any[];
  format?: string;
  quality?: string | number;
  width?: number;
  height?: number;
  crop?: string;
}

/**
 * Upload file to Cloudinary
 */
export const uploadToCloudinary = async (
  file: Express.Multer.File,
  folder: string = "theramea",
  options: UploadOptions = {}
): Promise<string> => {
  try {
    if (!process.env.CLOUDINARY_CLOUD_NAME) {
      logger.warn("Cloudinary not configured, returning placeholder URL");
      return "https://via.placeholder.com/150";
    }

    const uploadOptions = {
      folder: `theramea/${folder}`,
      resource_type: "auto" as const,
      format: options.format || "auto",
      quality: options.quality || "auto",
      ...options,
    };

    // Convert buffer to base64 data URI
    const dataUri = `data:${file.mimetype};base64,${file.buffer.toString(
      "base64"
    )}`;

    const result = await cloudinary.uploader.upload(dataUri, uploadOptions);

    logger.info(`File uploaded to Cloudinary: ${result.public_id}`);
    return result.secure_url;
  } catch (error) {
    logger.error("Cloudinary upload error:", error);
    throw createError("Failed to upload file", 500);
  }
};

/**
 * Upload profile picture with optimizations
 */
export const uploadProfilePicture = async (
  file: Express.Multer.File
): Promise<string> => {
  return uploadToCloudinary(file, "profile-pictures", {
    width: 400,
    height: 400,
    crop: "fill",
    quality: "auto:good",
    format: "auto",
  });
};

/**
 * Upload resource media (videos, images, documents)
 */
export const uploadResourceMedia = async (
  file: Express.Multer.File,
  type: "image" | "video" | "document"
): Promise<string> => {
  const options: UploadOptions = {
    quality: "auto:good",
  };

  if (type === "image") {
    options.width = 1200;
    options.height = 800;
    options.crop = "limit";
  } else if (type === "video") {
    options.quality = "auto:low";
  }

  return uploadToCloudinary(file, `resources/${type}s`, options);
};

/**
 * Delete file from Cloudinary
 */
export const deleteFromCloudinary = async (imageUrl: string): Promise<void> => {
  try {
    if (!process.env.CLOUDINARY_CLOUD_NAME) {
      logger.warn("Cloudinary not configured, skipping deletion");
      return;
    }

    // Extract public ID from URL
    const publicId = extractPublicIdFromUrl(imageUrl);

    if (!publicId) {
      logger.warn("Could not extract public ID from URL:", imageUrl);
      return;
    }

    await cloudinary.uploader.destroy(publicId);
    logger.info(`File deleted from Cloudinary: ${publicId}`);
  } catch (error) {
    logger.error("Cloudinary deletion error:", error);
    throw createError("Failed to delete file", 500);
  }
};

/**
 * Extract public ID from Cloudinary URL
 */
const extractPublicIdFromUrl = (url: string): string | null => {
  try {
    const regex = /\/v\d+\/(.+)\./;
    const match = url.match(regex);
    return match ? match[1] : null;
  } catch (error) {
    logger.error("Error extracting public ID from URL:", error);
    return null;
  }
};

/**
 * Generate transformation URL for existing image
 */
export const generateTransformationUrl = (
  publicId: string,
  transformations: any[]
): string => {
  try {
    return cloudinary.url(publicId, {
      transformation: transformations,
      secure: true,
    });
  } catch (error) {
    logger.error("Error generating transformation URL:", error);
    throw createError("Failed to generate transformation URL", 500);
  }
};

/**
 * Get optimized image URL with different sizes
 */
export const getOptimizedImageUrls = (imageUrl: string) => {
  const publicId = extractPublicIdFromUrl(imageUrl);

  if (!publicId) {
    return {
      thumbnail: imageUrl,
      small: imageUrl,
      medium: imageUrl,
      large: imageUrl,
      original: imageUrl,
    };
  }

  return {
    thumbnail: cloudinary.url(publicId, {
      width: 150,
      height: 150,
      crop: "fill",
      quality: "auto:low",
      secure: true,
    }),
    small: cloudinary.url(publicId, {
      width: 300,
      height: 300,
      crop: "limit",
      quality: "auto:good",
      secure: true,
    }),
    medium: cloudinary.url(publicId, {
      width: 600,
      height: 600,
      crop: "limit",
      quality: "auto:good",
      secure: true,
    }),
    large: cloudinary.url(publicId, {
      width: 1200,
      height: 1200,
      crop: "limit",
      quality: "auto:good",
      secure: true,
    }),
    original: imageUrl,
  };
};

/**
 * Validate file type and size
 */
export const validateFile = (
  file: Express.Multer.File,
  allowedTypes: string[],
  maxSize: number = 5 * 1024 * 1024 // 5MB default
): void => {
  // Check file type
  if (!allowedTypes.includes(file.mimetype)) {
    throw createError(
      `Invalid file type. Allowed types: ${allowedTypes.join(", ")}`,
      400
    );
  }

  // Check file size
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024);
    throw createError(`File size too large. Maximum size: ${maxSizeMB}MB`, 400);
  }
};

/**
 * Common file type constants
 */
export const FILE_TYPES = {
  IMAGES: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  VIDEOS: ["video/mp4", "video/mpeg", "video/quicktime", "video/webm"],
  DOCUMENTS: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ],
  AUDIO: ["audio/mpeg", "audio/wav", "audio/ogg"],
};

/**
 * File size constants
 */
export const FILE_SIZE_LIMITS = {
  PROFILE_PICTURE: 2 * 1024 * 1024, // 2MB
  RESOURCE_IMAGE: 5 * 1024 * 1024, // 5MB
  RESOURCE_VIDEO: 50 * 1024 * 1024, // 50MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  AUDIO: 20 * 1024 * 1024, // 20MB
  IMAGE: 5 * 1024 * 1024, // 5MB (alias for RESOURCE_IMAGE)
  VIDEO: 50 * 1024 * 1024, // 50MB (alias for RESOURCE_VIDEO)
};

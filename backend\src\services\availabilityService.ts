import { Counselor } from "@/models/Counselor";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";

export interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
  sessionId?: string;
}

export interface AvailabilitySlot {
  date: string;
  dayOfWeek: string;
  slots: TimeSlot[];
}

export interface AvailabilityQuery {
  counselorId: string;
  startDate: Date;
  endDate: Date;
  duration?: number;
  timezone?: string;
}

export class AvailabilityService {
  /**
   * Get available time slots for a counselor within a date range
   */
  static async getAvailableSlots(
    query: AvailabilityQuery
  ): Promise<AvailabilitySlot[]> {
    try {
      const {
        counselorId,
        startDate,
        endDate,
        duration = 60,
        timezone = "Africa/Lagos",
      } = query;

      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      if (counselor.verification.status !== "approved") {
        throw createError("Counselor is not approved", 400);
      }

      if (!counselor.settings.acceptingNewClients) {
        throw createError("Counselor is not accepting new clients", 400);
      }

      const availabilitySlots: AvailabilitySlot[] = [];
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const dayOfWeek = this.getDayOfWeek(currentDate);
        const dateString = currentDate.toISOString().split("T")[0];

        // Check if date is in unavailable dates
        const isUnavailable = counselor.availability.unavailableDates.some(
          (unavailableDate) =>
            unavailableDate.toISOString().split("T")[0] === dateString
        );

        if (!isUnavailable) {
          const daySchedule = counselor.availability.schedule.get(dayOfWeek);

          if (daySchedule && daySchedule.isAvailable) {
            const slots = await this.generateTimeSlots(
              counselorId,
              currentDate,
              daySchedule.timeSlots,
              duration
            );

            if (slots.length > 0) {
              availabilitySlots.push({
                date: dateString,
                dayOfWeek,
                slots,
              });
            }
          }
        }

        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return availabilitySlots;
    } catch (error) {
      logger.error("Get available slots error:", error);
      throw error;
    }
  }

  /**
   * Check if a specific time slot is available
   */
  static async isSlotAvailable(
    counselorId: string,
    date: Date,
    startTime: string,
    duration: number
  ): Promise<boolean> {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        return false;
      }

      // Check if counselor is approved and accepting clients
      if (
        counselor.verification.status !== "approved" ||
        !counselor.settings.acceptingNewClients
      ) {
        return false;
      }

      // Check if date is unavailable
      const dateString = date.toISOString().split("T")[0];
      const isUnavailable = counselor.availability.unavailableDates.some(
        (unavailableDate) =>
          unavailableDate.toISOString().split("T")[0] === dateString
      );

      if (isUnavailable) {
        return false;
      }

      // Check day schedule
      const dayOfWeek = this.getDayOfWeek(date);
      const daySchedule = counselor.availability.schedule.get(dayOfWeek);

      if (!daySchedule || !daySchedule.isAvailable) {
        return false;
      }

      // Check if time falls within available slots
      const isWithinSchedule = daySchedule.timeSlots.some((slot) => {
        const slotStart = this.timeToMinutes(slot.startTime);
        const slotEnd = this.timeToMinutes(slot.endTime);
        const requestStart = this.timeToMinutes(startTime);
        const requestEnd = requestStart + duration;

        return requestStart >= slotStart && requestEnd <= slotEnd;
      });

      if (!isWithinSchedule) {
        return false;
      }

      // TODO: Check against existing bookings
      // This would require querying the Session model
      // const existingBookings = await Session.find({
      //   counselorId,
      //   scheduledAt: {
      //     $gte: new Date(date.toISOString().split('T')[0] + 'T' + startTime),
      //     $lt: new Date(date.toISOString().split('T')[0] + 'T' + this.minutesToTime(this.timeToMinutes(startTime) + duration))
      //   },
      //   status: { $in: ['scheduled', 'in-progress'] }
      // });

      // return existingBookings.length === 0;

      return true; // For now, assume available if within schedule
    } catch (error) {
      logger.error("Check slot availability error:", error);
      return false;
    }
  }

  /**
   * Block time slots (for counselor to mark unavailable)
   */
  static async blockTimeSlots(
    counselorId: string,
    dates: Date[],
    reason?: string
  ): Promise<void> {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      // Add dates to unavailable dates
      const newUnavailableDates = [...counselor.availability.unavailableDates];

      dates.forEach((date) => {
        const dateString = date.toISOString().split("T")[0];
        const existingDate = newUnavailableDates.find(
          (d) => d.toISOString().split("T")[0] === dateString
        );

        if (!existingDate) {
          newUnavailableDates.push(date);
        }
      });

      counselor.availability.unavailableDates = newUnavailableDates;
      await counselor.save();

      logger.info(
        `Time slots blocked for counselor ${counselorId}: ${dates.length} dates`
      );
    } catch (error) {
      logger.error("Block time slots error:", error);
      throw error;
    }
  }

  /**
   * Unblock time slots
   */
  static async unblockTimeSlots(
    counselorId: string,
    dates: Date[]
  ): Promise<void> {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      // Remove dates from unavailable dates
      counselor.availability.unavailableDates =
        counselor.availability.unavailableDates.filter((unavailableDate) => {
          const unavailableDateString = unavailableDate
            .toISOString()
            .split("T")[0];
          return !dates.some(
            (date) => date.toISOString().split("T")[0] === unavailableDateString
          );
        });

      await counselor.save();

      logger.info(
        `Time slots unblocked for counselor ${counselorId}: ${dates.length} dates`
      );
    } catch (error) {
      logger.error("Unblock time slots error:", error);
      throw error;
    }
  }

  /**
   * Get counselor's next available slot
   */
  static async getNextAvailableSlot(
    counselorId: string,
    duration: number = 60,
    daysAhead: number = 30
  ): Promise<{ date: string; startTime: string } | null> {
    try {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + daysAhead);

      const availableSlots = await this.getAvailableSlots({
        counselorId,
        startDate,
        endDate,
        duration,
      });

      for (const daySlots of availableSlots) {
        const availableSlot = daySlots.slots.find((slot) => slot.available);
        if (availableSlot) {
          return {
            date: daySlots.date,
            startTime: availableSlot.startTime,
          };
        }
      }

      return null;
    } catch (error) {
      logger.error("Get next available slot error:", error);
      return null;
    }
  }

  /**
   * Generate time slots for a specific day
   */
  private static async generateTimeSlots(
    counselorId: string,
    date: Date,
    configuredSlots: { startTime: string; endTime: string }[],
    sessionDuration: number
  ): Promise<TimeSlot[]> {
    const slots: TimeSlot[] = [];

    for (const configuredSlot of configuredSlots) {
      const startMinutes = this.timeToMinutes(configuredSlot.startTime);
      const endMinutes = this.timeToMinutes(configuredSlot.endTime);

      let currentMinutes = startMinutes;

      while (currentMinutes + sessionDuration <= endMinutes) {
        const startTime = this.minutesToTime(currentMinutes);
        const endTime = this.minutesToTime(currentMinutes + sessionDuration);

        // TODO: Check against existing bookings
        const available = true; // For now, assume all slots are available

        slots.push({
          startTime,
          endTime,
          available,
        });

        currentMinutes += sessionDuration;
      }
    }

    return slots;
  }

  /**
   * Convert time string (HH:mm) to minutes since midnight
   */
  private static timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes since midnight to time string (HH:mm)
   */
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, "0")}:${mins
      .toString()
      .padStart(2, "0")}`;
  }

  /**
   * Get day of week string from date
   */
  private static getDayOfWeek(date: Date): string {
    const days = [
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ];
    return days[date.getDay()];
  }

  /**
   * Get counselor's weekly availability summary
   */
  static async getWeeklyAvailability(counselorId: string): Promise<any> {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      const weeklyAvailability: any = {};
      const days = [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ];

      days.forEach((day) => {
        const daySchedule = counselor.availability.schedule.get(day);
        weeklyAvailability[day] = {
          isAvailable: daySchedule?.isAvailable || false,
          timeSlots: daySchedule?.timeSlots || [],
          totalHours: daySchedule
            ? this.calculateDayHours(daySchedule.timeSlots)
            : 0,
        };
      });

      return {
        weeklyAvailability,
        timezone: counselor.availability.timezone,
        totalWeeklyHours: Object.values(weeklyAvailability).reduce(
          (total: number, day: any) => total + day.totalHours,
          0
        ),
      };
    } catch (error) {
      logger.error("Get weekly availability error:", error);
      throw error;
    }
  }

  /**
   * Calculate total hours for a day's time slots
   */
  private static calculateDayHours(
    timeSlots: { startTime: string; endTime: string }[]
  ): number {
    return timeSlots.reduce((total, slot) => {
      const startMinutes = this.timeToMinutes(slot.startTime);
      const endMinutes = this.timeToMinutes(slot.endTime);
      return total + (endMinutes - startMinutes) / 60;
    }, 0);
  }
}

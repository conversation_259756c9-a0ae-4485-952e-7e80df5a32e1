// Test script for ResourceCard button behavior
// Run this in browser console to test the like/bookmark functionality

console.log("🧪 Testing ResourceCard Button State Management");

// Test 1: Check current authentication status
const isAuthenticated = localStorage.getItem("accessToken") !== null;
const isGuest = localStorage.getItem("guestToken") !== null && !isAuthenticated;
console.log(
  `🔐 Auth Status: ${
    isAuthenticated ? "Registered" : isGuest ? "Guest" : "Unauthenticated"
  }`
);

// Test 2: Find all resource cards on the page
const resourceCards = document.querySelectorAll('[href*="/resources/"]');
console.log(`📊 Found ${resourceCards.length} resource cards on page`);

// Test 3: Check button behavior instructions
if (isAuthenticated && !isGuest) {
  console.log("✅ EXPECTED BEHAVIOR FOR REGISTERED USERS:");
  console.log(
    "- Bookmark button: Should toggle filled/unfilled state and persist"
  );
  console.log(
    "- Like button: Should briefly show filled state then reset (temporary feedback)"
  );
  console.log("- Both buttons should make API calls");
} else {
  console.log("✅ EXPECTED BEHAVIOR FOR GUESTS/UNAUTHENTICATED:");
  console.log("- Bookmark button: Should show signup modal, NO state change");
  console.log("- Like button: Should show signup modal, NO state change");
  console.log("- Buttons should remain unchanged after modal is closed");
}

// Test 4: Check for button state changes
console.log("\n🔍 MANUAL TESTING INSTRUCTIONS:");
console.log("1. Click bookmark buttons and observe:");
console.log("   - Guest/Unauth: Modal appears, button state unchanged");
console.log("   - Registered: Button toggles filled/unfilled");
console.log("2. Click like buttons and observe:");
console.log("   - Guest/Unauth: Modal appears, button state unchanged");
console.log("   - Registered: Button briefly fills red then resets");

// Test 5: Button state inspection helper
function inspectButtonStates() {
  const bookmarkButtons = document.querySelectorAll(
    'button[title*="bookmark"]'
  );
  const likeButtons = document.querySelectorAll('button[title*="like"]');

  console.log("\n📋 Current Button States:");
  bookmarkButtons.forEach((btn, i) => {
    const svg = btn.querySelector("svg");
    const isFilled = svg?.getAttribute("fill") === "currentColor";
    console.log(`Bookmark ${i + 1}: ${isFilled ? "FILLED" : "EMPTY"}`);
  });

  likeButtons.forEach((btn, i) => {
    const svg = btn.querySelector("svg");
    const isFilled = svg?.getAttribute("fill") === "currentColor";
    console.log(`Like ${i + 1}: ${isFilled ? "FILLED" : "EMPTY"}`);
  });
}

// Export helper function
window.inspectButtonStates = inspectButtonStates;
console.log("💡 Run 'inspectButtonStates()' to check current button states");

export { inspectButtonStates };

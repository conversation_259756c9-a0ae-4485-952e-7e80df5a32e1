// Resources API functions
import {
  Resource,
  ResourceFilters,
  ResourceListOptions,
} from "@/types/resources";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

class ResourcesAPI {
  private getHeaders(token?: string) {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  async getResources(
    filters: ResourceFilters = {},
    options: ResourceListOptions = {},
    token?: string
  ): Promise<{
    success: boolean;
    data: {
      content: Resource[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();

    if (options.page) queryParams.append("page", options.page.toString());
    if (options.limit) queryParams.append("limit", options.limit.toString());
    if (options.sortBy) queryParams.append("sortBy", options.sortBy);
    if (options.sortOrder) queryParams.append("sortOrder", options.sortOrder);

    if (filters.type) queryParams.append("type", filters.type);
    if (filters.category) queryParams.append("category", filters.category);
    if (filters.subcategory)
      queryParams.append("subcategory", filters.subcategory);
    if (filters.difficulty)
      queryParams.append("difficulty", filters.difficulty);
    if (filters.search) queryParams.append("search", filters.search);
    if (filters.minRating)
      queryParams.append("minRating", filters.minRating.toString());
    if (filters.isPremium !== undefined)
      queryParams.append("isPremium", filters.isPremium.toString());

    if (filters.tags) {
      filters.tags.forEach((tag) => queryParams.append("tags", tag));
    }

    const response = await fetch(`${API_BASE_URL}/resources?${queryParams}`, {
      method: "GET",
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch resources");
    }

    return response.json();
  }

  async getResource(
    identifier: string,
    token?: string
  ): Promise<{
    success: boolean;
    data: { content: Resource };
  }> {
    const response = await fetch(`${API_BASE_URL}/resources/${identifier}`, {
      method: "GET",
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch resource");
    }

    return response.json();
  }

  async getFeaturedResources(
    limit: number = 6,
    token?: string
  ): Promise<{
    success: boolean;
    data: { content: Resource[] };
  }> {
    const queryParams = new URLSearchParams({
      isFeatured: "true",
      limit: limit.toString(),
      sortBy: "publishedAt",
      sortOrder: "desc",
    });

    const response = await fetch(`${API_BASE_URL}/resources?${queryParams}`, {
      method: "GET",
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch featured resources");
    }

    return response.json();
  }

  async getTrendingResources(
    limit: number = 10,
    token?: string
  ): Promise<{
    success: boolean;
    data: { content: Resource[] };
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/trending?limit=${limit}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch trending resources");
    }

    return response.json();
  }

  async getSimilarResources(
    resourceId: string,
    limit: number = 5,
    token?: string
  ): Promise<{
    success: boolean;
    data: { content: Resource[] };
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/${resourceId}/similar?limit=${limit}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch similar resources");
    }

    return response.json();
  }

  async searchResources(
    query: string,
    filters: ResourceFilters = {},
    token?: string
  ): Promise<{
    success: boolean;
    data: {
      results: Resource[];
      suggestions: string[];
      totalResults: number;
    };
  }> {
    const queryParams = new URLSearchParams({ q: query });

    if (filters.type) queryParams.append("type", filters.type);
    if (filters.category) queryParams.append("category", filters.category);
    if (filters.difficulty)
      queryParams.append("difficulty", filters.difficulty);

    const response = await fetch(
      `${API_BASE_URL}/resources/search?${queryParams}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Search failed");
    }

    return response.json();
  }

  async getCategories(token?: string): Promise<{
    success: boolean;
    data: {
      categories: Array<{
        category: string;
        count: number;
        subcategories: Array<{ subcategory: string; count: number }>;
      }>;
    };
  }> {
    const response = await fetch(`${API_BASE_URL}/resources/categories`, {
      method: "GET",
      headers: this.getHeaders(token),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch categories");
    }

    return response.json();
  }

  async getPopularTags(
    limit: number = 20,
    token?: string
  ): Promise<{
    success: boolean;
    data: { tags: Array<{ tag: string; count: number }> };
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/tags?limit=${limit}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch popular tags");
    }

    return response.json();
  }

  async bookmarkResource(
    resourceId: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/${resourceId}/bookmark`,
      {
        method: "POST",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to bookmark resource");
    }

    return response.json();
  }

  async unbookmarkResource(
    resourceId: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/${resourceId}/bookmark`,
      {
        method: "DELETE",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to remove bookmark");
    }

    return response.json();
  }

  async likeResource(
    resourceId: string,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/${resourceId}/like`,
      {
        method: "POST",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to like resource");
    }

    return response.json();
  }

  async rateResource(
    resourceId: string,
    rating: number,
    review?: string,
    token?: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/${resourceId}/rate`,
      {
        method: "POST",
        headers: this.getHeaders(token),
        body: JSON.stringify({ rating, review }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to rate resource");
    }

    return response.json();
  }

  async getUserBookmarks(
    token: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    success: boolean;
    data: {
      content: Resource[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    queryParams.append("page", page.toString());
    queryParams.append("limit", limit.toString());

    const response = await fetch(
      `${API_BASE_URL}/resources/user/bookmarks?${queryParams}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch bookmarks");
    }

    return response.json();
  }

  async getUserFavorites(
    token: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    success: boolean;
    data: {
      content: Resource[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    // For now, favorites and bookmarks are the same - using bookmark endpoint
    // This can be updated later if a separate favorites system is implemented
    return this.getUserBookmarks(token, page, limit);
  }

  async getUserHistory(
    token: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    success: boolean;
    data: {
      content: Resource[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    queryParams.append("page", page.toString());
    queryParams.append("limit", limit.toString());
    queryParams.append("action", "view"); // Filter for viewed resources

    const response = await fetch(
      `${API_BASE_URL}/resources/user/interactions?${queryParams}`,
      {
        method: "GET",
        headers: this.getHeaders(token),
      }
    );

    if (!response.ok) {
      // If the endpoint doesn't exist, return empty data for now
      if (response.status === 404) {
        return {
          success: true,
          data: {
            content: [],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              pages: 0,
              hasNext: false,
              hasPrev: false,
            },
          },
        };
      }
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch history");
    }

    return response.json();
  }

  async trackProgress(
    resourceId: string,
    progress: number,
    token: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await fetch(
      `${API_BASE_URL}/resources/${resourceId}/progress`,
      {
        method: "POST",
        headers: this.getHeaders(token),
        body: JSON.stringify({ progress }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to track progress");
    }

    return response.json();
  }
}

export const resourcesAPI = new ResourcesAPI();

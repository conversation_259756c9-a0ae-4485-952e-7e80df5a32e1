const axios = require("axios");

const BASE_URL = "http://localhost:5000/api";

// Test user credentials (from create-test-data.js)
const TEST_USER = {
  email: "<EMAIL>",
  password: "password123",
};

let authToken = null;

// Step 1: Authenticate and get a valid token
async function authenticateUser() {
  try {
    console.log("🔐 Authenticating user...");

    const response = await axios.post(
      `${BASE_URL}/auth/login`,
      {
        email: TEST_USER.email,
        password: TEST_USER.password,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.success && response.data.data.tokens) {
      authToken = response.data.data.tokens.accessToken;
      console.log("✅ Authentication successful!");
      console.log(
        `User: ${response.data.data.user.firstName} ${response.data.data.user.lastName}`
      );
      return true;
    } else {
      console.log("❌ Authentication failed - unexpected response structure");
      console.log("Response:", response.data);
      return false;
    }
  } catch (error) {
    console.error("❌ Authentication failed:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);

      if (error.response.status === 403) {
        console.log(
          "\n💡 Hint: The user might not be email verified or account might be inactive."
        );
        console.log(
          "Try running the test data creation script: node backend/create-test-data.js"
        );
      }
    } else {
      console.error("Error:", error.message);
    }
    return false;
  }
}

// Step 2: Create a test booking session first
async function createTestBooking() {
  try {
    console.log("\n📝 Creating a test booking...");

    // First, let's get available counselors
    const counselorsResponse = await axios.get(`${BASE_URL}/counselors`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    if (
      !counselorsResponse.data.success ||
      !counselorsResponse.data.data.counselors.length
    ) {
      console.log("❌ No counselors available for booking");
      return null;
    }

    const firstCounselor = counselorsResponse.data.data.counselors[0];
    console.log(
      `📋 Found counselor: ${firstCounselor.userId.firstName} ${firstCounselor.userId.lastName}`
    );

    // Create a booking
    const bookingData = {
      counselorId: firstCounselor.userId._id,
      scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      duration: 60, // 60 minutes
      sessionType: "individual",
      notes: "Test booking for payment initialization",
    };

    const bookingResponse = await axios.post(
      `${BASE_URL}/sessions`,
      bookingData,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (bookingResponse.data.success) {
      const sessionId = bookingResponse.data.data.session._id;
      console.log(`✅ Test booking created with ID: ${sessionId}`);
      return sessionId;
    } else {
      console.log("❌ Failed to create test booking");
      console.log(bookingResponse.data);
      return null;
    }
  } catch (error) {
    console.error("❌ Error creating test booking:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    } else {
      console.error("Error:", error.message);
    }
    return null;
  }
}

// Step 3: Test payment initialization with proper validation
async function testPaymentInitialization(sessionId) {
  console.log(`\n💳 Testing payment initialization for session: ${sessionId}`);

  const testCases = [
    {
      name: "Valid payment data (Card - NGN)",
      data: {
        amount: 5000, // 50.00 NGN in kobo
        currency: "NGN",
        paymentMethod: "card",
      },
      shouldSucceed: true,
    },
    {
      name: "Valid payment data (Bank Transfer - USD)",
      data: {
        amount: 2500, // 25.00 USD in cents
        currency: "USD",
        paymentMethod: "bank_transfer",
      },
      shouldSucceed: true,
    },
    {
      name: "Valid payment data (USSD - NGN)",
      data: {
        amount: 10000, // 100.00 NGN in kobo
        currency: "NGN",
        paymentMethod: "ussd",
      },
      shouldSucceed: true,
    },
    {
      name: "Invalid amount (zero)",
      data: {
        amount: 0,
        currency: "NGN",
        paymentMethod: "card",
      },
      shouldSucceed: false,
    },
    {
      name: "Invalid currency",
      data: {
        amount: 5000,
        currency: "EUR",
        paymentMethod: "card",
      },
      shouldSucceed: false,
    },
    {
      name: "Invalid payment method",
      data: {
        amount: 5000,
        currency: "NGN",
        paymentMethod: "crypto",
      },
      shouldSucceed: false,
    },
    {
      name: "Missing required fields",
      data: {
        currency: "NGN",
        // Missing amount and paymentMethod
      },
      shouldSucceed: false,
    },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n🧪 Testing: ${testCase.name}`);

      const response = await axios.post(
        `${BASE_URL}/sessions/${sessionId}/payment/initialize`,
        testCase.data,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (testCase.shouldSucceed) {
        console.log(`✅ ${testCase.name}: SUCCESS`);
        console.log("Response:", {
          status: response.status,
          message: response.data.message,
          hasPaymentData: !!response.data.data,
        });

        if (response.data.data) {
          console.log(
            "Payment URL generated:",
            !!response.data.data.authorization_url
          );
          console.log("Reference:", response.data.data.reference);
        }
      } else {
        console.log(
          `⚠️ ${testCase.name}: Unexpectedly succeeded (should have failed)`
        );
      }
    } catch (error) {
      if (
        !testCase.shouldSucceed &&
        error.response &&
        error.response.status === 400
      ) {
        console.log(`✅ ${testCase.name}: CORRECTLY REJECTED`);
        if (error.response.data.errors) {
          console.log(
            "Validation errors:",
            error.response.data.errors.map((e) => `${e.field}: ${e.message}`)
          );
        }
      } else {
        console.log(`❌ ${testCase.name}: UNEXPECTED ERROR`);
        if (error.response) {
          console.log("Status:", error.response.status);
          console.log("Error:", error.response.data);
        } else {
          console.log("Error:", error.message);
        }
      }
    }
  }
}

// Step 4: Test payment initialization without request body (original frontend issue)
async function testOriginalIssue(sessionId) {
  console.log(
    `\n🔍 Testing original issue - Payment initialization without request body`
  );

  try {
    const response = await axios.post(
      `${BASE_URL}/sessions/${sessionId}/payment/initialize`,
      {}, // Empty body (how frontend was calling it)
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("⚠️ Unexpectedly succeeded with empty body");
    console.log("Response:", response.data);
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log("✅ Correctly rejected empty request body");
      console.log("Validation errors:");
      if (error.response.data.errors) {
        error.response.data.errors.forEach((err) => {
          console.log(`  - ${err.field}: ${err.message}`);
        });
      }

      console.log(
        "\n💡 Solution: Frontend needs to send amount, currency, and paymentMethod in request body"
      );
    } else {
      console.log(
        "❌ Unexpected error:",
        error.response?.data || error.message
      );
    }
  }
}

// Main execution function
async function runPaymentTests() {
  console.log("🚀 Starting Payment Initialization Tests");
  console.log("=========================================\n");

  // Step 1: Authenticate
  const isAuthenticated = await authenticateUser();
  if (!isAuthenticated) {
    console.log("\n❌ Cannot proceed without authentication. Please check:");
    console.log("1. Backend server is running on localhost:5000");
    console.log(
      "2. Test data has been created (run: node backend/create-test-data.js)"
    );
    console.log("3. User credentials are correct");
    return;
  }

  // Step 2: Create test booking
  const sessionId = await createTestBooking();
  if (!sessionId) {
    console.log("\n❌ Cannot proceed without a test booking. Please check:");
    console.log("1. Counselor data exists");
    console.log("2. User has permission to create bookings");
    return;
  }

  // Step 3: Test validation cases
  await testPaymentInitialization(sessionId);

  // Step 4: Test original issue
  await testOriginalIssue(sessionId);

  console.log("\n✨ Payment initialization tests completed!");
  console.log("\n📋 Summary:");
  console.log("- The backend correctly validates required fields");
  console.log(
    "- Frontend must send amount, currency, and paymentMethod in request body"
  );
  console.log("- Valid payment methods: 'card', 'bank_transfer', 'ussd'");
  console.log("- Valid currencies: 'NGN', 'USD'");
  console.log(
    "- Amount must be greater than 0 (in kobo for NGN, cents for USD)"
  );
}

// Run the tests
runPaymentTests().catch(console.error);

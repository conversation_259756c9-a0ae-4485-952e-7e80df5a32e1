"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  XMarkIcon,
  BookmarkIcon,
  HeartIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";

interface SignupPromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature: "bookmark" | "like" | "rate";
}

export default function SignupPromptModal({
  isOpen,
  onClose,
  feature,
}: SignupPromptModalProps) {
  const router = useRouter();

  console.log("📝 SignupPromptModal render:", { isOpen, feature });

  useEffect(() => {
    if (isOpen) {
      console.log("🔓 Modal opening for feature:", feature);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const getFeatureInfo = () => {
    switch (feature) {
      case "bookmark":
        return {
          title: "Save Your Favorite Resources",
          description:
            "Bookmark resources to easily find them later in your personal collection.",
          icon: <BookmarkIcon className="h-6 w-6 text-purple-600" />,
          benefits: [
            "Access your saved resources anytime",
            "Organize your mental health journey",
            "Never lose helpful content again",
          ],
        };
      case "like":
        return {
          title: "Show Your Appreciation",
          description:
            "Like resources to help others discover valuable content and support creators.",
          icon: <HeartIcon className="h-6 w-6 text-red-500" />,
          benefits: [
            "Help others find great content",
            "Support resource creators",
            "Build a personalized experience",
          ],
        };
      case "rate":
        return {
          title: "Share Your Experience",
          description:
            "Rate and review resources to help build a better community for everyone.",
          icon: (
            <svg
              className="h-6 w-6 text-yellow-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ),
          benefits: [
            "Share honest feedback",
            "Help improve resources",
            "Guide others in their journey",
          ],
        };
    }
  };

  const featureInfo = getFeatureInfo();

  const handleSignup = () => {
    onClose();
    router.push("/register");
  };

  const handleLogin = () => {
    onClose();
    router.push("/auth/login");
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-25 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4 text-center">
        <div className="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {featureInfo.icon}
              <h3 className="text-lg font-semibold leading-6 text-gray-900">
                {featureInfo.title}
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <p className="text-sm text-gray-600 mb-6">
            {featureInfo.description}
          </p>

          {/* Benefits */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              With a free account, you can:
            </h4>
            <ul className="space-y-2">
              {featureInfo.benefits.map((benefit, index) => (
                <li
                  key={index}
                  className="flex items-center text-sm text-gray-600"
                >
                  <svg
                    className="h-4 w-4 text-green-500 mr-2 flex-shrink-0"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {benefit}
                </li>
              ))}
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleSignup}
              className="w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
            >
              <UserPlusIcon className="h-5 w-5 mr-2" />
              Create Free Account
            </button>

            <button
              onClick={handleLogin}
              className="w-full px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Sign In to Existing Account
            </button>
          </div>

          <div className="mt-4 text-xs text-gray-500 text-center">
            It's free, secure, and takes less than a minute
          </div>
        </div>
      </div>
    </div>
  );
}

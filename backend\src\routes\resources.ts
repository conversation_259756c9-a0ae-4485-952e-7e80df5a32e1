import { Router } from "express";
import { ContentController } from "@/controllers/contentController";
import {
  resourceValidations,
  paramValidations,
  commonValidations,
} from "@/utils/validation";
import {
  authenticate,
  optionalAuth,
  requireCounselor,
} from "@/middleware/auth";
import { createUploadMiddleware, handleUploadError } from "@/middleware/upload";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// Public routes
router.get("/", optionalAuth, ContentController.getContent);

router.get("/search", ContentController.searchContent);

router.get("/autocomplete", ContentController.getAutocomplete);

router.get("/trending", ContentController.getTrendingContent);

router.get("/categories", ContentController.getCategories);

router.get("/tags", ContentController.getPopularTags);

router.get("/popular-searches", ContentController.getPopularSearches);

router.get(
  "/:identifier",
  optionalAuth,
  paramValidations.objectIdOrSlug("identifier"),
  validateRequest,
  ContentController.getContentById
);

router.get(
  "/:contentId/similar",
  paramValidations.objectId("contentId"),
  validateRequest,
  ContentController.getSimilarContent
);

// Content creation and management (counselors and admins)
router.post(
  "/",
  authenticate,
  requireCounselor,
  resourceValidations.create,
  validateRequest,
  ContentController.createContent
);

router.put(
  "/:contentId",
  authenticate,
  requireCounselor,
  paramValidations.objectId("contentId"),
  resourceValidations.update,
  validateRequest,
  ContentController.updateContent
);

router.delete(
  "/:contentId",
  authenticate,
  requireCounselor,
  paramValidations.objectId("contentId"),
  validateRequest,
  ContentController.deleteContent
);

// Media upload
const uploadContentMedia = createUploadMiddleware({
  fieldName: "file",
  allowedTypes: ["image/*", "video/*", "audio/*", "application/pdf"],
  maxSize: 50 * 1024 * 1024, // 50MB
  maxFiles: 1,
});

router.post(
  "/upload",
  authenticate,
  requireCounselor,
  uploadContentMedia,
  handleUploadError,
  ContentController.uploadMedia
);

// User interactions
router.post(
  "/:contentId/like",
  authenticate,
  paramValidations.objectId("contentId"),
  validateRequest,
  ContentController.toggleLike
);

router.post(
  "/:contentId/bookmark",
  authenticate,
  paramValidations.objectId("contentId"),
  validateRequest,
  ContentController.toggleBookmark
);

router.post(
  "/:contentId/rate",
  authenticate,
  paramValidations.objectId("contentId"),
  commonValidations.rating,
  validateRequest,
  ContentController.rateContent
);

// User content management
router.get("/user/bookmarks", authenticate, ContentController.getUserBookmarks);

router.get(
  "/user/recommendations",
  authenticate,
  ContentController.getRecommendations
);

export default router;

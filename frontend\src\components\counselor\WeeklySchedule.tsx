'use client';

import { useState } from 'react';
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

interface TimeSlot {
  startTime: string;
  endTime: string;
}

interface DaySchedule {
  isAvailable: boolean;
  timeSlots: TimeSlot[];
}

interface WeeklyScheduleProps {
  schedule: { [key: string]: DaySchedule };
  onChange: (day: string, daySchedule: DaySchedule) => void;
}

const DAYS = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' },
];

export default function WeeklySchedule({ schedule, onChange }: WeeklyScheduleProps) {
  const handleDayToggle = (day: string) => {
    const currentSchedule = schedule[day];
    const newSchedule = {
      ...currentSchedule,
      isAvailable: !currentSchedule.isAvailable,
      timeSlots: !currentSchedule.isAvailable ? [{ startTime: '09:00', endTime: '17:00' }] : [],
    };
    onChange(day, newSchedule);
  };

  const handleTimeSlotChange = (day: string, slotIndex: number, field: 'startTime' | 'endTime', value: string) => {
    const currentSchedule = schedule[day];
    const newTimeSlots = [...currentSchedule.timeSlots];
    newTimeSlots[slotIndex] = { ...newTimeSlots[slotIndex], [field]: value };
    
    onChange(day, { ...currentSchedule, timeSlots: newTimeSlots });
  };

  const addTimeSlot = (day: string) => {
    const currentSchedule = schedule[day];
    const newTimeSlot = { startTime: '09:00', endTime: '17:00' };
    const newTimeSlots = [...currentSchedule.timeSlots, newTimeSlot];
    
    onChange(day, { ...currentSchedule, timeSlots: newTimeSlots });
  };

  const removeTimeSlot = (day: string, slotIndex: number) => {
    const currentSchedule = schedule[day];
    const newTimeSlots = currentSchedule.timeSlots.filter((_, index) => index !== slotIndex);
    
    onChange(day, { ...currentSchedule, timeSlots: newTimeSlots });
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        options.push(timeString);
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  return (
    <div className="space-y-6">
      {DAYS.map(({ key, label }) => {
        const daySchedule = schedule[key];
        
        return (
          <div key={key} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id={`${key}-available`}
                  checked={daySchedule.isAvailable}
                  onChange={() => handleDayToggle(key)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <label htmlFor={`${key}-available`} className="text-lg font-medium text-gray-900">
                  {label}
                </label>
              </div>
              
              {daySchedule.isAvailable && (
                <button
                  onClick={() => addTimeSlot(key)}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Time Slot
                </button>
              )}
            </div>

            {daySchedule.isAvailable && (
              <div className="space-y-3">
                {daySchedule.timeSlots.length === 0 ? (
                  <p className="text-sm text-gray-500 italic">No time slots added</p>
                ) : (
                  daySchedule.timeSlots.map((slot, slotIndex) => (
                    <div key={slotIndex} className="flex items-center space-x-3 bg-gray-50 p-3 rounded-md">
                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-700">From:</label>
                        <select
                          value={slot.startTime}
                          onChange={(e) => handleTimeSlotChange(key, slotIndex, 'startTime', e.target.value)}
                          className="border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-sm"
                        >
                          {timeOptions.map((time) => (
                            <option key={time} value={time}>
                              {time}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-700">To:</label>
                        <select
                          value={slot.endTime}
                          onChange={(e) => handleTimeSlotChange(key, slotIndex, 'endTime', e.target.value)}
                          className="border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-sm"
                        >
                          {timeOptions.map((time) => (
                            <option key={time} value={time}>
                              {time}
                            </option>
                          ))}
                        </select>
                      </div>

                      {daySchedule.timeSlots.length > 1 && (
                        <button
                          onClick={() => removeTimeSlot(key, slotIndex)}
                          className="inline-flex items-center p-1 border border-transparent rounded-md text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}

            {!daySchedule.isAvailable && (
              <p className="text-sm text-gray-500 italic">Not available on this day</p>
            )}
          </div>
        );
      })}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Tips for Setting Your Schedule</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Set realistic time slots that allow for breaks between sessions</li>
          <li>• Consider different time zones if you serve international clients</li>
          <li>• Leave buffer time for session preparation and notes</li>
          <li>• You can have multiple time slots per day (e.g., morning and evening)</li>
        </ul>
      </div>
    </div>
  );
}

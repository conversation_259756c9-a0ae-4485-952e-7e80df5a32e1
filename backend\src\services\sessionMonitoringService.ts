import { Session } from "@/models/Session";
import { logger } from "@/utils/logger";
import { sendEmail } from "@/utils/email";

export interface QualityMetrics {
  videoQuality: "excellent" | "good" | "fair" | "poor";
  audioQuality: "excellent" | "good" | "fair" | "poor";
  connectionStability: "stable" | "unstable" | "poor";
  latency: number; // in milliseconds
  packetLoss: number; // percentage
  bandwidth: number; // in kbps
}

export interface SessionIssue {
  type: "connection" | "audio" | "video" | "recording" | "other";
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  timestamp: Date;
  resolved: boolean;
  resolution?: string;
}

export interface SessionAlert {
  sessionId: string;
  alertType:
    | "quality_degradation"
    | "connection_lost"
    | "recording_failed"
    | "participant_left";
  severity: "warning" | "error" | "critical";
  message: string;
  timestamp: Date;
  metadata?: any;
}

export class SessionMonitoringService {
  private static qualityThresholds = {
    video: {
      excellent: 90,
      good: 70,
      fair: 50,
    },
    audio: {
      excellent: 95,
      good: 80,
      fair: 60,
    },
    connection: {
      stable: 95,
      unstable: 80,
    },
    latency: {
      excellent: 100,
      good: 200,
      fair: 500,
    },
    packetLoss: {
      excellent: 1,
      good: 3,
      fair: 8,
    },
  };

  /**
   * Monitor session quality in real-time
   */
  static async monitorSessionQuality(
    sessionId: string,
    metrics: QualityMetrics
  ): Promise<void> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        logger.warn(`Session not found for quality monitoring: ${sessionId}`);
        return;
      }

      // Initialize quality tracking if not exists
      if (!session.qualityMetrics) {
        session.qualityMetrics = {
          samples: [],
          averageQuality: 0,
          issues: [],
        };
      }

      // Add quality sample
      const qualitySample = {
        timestamp: new Date(),
        ...metrics,
      };

      session.qualityMetrics!.samples.push(qualitySample as any);

      // Keep only last 100 samples to prevent document bloat
      if (session.qualityMetrics!.samples.length > 100) {
        session.qualityMetrics!.samples =
          session.qualityMetrics!.samples.slice(-100);
      }

      // Calculate average quality
      session.qualityMetrics!.averageQuality = this.calculateAverageQuality(
        session.qualityMetrics!.samples
      );

      // Check for quality issues
      const issues = this.detectQualityIssues(metrics);
      if (issues.length > 0) {
        session.qualityMetrics!.issues.push(...(issues as any));

        // Send alerts for critical issues
        for (const issue of issues) {
          if (issue.severity === "critical" || issue.severity === "high") {
            await this.sendQualityAlert(sessionId, issue);
          }
        }
      }

      await session.save();

      logger.debug(`Quality metrics updated for session: ${sessionId}`);
    } catch (error) {
      logger.error("Monitor session quality error:", error);
    }
  }

  /**
   * Track session events
   */
  static async trackSessionEvent(
    sessionId: string,
    eventType: string,
    eventData: any
  ): Promise<void> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        logger.warn(`Session not found for event tracking: ${sessionId}`);
        return;
      }

      // Initialize events tracking if not exists
      if (!session.events) {
        session.events = [];
      }

      const event = {
        type: eventType,
        timestamp: new Date(),
        data: eventData,
      };

      session.events.push(event as any);

      // Keep only last 200 events
      if (session.events.length > 200) {
        session.events = session.events.slice(-200);
      }

      await session.save();

      // Handle specific events
      switch (eventType) {
        case "participant_joined":
          await this.handleParticipantJoined(sessionId, eventData);
          break;
        case "participant_left":
          await this.handleParticipantLeft(sessionId, eventData);
          break;
        case "connection_lost":
          await this.handleConnectionLost(sessionId, eventData);
          break;
        case "recording_started":
          await this.handleRecordingStarted(sessionId, eventData);
          break;
        case "recording_stopped":
          await this.handleRecordingStopped(sessionId, eventData);
          break;
      }

      logger.debug(`Event tracked for session ${sessionId}: ${eventType}`);
    } catch (error) {
      logger.error("Track session event error:", error);
    }
  }

  /**
   * Generate session quality report
   */
  static async generateQualityReport(sessionId: string): Promise<any> {
    try {
      const session = await Session.findById(sessionId)
        .populate("clientId", "firstName lastName email")
        .populate("counselorId", "userId");

      if (!session) {
        throw new Error("Session not found");
      }

      const report = {
        sessionId,
        sessionDetails: {
          scheduledAt: session.scheduledAt,
          duration: session.duration,
          actualDuration: session.actualDuration,
          status: session.status,
        },
        participants: {
          client: session.clientId,
          counselor: session.counselorId,
        },
        qualityMetrics: session.qualityMetrics || {
          averageQuality: {
            video: "unknown",
            audio: "unknown",
            connection: "unknown",
          },
          samples: [],
          issues: [],
        },
        events: session.events || [],
        videoSession: session.videoSession,
        summary: this.generateQualitySummary(session),
      };

      return report;
    } catch (error) {
      logger.error("Generate quality report error:", error);
      throw error;
    }
  }

  /**
   * Get session health status
   */
  static async getSessionHealth(sessionId: string): Promise<any> {
    try {
      const session = await Session.findById(sessionId);

      if (!session) {
        throw new Error("Session not found");
      }

      const health: {
        overall: string;
        issues: string[];
        recommendations: string[];
      } = {
        overall: "healthy",
        issues: [],
        recommendations: [],
      };

      // Check quality metrics
      if (session.qualityMetrics) {
        const avgQuality = session.qualityMetrics.averageQuality;

        if (avgQuality < 3) {
          health.overall = "poor";
          health.issues.push("Poor audio/video quality detected");
          health.recommendations.push(
            "Check internet connection and device settings"
          );
        } else if (avgQuality < 4) {
          health.overall = "unstable";
          health.issues.push("Unstable connection detected");
          health.recommendations.push(
            "Consider switching to a more stable network"
          );
        }

        // Check for recent issues
        const recentIssues = session.qualityMetrics.issues.filter(
          (issue: any) =>
            new Date().getTime() - issue.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
        );

        if (recentIssues.length > 0) {
          health.overall = "degraded";
          health.issues.push(`${recentIssues.length} recent quality issues`);
        }
      }

      return health;
    } catch (error) {
      logger.error("Get session health error:", error);
      throw error;
    }
  }

  /**
   * Calculate average quality from samples
   */
  private static calculateAverageQuality(samples: any[]): any {
    if (samples.length === 0) {
      return { video: "good", audio: "good", connection: "stable" };
    }

    const recent = samples.slice(-10); // Last 10 samples

    const avgVideo =
      recent.reduce((sum, sample) => {
        const score = this.qualityToScore(sample.videoQuality);
        return sum + score;
      }, 0) / recent.length;

    const avgAudio =
      recent.reduce((sum, sample) => {
        const score = this.qualityToScore(sample.audioQuality);
        return sum + score;
      }, 0) / recent.length;

    const avgConnection =
      recent.reduce((sum, sample) => {
        const score = this.connectionToScore(sample.connectionStability);
        return sum + score;
      }, 0) / recent.length;

    return {
      video: this.scoreToQuality(avgVideo),
      audio: this.scoreToQuality(avgAudio),
      connection: this.scoreToConnection(avgConnection),
    };
  }

  /**
   * Detect quality issues from metrics
   */
  private static detectQualityIssues(metrics: QualityMetrics): SessionIssue[] {
    const issues: SessionIssue[] = [];

    // Check video quality
    if (metrics.videoQuality === "poor") {
      issues.push({
        type: "video",
        severity: "high",
        description: "Poor video quality detected",
        timestamp: new Date(),
        resolved: false,
      });
    }

    // Check audio quality
    if (metrics.audioQuality === "poor") {
      issues.push({
        type: "audio",
        severity: "high",
        description: "Poor audio quality detected",
        timestamp: new Date(),
        resolved: false,
      });
    }

    // Check connection stability
    if (metrics.connectionStability === "poor") {
      issues.push({
        type: "connection",
        severity: "critical",
        description: "Poor connection stability detected",
        timestamp: new Date(),
        resolved: false,
      });
    }

    // Check latency
    if (metrics.latency > this.qualityThresholds.latency.fair) {
      issues.push({
        type: "connection",
        severity: metrics.latency > 1000 ? "critical" : "medium",
        description: `High latency detected: ${metrics.latency}ms`,
        timestamp: new Date(),
        resolved: false,
      });
    }

    // Check packet loss
    if (metrics.packetLoss > this.qualityThresholds.packetLoss.fair) {
      issues.push({
        type: "connection",
        severity: metrics.packetLoss > 15 ? "critical" : "medium",
        description: `High packet loss detected: ${metrics.packetLoss}%`,
        timestamp: new Date(),
        resolved: false,
      });
    }

    return issues;
  }

  /**
   * Send quality alert
   */
  private static async sendQualityAlert(
    sessionId: string,
    issue: SessionIssue
  ): Promise<void> {
    try {
      const session = await Session.findById(sessionId)
        .populate("clientId")
        .populate("counselorId");

      if (!session) return;

      const alert: SessionAlert = {
        sessionId,
        alertType: "quality_degradation",
        severity: issue.severity === "critical" ? "critical" : "warning",
        message: issue.description,
        timestamp: new Date(),
        metadata: { issue },
      };

      // Log alert
      logger.warn(`Session quality alert: ${sessionId} - ${issue.description}`);

      // Send email to support team for critical issues
      if (issue.severity === "critical") {
        await sendEmail({
          to: process.env.SUPPORT_EMAIL || "<EMAIL>",
          subject: `Critical Session Quality Issue - ${sessionId}`,
          html: `
            <h2>Critical Session Quality Issue</h2>
            <p><strong>Session ID:</strong> ${sessionId}</p>
            <p><strong>Issue:</strong> ${issue.description}</p>
            <p><strong>Severity:</strong> ${issue.severity}</p>
            <p><strong>Time:</strong> ${issue.timestamp.toISOString()}</p>
            <p>Please investigate immediately.</p>
          `,
        });
      }
    } catch (error) {
      logger.error("Send quality alert error:", error);
    }
  }

  /**
   * Generate quality summary
   */
  private static generateQualitySummary(session: any): any {
    const summary: {
      overallRating: string;
      keyMetrics: any;
      recommendations: string[];
    } = {
      overallRating: "good",
      keyMetrics: {},
      recommendations: [],
    };

    if (session.qualityMetrics && session.qualityMetrics.samples.length > 0) {
      const samples = session.qualityMetrics.samples;
      const avgQuality = session.qualityMetrics.averageQuality;

      // Use the numeric average quality directly
      summary.overallRating =
        avgQuality >= 4
          ? "excellent"
          : avgQuality >= 3
          ? "good"
          : avgQuality >= 2
          ? "fair"
          : "poor";

      // Key metrics
      summary.keyMetrics = {
        averageLatency:
          samples.reduce((sum: number, s: any) => sum + s.latency, 0) /
          samples.length,
        averagePacketLoss:
          samples.reduce((sum: number, s: any) => sum + s.packetLoss, 0) /
          samples.length,
        qualityIssues: session.qualityMetrics.issues.length,
      };

      // Recommendations
      if (avgQuality.video === "poor" || avgQuality.audio === "poor") {
        summary.recommendations.push(
          "Consider upgrading internet connection for better quality"
        );
      }
      if (avgQuality.connection === "poor") {
        summary.recommendations.push(
          "Use a wired connection for more stability"
        );
      }
    }

    return summary;
  }

  // Helper methods for quality scoring
  private static qualityToScore(quality: string): number {
    switch (quality) {
      case "excellent":
        return 100;
      case "good":
        return 75;
      case "fair":
        return 50;
      case "poor":
        return 25;
      default:
        return 75;
    }
  }

  private static connectionToScore(connection: string): number {
    switch (connection) {
      case "stable":
        return 100;
      case "unstable":
        return 60;
      case "poor":
        return 20;
      default:
        return 80;
    }
  }

  private static scoreToQuality(score: number): string {
    if (score >= 90) return "excellent";
    if (score >= 70) return "good";
    if (score >= 50) return "fair";
    return "poor";
  }

  private static scoreToConnection(score: number): string {
    if (score >= 90) return "stable";
    if (score >= 60) return "unstable";
    return "poor";
  }

  // Event handlers
  private static async handleParticipantJoined(
    sessionId: string,
    eventData: any
  ): Promise<void> {
    logger.info(
      `Participant joined session ${sessionId}: ${eventData.participantId}`
    );
  }

  private static async handleParticipantLeft(
    sessionId: string,
    eventData: any
  ): Promise<void> {
    logger.info(
      `Participant left session ${sessionId}: ${eventData.participantId}`
    );
  }

  private static async handleConnectionLost(
    sessionId: string,
    eventData: any
  ): Promise<void> {
    logger.warn(
      `Connection lost in session ${sessionId}: ${eventData.participantId}`
    );
  }

  private static async handleRecordingStarted(
    sessionId: string,
    eventData: any
  ): Promise<void> {
    logger.info(`Recording started for session ${sessionId}`);
  }

  private static async handleRecordingStopped(
    sessionId: string,
    eventData: any
  ): Promise<void> {
    logger.info(`Recording stopped for session ${sessionId}`);
  }
}

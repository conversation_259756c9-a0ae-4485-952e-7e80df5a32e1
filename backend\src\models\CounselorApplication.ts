import mongoose, { Document, Schema } from 'mongoose';

export interface ICounselorApplication extends Document {
  userId: mongoose.Types.ObjectId;
  profilePhoto: string;
  bio: string;
  specializations: string[];
  pricePerMinute: number;
  weeklyAvailability: {
    day: string;
    startTime: string;
    endTime: string;
  }[];
  idDocument: string;
  certifications: string[];
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: mongoose.Types.ObjectId;
  reviewedAt?: Date;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const counselorApplicationSchema = new Schema<ICounselorApplication>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  profilePhoto: { type: String, required: true },
  bio: { type: String, required: true, maxlength: 500 },
  specializations: [{ type: String, required: true }],
  pricePerMinute: { type: Number, required: true, min: 1 },
  weeklyAvailability: [{
    day: { type: String, required: true },
    startTime: { type: String, required: true },
    endTime: { type: String, required: true }
  }],
  idDocument: { type: String, required: true },
  certifications: [{ type: String, required: true }],
  status: { 
    type: String, 
    enum: ['pending', 'approved', 'rejected'], 
    default: 'pending' 
  },
  reviewedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  reviewedAt: Date,
  rejectionReason: String
}, {
  timestamps: true
});

export default mongoose.model<ICounselorApplication>('CounselorApplication', counselorApplicationSchema);
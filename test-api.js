const fetch = require("node-fetch");

async function testAPI() {
  try {
    console.log("Testing API endpoint...");
    const response = await fetch("http://localhost:5000/api/counselors");
    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers));

    const data = await response.json();
    console.log("Response data:", JSON.stringify(data, null, 2));
  } catch (error) {
    console.error("Error:", error.message);
  }
}

testAPI();

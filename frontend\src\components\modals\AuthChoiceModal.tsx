"use client";

import { useEffect } from "react";
import {
  XMarkIcon,
  UserIcon,
  PlusIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";

interface AuthChoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onChoice: (choice: "guest" | "signup" | "login") => void;
}

export default function AuthChoiceModal({
  isOpen,
  onClose,
  onChoice,
}: AuthChoiceModalProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-25 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4 text-center">
        <div className="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold leading-6 text-gray-900">
              Join the Conversation!
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <p className="text-sm text-gray-600 mb-6">
            Choose how you'd like to participate in our supportive community:
          </p>

          <div className="space-y-3">
            {/* Continue as Guest */}
            <button
              onClick={() => onChoice("guest")}
              className="w-full p-4 border-2 border-purple-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors text-left group"
            >
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                  <UserIcon className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Continue as Guest
                  </h4>
                  <p className="text-sm text-gray-600">
                    Browse and join discussions with a temporary account
                  </p>
                  <div className="mt-2 flex items-center text-xs text-purple-600">
                    <span>Quick start • No email required</span>
                    <ArrowRightIcon className="h-3 w-3 ml-1" />
                  </div>
                </div>
              </div>
            </button>

            {/* Create Full Account */}
            <button
              onClick={() => onChoice("signup")}
              className="w-full p-4 border-2 border-primary-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors text-left group"
            >
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                  <PlusIcon className="h-5 w-5 text-primary-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Create Full Account
                  </h4>
                  <p className="text-sm text-gray-600">
                    Access all features including counselor booking and session
                    history
                  </p>
                  <div className="mt-2 flex items-center text-xs text-green-600">
                    <span>Full features • Secure & private</span>
                    <ArrowRightIcon className="h-3 w-3 ml-1" />
                  </div>
                </div>
              </div>
            </button>
          </div>

          <div className="mt-6 text-center">
            <button
              onClick={() => onChoice("login")}
              className="text-sm text-purple-600 hover:text-purple-700 font-medium transition-colors"
            >
              Already have an account? Sign in
            </button>
          </div>

          <div className="mt-4 text-xs text-gray-500 text-center">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { adminAPI } from "@/lib/admin";
import { PlatformMetrics } from "@/types/admin";
import AdminLayout from "@/components/admin/AdminLayout";
import MetricsChart from "@/components/admin/MetricsChart";
import RevenueChart from "@/components/admin/RevenueChart";
import UserGrowthChart from "@/components/admin/UserGrowthChart";
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

export default function AdminAnalyticsPage() {
  const router = useRouter();
  const { user, tokens, isLoading } = useAuthStore();

  const [metrics, setMetrics] = useState<PlatformMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<
    "week" | "month" | "quarter" | "year"
  >("month");
  const [selectedMetric, setSelectedMetric] = useState<
    "revenue" | "users" | "sessions" | "engagement"
  >("revenue");

  useEffect(() => {
    if (!isLoading && (!user || user.role !== "admin")) {
      router.push("/");
      return;
    }

    if (tokens?.accessToken) {
      fetchMetrics();
    }
  }, [isLoading, user, tokens, selectedPeriod]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await adminAPI.getPlatformMetrics(
        selectedPeriod,
        tokens!.accessToken
      );
      setMetrics(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load analytics");
    } finally {
      setLoading(false);
    }
  };

  const exportAnalytics = async () => {
    try {
      const response = await adminAPI.exportAnalytics(
        selectedPeriod,
        tokens!.accessToken
      );

      // Create download link
      const blob = new Blob([JSON.stringify(response.data)], {
        type: "text/csv",
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `theramea-analytics-${selectedPeriod}-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to export analytics"
      );
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? "+" : ""}${value.toFixed(1)}%`;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return "text-green-600";
    if (change < 0) return "text-red-600";
    return "text-gray-600";
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!metrics) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No analytics data available
          </h3>
          <p className="text-gray-600">
            Analytics data will appear here once there's platform activity.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <ChartBarIcon className="h-8 w-8 text-purple-600 mr-3" />
              Platform Analytics
            </h1>
            <p className="mt-2 text-gray-600">
              Comprehensive insights into platform performance and growth
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5 text-gray-400" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-sm"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
            </div>

            <button
              onClick={exportAnalytics}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Revenue
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(metrics.revenue.total)}
                </p>
                <div className="flex items-center mt-1">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span
                    className={`text-sm ${getChangeColor(
                      metrics.revenue.growth
                    )}`}
                  >
                    {formatPercentage(metrics.revenue.growth)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Active Users
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics.users.active.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-blue-500 mr-1" />
                  <span
                    className={`text-sm ${getChangeColor(
                      metrics.users.growth
                    )}`}
                  >
                    {formatPercentage(metrics.users.growth)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-bold text-sm">S</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Sessions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics.sessions.total.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-purple-500 mr-1" />
                  <span
                    className={`text-sm ${getChangeColor(
                      metrics.sessions.growth
                    )}`}
                  >
                    {formatPercentage(metrics.sessions.growth)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-bold text-sm">%</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Conversion Rate
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics.conversion.rate.toFixed(1)}%
                </p>
                <div className="flex items-center mt-1">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-orange-500 mr-1" />
                  <span
                    className={`text-sm ${getChangeColor(
                      metrics.conversion.growth
                    )}`}
                  >
                    {formatPercentage(metrics.conversion.growth)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Chart Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Trends</h3>
            <div className="flex space-x-2">
              {[
                { key: "revenue", label: "Revenue" },
                { key: "users", label: "Users" },
                { key: "sessions", label: "Sessions" },
                { key: "engagement", label: "Engagement" },
              ].map((metric) => (
                <button
                  key={metric.key}
                  onClick={() => setSelectedMetric(metric.key as any)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    selectedMetric === metric.key
                      ? "bg-purple-600 text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  {metric.label}
                </button>
              ))}
            </div>
          </div>

          <div className="h-80">
            {selectedMetric === "revenue" && (
              <RevenueChart
                data={metrics.charts.revenue}
                period={selectedPeriod}
              />
            )}
            {selectedMetric === "users" && (
              <UserGrowthChart
                data={metrics.charts.users}
                period={selectedPeriod}
              />
            )}
            {(selectedMetric === "sessions" ||
              selectedMetric === "engagement") && (
              <MetricsChart
                data={metrics.charts[selectedMetric]}
                type={selectedMetric}
                period={selectedPeriod}
              />
            )}
          </div>
        </div>

        {/* Detailed Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Metrics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              User Metrics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Users</span>
                <span className="font-medium text-gray-900">
                  {metrics.users.total.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">New Users</span>
                <span className="font-medium text-gray-900">
                  {metrics.users.new.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Retention Rate</span>
                <span className="font-medium text-gray-900">
                  {metrics.users.retention.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Avg. Session Duration</span>
                <span className="font-medium text-gray-900">
                  {metrics.engagement.avgSessionDuration} min
                </span>
              </div>
            </div>
          </div>

          {/* Revenue Metrics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Revenue Metrics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Average Order Value</span>
                <span className="font-medium text-gray-900">
                  {formatCurrency(metrics.revenue.averageOrderValue)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Monthly Recurring Revenue</span>
                <span className="font-medium text-gray-900">
                  {formatCurrency(metrics.revenue.mrr)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Refund Rate</span>
                <span className="font-medium text-gray-900">
                  {metrics.revenue.refundRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Platform Fee Revenue</span>
                <span className="font-medium text-gray-900">
                  {formatCurrency(metrics.revenue.platformFees)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Top Performers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Counselors */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Top Counselors
            </h3>
            <div className="space-y-3">
              {metrics.topPerformers.counselors.map((counselor, index) => (
                <div
                  key={counselor.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">
                      #{index + 1}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {counselor.name}
                      </p>
                      <p className="text-xs text-gray-600">
                        {counselor.sessions} sessions
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(counselor.earnings)}
                    </p>
                    <p className="text-xs text-gray-600">
                      {counselor.rating.toFixed(1)}★
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Popular Topics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Popular Topics
            </h3>
            <div className="space-y-3">
              {metrics.popularTopics.map((topic, index) => (
                <div
                  key={topic.name}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">
                      #{index + 1}
                    </span>
                    <p className="text-sm font-medium text-gray-900 capitalize">
                      {topic.name}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {topic.count}
                    </p>
                    <p className="text-xs text-gray-600">
                      {topic.growth.toFixed(1)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

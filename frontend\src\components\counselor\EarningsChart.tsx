'use client';

interface EarningsData {
  month: string;
  earnings: number;
  sessions: number;
}

interface EarningsChartProps {
  data: EarningsData[];
  currency: string;
  period: 'week' | 'month' | 'year';
}

export default function EarningsChart({ data, currency, period }: EarningsChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <div className="text-4xl mb-2">📊</div>
          <p>No earnings data available</p>
        </div>
      </div>
    );
  }

  const maxEarnings = Math.max(...data.map(d => d.earnings));
  const maxSessions = Math.max(...data.map(d => d.sessions));

  const formatCurrency = (amount: number) => {
    const symbol = currency === 'NGN' ? '₦' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const getBarHeight = (value: number, max: number) => {
    return Math.max((value / max) * 200, 2); // Minimum height of 2px
  };

  const formatPeriodLabel = (label: string) => {
    if (period === 'week') {
      return label.split(' ')[0]; // Show just the day name
    }
    if (period === 'month') {
      return label.substring(0, 3); // Show first 3 characters of month
    }
    return label; // Show full year
  };

  return (
    <div className="space-y-6">
      {/* Chart */}
      <div className="relative">
        <div className="flex items-end justify-between space-x-2 h-64 px-4">
          {data.map((item, index) => (
            <div key={index} className="flex-1 flex flex-col items-center space-y-2">
              {/* Earnings Bar */}
              <div className="relative flex flex-col items-center">
                <div className="relative group">
                  <div
                    className="bg-purple-500 rounded-t-sm transition-all duration-300 hover:bg-purple-600 cursor-pointer"
                    style={{
                      height: `${getBarHeight(item.earnings, maxEarnings)}px`,
                      width: '24px',
                    }}
                  />
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    <div className="text-center">
                      <div className="font-medium">{formatCurrency(item.earnings)}</div>
                      <div className="text-gray-300">{item.sessions} sessions</div>
                    </div>
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
                
                {/* Value Label */}
                <div className="text-xs text-gray-600 mt-1 text-center">
                  {formatCurrency(item.earnings)}
                </div>
              </div>
              
              {/* Period Label */}
              <div className="text-xs text-gray-500 text-center font-medium">
                {formatPeriodLabel(item.month)}
              </div>
            </div>
          ))}
        </div>

        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-64 flex flex-col justify-between text-xs text-gray-500 -ml-2">
          <span>{formatCurrency(maxEarnings)}</span>
          <span>{formatCurrency(Math.floor(maxEarnings * 0.75))}</span>
          <span>{formatCurrency(Math.floor(maxEarnings * 0.5))}</span>
          <span>{formatCurrency(Math.floor(maxEarnings * 0.25))}</span>
          <span>₦0</span>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {formatCurrency(data.reduce((sum, item) => sum + item.earnings, 0))}
          </div>
          <div className="text-sm text-gray-600">Total Earnings</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {data.reduce((sum, item) => sum + item.sessions, 0)}
          </div>
          <div className="text-sm text-gray-600">Total Sessions</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(
              Math.round(
                data.reduce((sum, item) => sum + item.earnings, 0) / 
                Math.max(data.reduce((sum, item) => sum + item.sessions, 0), 1)
              )
            )}
          </div>
          <div className="text-sm text-gray-600">Avg per Session</div>
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-purple-500 rounded"></div>
          <span className="text-gray-700">Earnings</span>
        </div>
        <div className="text-gray-500">
          Hover over bars for details
        </div>
      </div>

      {/* Trend Analysis */}
      {data.length > 1 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Trend Analysis</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Best performing period: </span>
              <span className="font-medium text-gray-900">
                {data.reduce((best, current) => 
                  current.earnings > best.earnings ? current : best
                ).month}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Growth trend: </span>
              <span className={`font-medium ${
                data[data.length - 1].earnings > data[0].earnings 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {data[data.length - 1].earnings > data[0].earnings ? '↗ Increasing' : '↘ Decreasing'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

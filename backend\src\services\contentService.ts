import { Resource, IResource } from "@/models/Resource";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import { uploadToCloudinary } from "@/utils/cloudinary";
import mongoose from "mongoose";

export interface CreateContentData {
  title: string;
  description: string;
  content?: string;
  type: "article" | "video" | "audio" | "tool" | "worksheet" | "guide";
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedReadTime?: number; // in minutes
  estimatedDuration?: number; // in minutes for videos/audio
  targetAudience: string[];
  mentalHealthTopics: string[];
  isPublished: boolean;
  isPremium: boolean;
  metadata?: {
    videoUrl?: string;
    audioUrl?: string;
    downloadUrl?: string;
    externalUrl?: string;
    thumbnailUrl?: string;
    fileSize?: number;
    format?: string;
  };
  seoData?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
    slug?: string;
  };
}

export interface ContentFilters {
  type?: string[];
  category?: string;
  subcategory?: string;
  tags?: string[];
  difficulty?: string[];
  targetAudience?: string[];
  mentalHealthTopics?: string[];
  isPremium?: boolean;
  isPublished?: boolean;
  search?: string;
  authorId?: string;
  minRating?: number;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface ContentInteraction {
  userId: string;
  action: "view" | "like" | "bookmark" | "share" | "download" | "complete";
  metadata?: any;
}

export class ContentService {
  /**
   * Create new content
   */
  static async createContent(
    authorId: string,
    data: CreateContentData
  ): Promise<IResource> {
    try {
      // Generate slug if not provided
      const slug = data.seoData?.slug || this.generateSlug(data.title);

      // Check if slug already exists in either seoData.slug or seo.slug
      const existingContent = await Resource.findOne({
        $or: [{ "seoData.slug": slug }, { "seo.slug": slug }],
      });
      if (existingContent) {
        throw createError("Content with this slug already exists", 400);
      }

      const content = new Resource({
        title: data.title,
        description: data.description,
        content: data.content,
        type: data.type,
        category: data.category,
        subcategory: data.subcategory,
        tags: data.tags,
        difficulty: data.difficulty,
        estimatedReadTime: data.estimatedReadTime,
        estimatedDuration: data.estimatedDuration,
        targetAudience: data.targetAudience,
        mentalHealthTopics: data.mentalHealthTopics,
        isPublished: data.isPublished,
        isPremium: data.isPremium,
        metadata: data.metadata || {},
        seoData: {
          metaTitle: data.seoData?.metaTitle || data.title,
          metaDescription: data.seoData?.metaDescription || data.description,
          keywords: data.seoData?.keywords || data.tags,
          slug,
        },
        author: authorId,
        publishedAt: data.isPublished ? new Date() : undefined,
      });

      await content.save();

      logger.info(`Content created: ${content.title} by ${authorId}`);
      return content;
    } catch (error) {
      logger.error("Create content error:", error);
      throw error;
    }
  }

  /**
   * Get content with filters and pagination
   */
  static async getContent(filters: ContentFilters = {}, options: any = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "publishedAt",
        sortOrder = "desc",
      } = options;

      // Build query
      const query: any = {};

      if (filters.type?.length) query.type = { $in: filters.type };
      if (filters.category) query.category = filters.category;
      if (filters.subcategory) query.subcategory = filters.subcategory;
      if (filters.tags?.length) query.tags = { $in: filters.tags };
      if (filters.difficulty?.length)
        query.difficulty = { $in: filters.difficulty };
      if (filters.targetAudience?.length)
        query.targetAudience = { $in: filters.targetAudience };
      if (filters.mentalHealthTopics?.length)
        query.mentalHealthTopics = { $in: filters.mentalHealthTopics };
      if (filters.isPremium !== undefined) query.isPremium = filters.isPremium;
      if (filters.isPublished !== undefined)
        query.isPublished = filters.isPublished;
      if (filters.authorId) query.author = filters.authorId;
      if (filters.minRating)
        query["statistics.averageRating"] = { $gte: filters.minRating };

      // Date range filter
      if (filters.dateFrom || filters.dateTo) {
        query.publishedAt = {};
        if (filters.dateFrom) query.publishedAt.$gte = filters.dateFrom;
        if (filters.dateTo) query.publishedAt.$lte = filters.dateTo;
      }

      // Search functionality
      if (filters.search) {
        query.$or = [
          { title: { $regex: filters.search, $options: "i" } },
          { description: { $regex: filters.search, $options: "i" } },
          { content: { $regex: filters.search, $options: "i" } },
          { tags: { $regex: filters.search, $options: "i" } },
          { mentalHealthTopics: { $regex: filters.search, $options: "i" } },
        ];
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === "asc" ? 1 : -1;

      const [content, total] = await Promise.all([
        Resource.find(query)
          .populate("author", "firstName lastName profilePicture")
          .sort(sortOptions)
          .skip(skip)
          .limit(limit),
        Resource.countDocuments(query),
      ]);

      return {
        content,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get content error:", error);
      throw error;
    }
  }

  /**
   * Get content by ID or slug
   */
  static async getContentById(identifier: string): Promise<IResource> {
    try {
      let content;

      // Try to find by ID first, then by slug
      if (identifier.match(/^[0-9a-fA-F]{24}$/)) {
        content = await Resource.findById(identifier).populate(
          "author",
          "_id firstName lastName profilePicture bio"
        );
      } else {
        // Try both seoData.slug and seo.slug for compatibility
        content = await Resource.findOne({
          $or: [{ "seoData.slug": identifier }, { "seo.slug": identifier }],
        }).populate("author", "_id firstName lastName profilePicture bio");
      }

      if (!content) {
        throw createError("Content not found", 404);
      }

      return content;
    } catch (error) {
      logger.error("Get content by ID error:", error);
      throw error;
    }
  }

  /**
   * Update content
   */
  static async updateContent(
    contentId: string,
    updateData: Partial<CreateContentData>
  ): Promise<IResource> {
    try {
      const content = await Resource.findById(contentId);

      if (!content) {
        throw createError("Content not found", 404);
      }

      // Update fields
      Object.keys(updateData).forEach((key) => {
        if (updateData[key as keyof CreateContentData] !== undefined) {
          if (key === "seoData") {
            content.seoData = { ...content.seoData, ...updateData.seoData };
          } else if (key === "metadata") {
            content.metadata = { ...content.metadata, ...updateData.metadata };
          } else {
            (content as any)[key] = updateData[key as keyof CreateContentData];
          }
        }
      });

      // Update published date if publishing for the first time
      if (updateData.isPublished && !content.publishedAt) {
        content.publishedAt = new Date();
      }

      await content.save();

      logger.info(`Content updated: ${contentId}`);
      return content;
    } catch (error) {
      logger.error("Update content error:", error);
      throw error;
    }
  }

  /**
   * Delete content
   */
  static async deleteContent(contentId: string): Promise<void> {
    try {
      const content = await Resource.findById(contentId);

      if (!content) {
        throw createError("Content not found", 404);
      }

      await Resource.findByIdAndDelete(contentId);

      logger.info(`Content deleted: ${contentId}`);
    } catch (error) {
      logger.error("Delete content error:", error);
      throw error;
    }
  }

  /**
   * Track content interaction
   */
  static async trackInteraction(
    contentId: string,
    interaction: ContentInteraction
  ): Promise<void> {
    try {
      const content = await Resource.findById(contentId);

      if (!content) {
        throw createError("Content not found", 404);
      }

      // Convert userId to ObjectId if it's a string
      let userObjectId;
      try {
        userObjectId = new mongoose.Types.ObjectId(interaction.userId);
      } catch (error) {
        console.warn(
          `Invalid userId format: ${interaction.userId}, skipping interaction tracking`
        );
        return;
      }

      // Add interaction to content
      const interactionData = {
        userId: userObjectId,
        action: interaction.action,
        timestamp: new Date(),
        metadata: interaction.metadata || {},
      };

      content.interactions.push(interactionData as any);

      // Update statistics based on interaction type
      switch (interaction.action) {
        case "view":
          content.statistics.views += 1;
          break;
        case "like":
          content.statistics.likes += 1;
          break;
        case "bookmark":
          content.statistics.bookmarks += 1;
          break;
        case "share":
          content.statistics.shares += 1;
          break;
        case "download":
          content.statistics.downloads += 1;
          break;
        case "complete":
          content.statistics.completions += 1;
          break;
      }

      await content.save();

      logger.debug(
        `Interaction tracked: ${interaction.action} on ${contentId} by ${interaction.userId}`
      );
    } catch (error) {
      logger.error("Track interaction error:", error);
      throw error;
    }
  }

  /**
   * Add content rating
   */
  static async addRating(
    contentId: string,
    userId: string,
    rating: number,
    review?: string
  ): Promise<IResource> {
    try {
      const content = await Resource.findById(contentId);

      if (!content) {
        throw createError("Content not found", 404);
      }

      if (rating < 1 || rating > 5) {
        throw createError("Rating must be between 1 and 5", 400);
      }

      // Check if user already rated this content
      const existingRatingIndex = content.ratings.findIndex(
        (r: any) => r.userId.toString() === userId
      );

      const ratingData = {
        userId: userId as any,
        rating,
        review: review || "",
        createdAt: new Date(),
      };

      if (existingRatingIndex > -1) {
        // Update existing rating
        content.ratings[existingRatingIndex] = ratingData as any;
      } else {
        // Add new rating
        content.ratings.push(ratingData as any);
      }

      // Recalculate average rating
      const totalRating = content.ratings.reduce(
        (sum: number, r: any) => sum + r.rating,
        0
      );
      content.statistics.averageRating = totalRating / content.ratings.length;
      content.statistics.totalRatings = content.ratings.length;

      await content.save();

      logger.info(
        `Rating added: ${rating} stars for content ${contentId} by ${userId}`
      );
      return content;
    } catch (error) {
      logger.error("Add rating error:", error);
      throw error;
    }
  }

  /**
   * Get user bookmarks
   */
  static async getUserBookmarks(
    userId: string,
    options: any = {}
  ): Promise<any> {
    try {
      const { page = 1, limit = 20, type, category } = options;

      const query: any = {
        "interactions.userId": userId,
        "interactions.action": "bookmark",
      };

      if (type) query.type = type;
      if (category) query.category = category;

      const skip = (page - 1) * limit;

      const [bookmarks, total] = await Promise.all([
        Resource.find(query)
          .populate("author", "firstName lastName profilePicture")
          .sort({ "interactions.timestamp": -1 })
          .skip(skip)
          .limit(limit),
        Resource.countDocuments(query),
      ]);

      return {
        bookmarks,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get user bookmarks error:", error);
      throw error;
    }
  }

  /**
   * Get content recommendations
   */
  static async getRecommendations(
    userId: string,
    limit: number = 10
  ): Promise<IResource[]> {
    try {
      // Get user's interaction history
      const userInteractions = await Resource.find({
        "interactions.userId": userId,
      }).select("category mentalHealthTopics tags");

      // Extract user preferences
      const categories = new Set<string>();
      const topics = new Set<string>();
      const tags = new Set<string>();

      userInteractions.forEach((content) => {
        if (content.category) categories.add(content.category);
        content.mentalHealthTopics.forEach((topic) => topics.add(topic));
        content.tags.forEach((tag) => tags.add(tag));
      });

      // Build recommendation query
      const query: any = {
        isPublished: true,
        "interactions.userId": { $ne: userId }, // Exclude already interacted content
      };

      if (categories.size > 0 || topics.size > 0 || tags.size > 0) {
        query.$or = [];

        if (categories.size > 0) {
          query.$or.push({ category: { $in: Array.from(categories) } });
        }
        if (topics.size > 0) {
          query.$or.push({ mentalHealthTopics: { $in: Array.from(topics) } });
        }
        if (tags.size > 0) {
          query.$or.push({ tags: { $in: Array.from(tags) } });
        }
      }

      const recommendations = await Resource.find(query)
        .populate("author", "firstName lastName profilePicture")
        .sort({ "statistics.averageRating": -1, "statistics.views": -1 })
        .limit(limit);

      return recommendations;
    } catch (error) {
      logger.error("Get recommendations error:", error);
      throw error;
    }
  }

  /**
   * Get trending content
   */
  static async getTrendingContent(
    timeframe: "day" | "week" | "month" = "week",
    limit: number = 10
  ): Promise<IResource[]> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (timeframe) {
        case "day":
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case "week":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "month":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }

      const trending = await Resource.aggregate([
        {
          $match: {
            isPublished: true,
            publishedAt: { $gte: startDate },
          },
        },
        {
          $addFields: {
            trendingScore: {
              $add: [
                { $multiply: ["$statistics.views", 1] },
                { $multiply: ["$statistics.likes", 3] },
                { $multiply: ["$statistics.bookmarks", 5] },
                { $multiply: ["$statistics.shares", 4] },
                { $multiply: ["$statistics.averageRating", 10] },
              ],
            },
          },
        },
        {
          $sort: { trendingScore: -1 },
        },
        {
          $limit: limit,
        },
        {
          $lookup: {
            from: "users",
            localField: "author",
            foreignField: "_id",
            as: "author",
            pipeline: [
              { $project: { firstName: 1, lastName: 1, profilePicture: 1 } },
            ],
          },
        },
        {
          $unwind: "$author",
        },
      ]);

      return trending;
    } catch (error) {
      logger.error("Get trending content error:", error);
      throw error;
    }
  }

  /**
   * Upload content media
   */
  static async uploadMedia(
    file: Express.Multer.File,
    type: "thumbnail" | "video" | "audio" | "document"
  ): Promise<string> {
    try {
      const folder = `content/${type}s`;
      const url = await uploadToCloudinary(file, folder);

      logger.info(`Content media uploaded: ${type} - ${file.originalname}`);
      return url;
    } catch (error) {
      logger.error("Upload content media error:", error);
      throw error;
    }
  }

  /**
   * Generate content slug
   */
  private static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  /**
   * Get content categories
   */
  static async getCategories(): Promise<any[]> {
    try {
      const categories = await Resource.aggregate([
        { $match: { isPublished: true } },
        {
          $group: {
            _id: "$category",
            count: { $sum: 1 },
            subcategories: { $addToSet: "$subcategory" },
          },
        },
        { $sort: { count: -1 } },
      ]);

      return categories.map((cat) => ({
        name: cat._id,
        count: cat.count,
        subcategories: cat.subcategories.filter(Boolean),
      }));
    } catch (error) {
      logger.error("Get categories error:", error);
      throw error;
    }
  }

  /**
   * Get popular tags
   */
  static async getPopularTags(limit: number = 20): Promise<any[]> {
    try {
      const tags = await Resource.aggregate([
        { $match: { isPublished: true } },
        { $unwind: "$tags" },
        {
          $group: {
            _id: "$tags",
            count: { $sum: 1 },
          },
        },
        { $sort: { count: -1 } },
        { $limit: limit },
      ]);

      return tags.map((tag) => ({
        name: tag._id,
        count: tag.count,
      }));
    } catch (error) {
      logger.error("Get popular tags error:", error);
      throw error;
    }
  }
}

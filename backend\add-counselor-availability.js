// Add availability schedules to existing counselors
const { MongoClient } = require("mongodb");

const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/theramea";

async function addCounselorAvailability() {
  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log("Connected to MongoDB");

    const db = client.db();
    const counselorsCollection = db.collection("counselors");

    // Get all counselors
    const counselors = await counselorsCollection.find({}).toArray();
    console.log(`Found ${counselors.length} counselors`);

    // Default availability schedule (Monday to Friday, 9 AM to 5 PM)
    const defaultSchedule = new Map([
      [
        "monday",
        {
          isAvailable: true,
          timeSlots: [
            { startTime: "09:00", endTime: "12:00" },
            { startTime: "14:00", endTime: "17:00" },
          ],
        },
      ],
      [
        "tuesday",
        {
          isAvailable: true,
          timeSlots: [
            { startTime: "09:00", endTime: "12:00" },
            { startTime: "14:00", endTime: "17:00" },
          ],
        },
      ],
      [
        "wednesday",
        {
          isAvailable: true,
          timeSlots: [
            { startTime: "09:00", endTime: "12:00" },
            { startTime: "14:00", endTime: "17:00" },
          ],
        },
      ],
      [
        "thursday",
        {
          isAvailable: true,
          timeSlots: [
            { startTime: "09:00", endTime: "12:00" },
            { startTime: "14:00", endTime: "17:00" },
          ],
        },
      ],
      [
        "friday",
        {
          isAvailable: true,
          timeSlots: [
            { startTime: "09:00", endTime: "12:00" },
            { startTime: "14:00", endTime: "17:00" },
          ],
        },
      ],
      [
        "saturday",
        {
          isAvailable: false,
          timeSlots: [],
        },
      ],
      [
        "sunday",
        {
          isAvailable: false,
          timeSlots: [],
        },
      ],
    ]);

    // Update each counselor
    for (const counselor of counselors) {
      // Convert Map to object for MongoDB storage
      const scheduleObj = {};
      for (const [key, value] of defaultSchedule) {
        scheduleObj[key] = value;
      }

      const result = await counselorsCollection.updateOne(
        { _id: counselor._id },
        {
          $set: {
            "availability.schedule": scheduleObj,
          },
        }
      );

      if (result.modifiedCount > 0) {
        console.log(
          `Updated availability for counselor: ${
            counselor.userId?.firstName || "Unknown"
          } ${counselor.userId?.lastName || ""}`
        );
      }
    }

    console.log("✅ All counselors updated with availability schedules");
  } catch (error) {
    console.error("❌ Error updating counselor availability:", error);
  } finally {
    await client.close();
  }
}

addCounselorAvailability();

"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import { resourcesAPI } from "@/lib/resources";
import { Resource, RESOURCE_TYPES, DIFFICULTY_LEVELS } from "@/types/resources";
import Header from "@/components/layout/Header";
import ResourceCard from "@/components/resources/ResourceCard";
import SignupPromptModal from "@/components/modals/SignupPromptModal";
import RatingModal from "@/components/modals/RatingModal";

export default function ResourcePage() {
  const router = useRouter();
  const params = useParams();
  const slug = params.slug as string;

  const {
    isAuthenticated,
    isGuest,
    checkAuth,
    isLoading,
    tokens,
    guestToken,
    user,
  } = useAuthStore();
  const [resource, setResource] = useState<Resource | null>(null);
  const [similarResources, setSimilarResources] = useState<Resource[]>([]);
  const [resourceLoading, setResourceLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [modalFeature, setModalFeature] = useState<"bookmark" | "rate">(
    "bookmark"
  );

  const token = tokens?.accessToken || guestToken;
  const authToken = tokens?.accessToken; // Only for authenticated actions

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (slug) {
      fetchResource();
    }
  }, [slug]);

  const fetchResource = async () => {
    try {
      setResourceLoading(true);
      setError(null);

      const response = await resourcesAPI.getResource(slug, token || undefined);
      const resourceData = response.data.content;

      setResource(resourceData);

      // Check if user has bookmarked this resource
      if (
        user &&
        resourceData.bookmarkedBy &&
        resourceData.bookmarkedBy.includes(user._id)
      ) {
        setIsBookmarked(true);
      }

      // Get user's rating if exists
      if (user && resourceData.ratings) {
        const userRatingData = resourceData.ratings.find(
          (r) => r.userId === user._id
        );
        if (userRatingData) {
          setUserRating(userRatingData.rating);
        }
      }

      // Fetch similar resources
      const similarResponse = await resourcesAPI.getSimilarResources(
        resourceData._id,
        4,
        token || undefined
      );
      setSimilarResources(similarResponse.data.content || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load resource");
      setSimilarResources([]); // Ensure it's always an array even on error
    } finally {
      setResourceLoading(false);
    }
  };

  const handleBookmark = async () => {
    if (!isAuthenticated || isGuest || !authToken || !resource) {
      if (!isAuthenticated || isGuest) {
        setModalFeature("bookmark");
        setShowSignupModal(true);
      }
      return;
    }

    try {
      if (isBookmarked) {
        await resourcesAPI.unbookmarkResource(resource._id, authToken);
        setIsBookmarked(false);
      } else {
        await resourcesAPI.bookmarkResource(resource._id, authToken);
        setIsBookmarked(true);
      }
    } catch (error) {
      console.error("Bookmark error:", error);
    }
  };

  const handleRating = async (rating: number, review?: string) => {
    if (!isAuthenticated || isGuest || !authToken || !resource) {
      if (!isAuthenticated || isGuest) {
        setModalFeature("rate");
        setShowSignupModal(true);
      }
      return;
    }

    try {
      await resourcesAPI.rateResource(resource._id, rating, review, authToken);
      setUserRating(rating);
      setShowRatingModal(false);

      // Refresh resource to get updated ratings
      fetchResource();
    } catch (error) {
      console.error("Rating error:", error);
    }
  };

  if (isLoading || resourceLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <svg
              className="w-12 h-12 text-red-400 mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Error</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/resources"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Resources
            </Link>
          </div>
        </main>
      </div>
    );
  }

  if (!resource) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Resource not found
            </h1>
            <p className="text-gray-600 mb-4">
              This resource may have been removed or you don't have access.
            </p>
            <Link
              href="/resources"
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
            >
              Back to Resources
            </Link>
          </div>
        </main>
      </div>
    );
  }

  const typeInfo = RESOURCE_TYPES.find((t) => t.value === resource.type);
  const difficultyInfo = DIFFICULTY_LEVELS.find(
    (d) => d.value === resource.difficulty
  );

  const formatDuration = () => {
    if (resource.type === "article" && resource.estimatedReadTime) {
      return `${resource.estimatedReadTime} min read`;
    }
    if (
      (resource.type === "video" || resource.type === "audio") &&
      resource.estimatedDuration
    ) {
      return `${resource.estimatedDuration} min`;
    }
    return null;
  };

  const formatRating = (rating: number) => {
    return "★".repeat(Math.floor(rating)) + "☆".repeat(5 - Math.floor(rating));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link
                href="/resources"
                className="text-gray-500 hover:text-gray-700"
              >
                Resources
              </Link>
            </li>
            <li>
              <svg
                className="flex-shrink-0 h-5 w-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </li>
            <li>
              <span className="text-gray-900 font-medium">
                {resource.title}
              </span>
            </li>
          </ol>
        </nav>

        {/* Resource Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-4">
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    resource.type === "video"
                      ? "bg-red-100 text-red-800"
                      : resource.type === "audio"
                      ? "bg-green-100 text-green-800"
                      : resource.type === "worksheet"
                      ? "bg-blue-100 text-blue-800"
                      : resource.type === "tool"
                      ? "bg-indigo-100 text-indigo-800"
                      : resource.type === "guide"
                      ? "bg-purple-100 text-purple-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {typeInfo?.icon} {typeInfo?.label}
                  {resource.type === "video" && " • Watch"}
                  {resource.type === "audio" && " • Listen"}
                  {(resource.type === "worksheet" ||
                    resource.type === "tool") &&
                    " • Download"}
                  {(resource.type === "article" || resource.type === "guide") &&
                    " • Read"}
                </span>

                {difficultyInfo && (
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      difficultyInfo.color === "green"
                        ? "bg-green-100 text-green-800"
                        : difficultyInfo.color === "yellow"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {difficultyInfo.label}
                  </span>
                )}

                {resource.isPremium && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    <svg
                      className="w-3 h-3 mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    Premium
                  </span>
                )}
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {resource.title}
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                {resource.description}
              </p>

              {/* Meta Information */}
              <div className="flex items-center space-x-6 text-sm text-gray-500 mb-6">
                <div className="flex items-center">
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  {resource.statistics.views} views
                </div>

                {formatDuration() && (
                  <div className="flex items-center">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    {formatDuration()}
                  </div>
                )}

                {resource.statistics.totalRatings > 0 && (
                  <div className="flex items-center">
                    <span className="text-yellow-400 mr-1">
                      {formatRating(resource.statistics.averageRating)}
                    </span>
                    <span>({resource.statistics.totalRatings} ratings)</span>
                  </div>
                )}
              </div>

              {/* Author */}
              <div className="flex items-center space-x-3 mb-6">
                {resource.author.profilePicture ? (
                  <img
                    src={resource.author.profilePicture}
                    alt={resource.author.name}
                    className="w-10 h-10 rounded-full"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600">
                      {resource.author.name.charAt(0)}
                    </span>
                  </div>
                )}
                <div>
                  <p className="font-medium text-gray-900">
                    {resource.author.name}
                  </p>
                  {resource.author.credentials && (
                    <p className="text-sm text-gray-500">
                      {resource.author.credentials}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleBookmark}
                className={`flex items-center px-4 py-2 rounded-md border transition-colors ${
                  isBookmarked
                    ? "bg-purple-50 border-purple-200 text-purple-700"
                    : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
                }`}
                title={
                  isAuthenticated && !isGuest
                    ? isBookmarked
                      ? "Bookmarked"
                      : "Bookmark"
                    : "Sign up to bookmark this resource"
                }
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill={isBookmarked ? "currentColor" : "none"}
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                  />
                </svg>
                {isBookmarked ? "Bookmarked" : "Bookmark"}
              </button>

              <button
                onClick={() => {
                  if (!isAuthenticated || isGuest) {
                    setModalFeature("rate");
                    setShowSignupModal(true);
                  } else {
                    setShowRatingModal(true);
                  }
                }}
                className="flex items-center px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
                title={
                  isAuthenticated && !isGuest
                    ? userRating > 0
                      ? `Rated ${userRating}/5`
                      : "Rate"
                    : "Sign up to rate this resource"
                }
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                  />
                </svg>
                {userRating > 0 ? `Rated ${userRating}/5` : "Rate"}
              </button>
            </div>
          </div>

          {/* Tags */}
          {resource.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {resource.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Resource Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          {/* Video Content */}
          {resource.type === "video" && (
            <div className="mb-8">
              {resource.media?.videoUrl ? (
                <div>
                  <div className="flex items-center mb-4">
                    <svg
                      className="w-5 h-5 mr-2 text-red-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                    </svg>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Watch Video
                    </h3>
                    {resource.estimatedDuration && (
                      <span className="ml-2 text-sm text-gray-500">
                        ({resource.estimatedDuration} min)
                      </span>
                    )}
                  </div>
                  <video
                    controls
                    className="w-full rounded-lg shadow-lg"
                    poster={resource.media?.thumbnailUrl}
                  >
                    <source src={resource.media.videoUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                  <p className="mt-2 text-gray-500">
                    Video content not available
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Audio Content */}
          {resource.type === "audio" && (
            <div className="mb-8">
              {resource.media?.audioUrl ? (
                <div>
                  <div className="flex items-center mb-4">
                    <svg
                      className="w-5 h-5 mr-2 text-green-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793l-4.998-3.798a1 1 0 01-.383-.793V7.798a1 1 0 01.383-.793l4.998-3.798a1 1 0 011-.131zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Listen to Audio
                    </h3>
                    {resource.estimatedDuration && (
                      <span className="ml-2 text-sm text-gray-500">
                        ({resource.estimatedDuration} min)
                      </span>
                    )}
                  </div>
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <audio controls className="w-full">
                      <source src={resource.media.audioUrl} type="audio/mpeg" />
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
                    />
                  </svg>
                  <p className="mt-2 text-gray-500">
                    Audio content not available
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Worksheet/Tool Download */}
          {(resource.type === "worksheet" || resource.type === "tool") && (
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <svg
                  className="w-5 h-5 mr-2 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z"
                    clipRule="evenodd"
                  />
                </svg>
                <h3 className="text-lg font-semibold text-gray-900">
                  {resource.type === "worksheet" ? "Worksheet" : "Tool"}
                </h3>
              </div>

              {resource.media?.downloadUrl ? (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-8 w-8 text-blue-600"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-4 flex-1">
                      <h4 className="text-lg font-medium text-blue-900">
                        Download{" "}
                        {resource.type === "worksheet" ? "Worksheet" : "Tool"}
                      </h4>
                      <p className="mt-1 text-sm text-blue-700">
                        {resource.type === "worksheet"
                          ? "Download this worksheet to work through the exercises offline."
                          : "Download this tool to use for your personal development."}
                        {resource.media.fileSize && (
                          <span className="block mt-1">
                            File size:{" "}
                            {(resource.media.fileSize / 1024 / 1024).toFixed(1)}{" "}
                            MB
                          </span>
                        )}
                      </p>
                      <a
                        href={resource.media.downloadUrl}
                        download
                        className="mt-3 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        Download{" "}
                        {resource.type === "worksheet" ? "Worksheet" : "Tool"}
                      </a>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <p className="mt-2 text-gray-500">Download not available</p>
                </div>
              )}
            </div>
          )}

          {/* Article/Guide Content */}
          {(resource.type === "article" || resource.type === "guide") && (
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <svg
                  className="w-5 h-5 mr-2 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                    clipRule="evenodd"
                  />
                </svg>
                <h3 className="text-lg font-semibold text-gray-900">
                  {resource.type === "guide" ? "Complete Guide" : "Article"}
                </h3>
                {resource.estimatedReadTime && (
                  <span className="ml-2 text-sm text-gray-500">
                    ({resource.estimatedReadTime} min read)
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Text Content for all types */}
          <div
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: resource.content }}
          />

          {/* Additional Download Option (for any type that has downloadable content) */}
          {resource.media?.downloadUrl &&
            !["worksheet", "tool"].includes(resource.type) && (
              <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                <a
                  href={resource.media?.downloadUrl}
                  download
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  Download Resource
                </a>
              </div>
            )}
        </div>

        {/* Similar Resources */}
        {similarResources && similarResources.length > 0 && (
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Similar Resources
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarResources.map((similarResource) => (
                <ResourceCard
                  key={similarResource._id}
                  resource={similarResource}
                />
              ))}
            </div>
          </div>
        )}
      </main>

      {/* Signup Prompt Modal */}
      <SignupPromptModal
        isOpen={showSignupModal}
        onClose={() => setShowSignupModal(false)}
        feature={modalFeature}
      />

      {/* Rating Modal */}
      {resource && (
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          onSubmit={handleRating}
          resourceTitle={resource.title}
          currentRating={userRating}
        />
      )}
    </div>
  );
}

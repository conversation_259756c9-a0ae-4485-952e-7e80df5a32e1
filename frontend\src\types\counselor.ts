// Counselor and booking types

export interface Counselor {
  _id: string;
  userId: string;
  bio: string;
  specializations: string[];
  qualifications: {
    degree: string;
    institution: string;
    year: number;
    certificateUrl?: string;
  }[];
  licenses: {
    type: string;
    number: string;
    issuingAuthority: string;
    expiryDate: string;
    documentUrl?: string;
  }[];
  experience: {
    years: number;
    description: string;
  };
  pricing: {
    currency: "NGN" | "USD";
    ratePerMinute: number;
    minimumSessionDuration: number;
  };
  availability: {
    timezone: string;
    schedule: {
      [key: string]: {
        isAvailable: boolean;
        timeSlots: {
          startTime: string;
          endTime: string;
        }[];
      };
    };
    unavailableDates: string[];
    availabilityExpiresAt: string;
  };
  verification: {
    status: "pending" | "approved" | "rejected" | "suspended";
    submittedAt: string;
    reviewedAt?: string;
    reviewedBy?: string;
    rejectionReason?: string;
  };
  profile: {
    profilePicture?: string;
    languages: string[];
    approachDescription: string;
    sessionTypes: ("individual" | "group" | "couples" | "family")[];
  };
  statistics: {
    totalSessions: number;
    totalEarnings: number;
    averageRating: number;
    totalReviews: number;
    completionRate: number;
    responseTime: number;
  };
  settings: {
    acceptingNewClients: boolean;
    autoAcceptBookings: boolean;
    requiresApproval: boolean;
    cancellationPolicy: string;
    reschedulePolicy: string;
  };
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    profilePicture?: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  _id: string;
  userId: string;
  counselorId: string;
  scheduledAt: string;
  duration: number;
  status:
    | "scheduled"
    | "in-progress"
    | "completed"
    | "cancelled"
    | "no-show"
    | "rescheduled";
  type: "individual" | "group" | "couples" | "family";
  pricing: {
    currency: "NGN" | "USD";
    ratePerMinute: number;
    totalAmount: number;
    platformFee: number;
    counselorEarnings: number;
  };
  payment: {
    paymentId: string;
    paymentMethod: "paystack" | "stripe";
    status:
      | "pending"
      | "completed"
      | "failed"
      | "refunded"
      | "partially-refunded";
    paidAt?: string;
    refundedAt?: string;
    refundAmount?: number;
    transactionReference: string;
  };
  videoSession: {
    roomId?: string;
    roomUrl?: string;
    provider: "daily.co";
    startedAt?: string;
    endedAt?: string;
    actualDuration?: number;
    recordingUrl?: string;
  };
  notes: {
    counselorNotes?: string;
    sessionSummary?: string;
    nextSteps?: string;
    isPrivate: boolean;
  };
  feedback: {
    userFeedback?: {
      rating: number;
      comment?: string;
      submittedAt: string;
      wouldRecommend: boolean;
    };
    counselorFeedback?: {
      sessionQuality: number;
      clientEngagement: number;
      technicalIssues: boolean;
      notes?: string;
      submittedAt: string;
    };
  };
  counselor?: Counselor;
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AvailabilitySlot {
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  price: number;
  currency: string;
}

export interface BookingRequest {
  counselorId: string;
  userId?: string;
  scheduledAt: string;
  duration: number;
  sessionType: "individual" | "group" | "couples" | "family";
  notes?: string;
  isUrgent?: boolean;
  preferredLanguage?: string;
}

export interface CounselorFilters {
  specializations?: string[];
  languages?: string[];
  sessionTypes?: string[];
  minRating?: number;
  maxRate?: number;
  currency?: "NGN" | "USD";
  availability?: {
    date?: string;
    timeSlot?: string;
  };
  search?: string;
}

export interface CounselorListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Specializations with display information
export const COUNSELOR_SPECIALIZATIONS = [
  {
    value: "anxiety-disorders",
    label: "Anxiety Disorders",
    icon: "😰",
    color: "yellow",
  },
  { value: "depression", label: "Depression", icon: "💙", color: "blue" },
  { value: "trauma-ptsd", label: "Trauma & PTSD", icon: "🛡️", color: "red" },
  {
    value: "relationship-counseling",
    label: "Relationship Counseling",
    icon: "💕",
    color: "pink",
  },
  {
    value: "family-therapy",
    label: "Family Therapy",
    icon: "👨‍👩‍👧‍👦",
    color: "green",
  },
  {
    value: "addiction-recovery",
    label: "Addiction Recovery",
    icon: "🔄",
    color: "purple",
  },
  {
    value: "grief-counseling",
    label: "Grief Counseling",
    icon: "🕊️",
    color: "gray",
  },
  {
    value: "stress-management",
    label: "Stress Management",
    icon: "😌",
    color: "teal",
  },
  {
    value: "career-counseling",
    label: "Career Counseling",
    icon: "💼",
    color: "indigo",
  },
  {
    value: "adolescent-therapy",
    label: "Adolescent Therapy",
    icon: "🧑‍🎓",
    color: "orange",
  },
  {
    value: "couples-therapy",
    label: "Couples Therapy",
    icon: "💑",
    color: "rose",
  },
  {
    value: "eating-disorders",
    label: "Eating Disorders",
    icon: "🍎",
    color: "emerald",
  },
  {
    value: "anger-management",
    label: "Anger Management",
    icon: "😤",
    color: "red",
  },
  {
    value: "sleep-disorders",
    label: "Sleep Disorders",
    icon: "😴",
    color: "slate",
  },
  {
    value: "life-coaching",
    label: "Life Coaching",
    icon: "🎯",
    color: "amber",
  },
  {
    value: "spiritual-counseling",
    label: "Spiritual Counseling",
    icon: "🙏",
    color: "violet",
  },
] as const;

export const SESSION_TYPES = [
  {
    value: "individual",
    label: "Individual",
    description: "One-on-one counseling session",
    icon: "👤",
  },
  {
    value: "couples",
    label: "Couples",
    description: "Counseling for couples",
    icon: "💑",
  },
  {
    value: "family",
    label: "Family",
    description: "Family therapy session",
    icon: "👨‍👩‍👧‍👦",
  },
  {
    value: "group",
    label: "Group",
    description: "Group therapy session",
    icon: "👥",
  },
] as const;

export const LANGUAGES = [
  { value: "english", label: "English" },
  { value: "yoruba", label: "Yoruba" },
  { value: "igbo", label: "Igbo" },
  { value: "hausa", label: "Hausa" },
  { value: "french", label: "French" },
  { value: "spanish", label: "Spanish" },
  { value: "arabic", label: "Arabic" },
] as const;

export const SESSION_DURATIONS = [
  { value: 30, label: "30 minutes", description: "Quick consultation" },
  { value: 45, label: "45 minutes", description: "Standard session" },
  { value: 60, label: "1 hour", description: "Extended session" },
  { value: 90, label: "1.5 hours", description: "Deep dive session" },
] as const;

export type CounselorSpecialization =
  (typeof COUNSELOR_SPECIALIZATIONS)[number]["value"];
export type SessionType = (typeof SESSION_TYPES)[number]["value"];
export type Language = (typeof LANGUAGES)[number]["value"];
export type SessionDuration = (typeof SESSION_DURATIONS)[number]["value"];

// API response interface - matches backend exactly
export interface ApiCounselor {
  _id: string;
  userId: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    fullName: string;
    id: string;
  };
  bio: string;
  specializations: string[];
  experience: {
    years: number;
    description: string;
  };
  pricing: {
    currency: string;
    ratePerMinute: number;
    minimumSessionDuration: number;
  };
  profile: {
    title: string;
    profilePicture?: string;
    languages: string[];
    approachDescription: string;
    sessionTypes: string[];
    approachStyle: string[];
    isOnline: boolean;
  };
  statistics: {
    totalSessions: number;
    totalEarnings: number;
    averageRating: number;
    totalReviews: number;
    completionRate: number;
    responseTime: number;
  };
  qualifications: {
    degree: string;
    institution: string;
    year: number;
    _id: string;
    id: string;
  }[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
  id: string;
}

import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { BookingService } from "@/services/bookingService";
import { PaymentService } from "@/services/paymentService";
import { createError } from "@/middleware/errorHandler";
import { logger } from "@/utils/logger";
import mongoose from "mongoose";

export class BookingController {
  /**
   * Create a new booking
   */
  static async createBooking(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info("=== CREATE BOOKING CALLED ===");
      logger.info("Request method:", req.method);
      logger.info("Request URL:", req.url);
      logger.info("Request body:", req.body);

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logger.error("Validation errors:", errors.array());
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const scheduledAt = new Date(req.body.scheduledAt);
      if (isNaN(scheduledAt.getTime())) {
        return next(createError("Invalid scheduledAt date format", 400));
      }

      const bookingData = {
        ...req.body,
        userId: req.user._id.toString(),
        scheduledAt,
      };

      const session = await BookingService.createBooking(bookingData);

      res.status(201).json({
        success: true,
        message: "Booking created successfully",
        data: { session },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get bookings list
   */
  static async getBookings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const {
        page = 1,
        limit = 20,
        sortBy = "scheduledAt",
        sortOrder = "desc",
        status,
        sessionType,
        dateFrom,
        dateTo,
      } = req.query;

      const filters: any = {};

      // Filter by user role
      if (req.user.role === "user") {
        filters.userId = req.user._id.toString();
      } else if (req.user.role === "counselor") {
        // Get counselor ID from user
        const { CounselorService } = await import(
          "@/services/counselorService"
        );
        const counselor = await CounselorService.getCounselorByUserId(
          req.user._id.toString()
        );
        filters.counselorId = counselor._id.toString();
      }

      if (status) {
        filters.status = Array.isArray(status) ? status : [status];
      }
      if (sessionType) filters.sessionType = sessionType;
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as "asc" | "desc",
      };

      const result = await BookingService.getBookings(filters, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get specific booking
   */
  static async getBooking(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info("=== GET BOOKING CALLED ===");
      logger.info("Request method:", req.method);
      logger.info("Request URL:", req.url);
      logger.info("Session ID param:", req.params.sessionId);

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { sessionId } = req.params;

      logger.info(
        "About to call BookingService.getBooking with sessionId:",
        sessionId
      );
      const session = await BookingService.getBooking(sessionId);
      logger.info("BookingService.getBooking returned:", !!session);

      if (!session) {
        logger.info("Session not found, returning 404");
        return next(createError("Booking not found", 404));
      }

      logger.info("Session found, proceeding with authorization check");

      // Check authorization with comprehensive ID extraction and comparison
      logger.info("=== AUTHORIZATION DEBUG ===");
      logger.info("req.user:", {
        _id: req.user._id,
        role: req.user.role,
        email: req.user.email,
      });
      logger.info("session data:", {
        _id: session._id,
        userId: session.userId,
        counselorId: session.counselorId,
        status: session.status,
      });

      // Helper function to safely extract ObjectId string
      const extractId = (field: any): string | null => {
        if (!field) return null;
        if (typeof field === "string") return field;
        if (field._id) return field._id.toString();
        if (field.toString) return field.toString();
        return null;
      };

      const sessionUserId = extractId(session.userId);
      const sessionCounselorId = extractId(session.counselorId);
      const requestUserId = extractId(req.user._id);

      logger.info("Extracted IDs for comparison:", {
        sessionUserId,
        sessionCounselorId,
        requestUserId,
      });

      const isClient =
        sessionUserId && requestUserId && sessionUserId === requestUserId;
      const isCounselor =
        sessionCounselorId &&
        requestUserId &&
        sessionCounselorId === requestUserId;
      const isAdmin = req.user.role === "admin";

      logger.info("Authorization results:", {
        isClient,
        isCounselor,
        isAdmin,
        sessionUserIdExists: !!sessionUserId,
        requestUserIdExists: !!requestUserId,
        idsMatch: sessionUserId === requestUserId,
      });

      if (!isClient && !isCounselor && !isAdmin) {
        logger.error("Authorization failed - user cannot access this booking");
        logger.error("Detailed comparison:", {
          sessionUserId,
          requestUserId,
          sessionCounselorId,
          userRole: req.user.role,
          exact_match: sessionUserId === requestUserId,
        });
        return next(createError("Unauthorized to view this booking", 403));
      }

      res.json({
        success: true,
        data: { session },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initialize payment for booking
   */
  static async initializePayment(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { sessionId } = req.params;

      // Verify user owns this booking
      const session = await BookingService.getBooking(sessionId);

      if (!session) {
        return next(createError("Booking not found", 404));
      }

      // if (
      //   !session.userId ||
      //   session.userId.toString() !== req.user._id.toString()
      // ) {
      //   return next(createError("Unauthorized to pay for this booking", 403));
      // }

      const sessionUserId =
        typeof session.userId === "object" && session.userId._id
          ? session.userId._id.toString()
          : session.userId.toString();
      const requestUserId = req.user._id.toString();
      if (!sessionUserId || sessionUserId !== requestUserId) {
        return next(createError("Unauthorized to pay for this booking", 403));
      }

      const paymentData = await BookingService.initializePayment(sessionId);

      res.json({
        success: true,
        message: "Payment initialized successfully",
        data: paymentData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify payment
   */
  static async verifyPayment(req: Request, res: Response, next: NextFunction) {
    try {
      const { reference } = req.params;
      const session = await BookingService.verifyPayment(reference);

      res.json({
        success: true,
        message: "Payment verified successfully",
        data: { session },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { sessionId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError("Cancellation reason is required", 400));
      }

      // Verify user can cancel this booking
      const session = await BookingService.getBooking(sessionId);

      if (!session) {
        return next(createError("Booking not found", 404));
      }

      // Debug logging for cancellation authorization
      console.log("=== CANCEL BOOKING DEBUG ===");
      console.log("Session ID:", sessionId);
      console.log("Session userId:", session.userId);
      console.log("Session userId type:", typeof session.userId);
      console.log("Session userId toString():", session.userId?.toString());
      console.log("Session counselorId:", session.counselorId);
      console.log(
        "Session counselorId toString():",
        session.counselorId?.toString()
      );
      console.log("Request user _id:", req.user._id);
      console.log("Request user _id type:", typeof req.user._id);
      console.log("Request user _id toString():", req.user._id.toString());
      console.log("Session status:", session.status);

      // Handle both populated and non-populated user fields
      const getIdString = (field: any): string | null => {
        if (!field) return null;
        if (typeof field === "string") return field;
        if (field._id) return field._id.toString();
        if (field.toString) return field.toString();
        return null;
      };

      const sessionUserId = getIdString(session.userId);
      const sessionCounselorId = getIdString(session.counselorId);
      const requestUserId = req.user._id.toString();

      const isClient = sessionUserId && sessionUserId === requestUserId;
      const isCounselor =
        sessionCounselorId && sessionCounselorId === requestUserId;

      console.log("Authorization results:");
      console.log("isClient:", isClient);
      console.log("isCounselor:", isCounselor);
      console.log("User role:", req.user.role);

      if (!isClient && !isCounselor) {
        console.log(
          "❌ Authorization failed - user is neither client nor counselor"
        );
        return next(createError("Unauthorized to cancel this booking", 403));
      }

      console.log("✅ Authorization successful - proceeding with cancellation");

      const cancelledSession = await BookingService.cancelBooking(
        sessionId,
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: "Booking cancelled successfully",
        data: { session: cancelledSession },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reschedule booking
   */
  static async rescheduleBooking(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { sessionId } = req.params;
      const { newScheduledAt, reason } = req.body;

      if (!newScheduledAt) {
        return next(createError("New scheduled time is required", 400));
      }

      // Verify user can reschedule this booking
      const session = await BookingService.getBooking(sessionId);

      if (!session) {
        return next(createError("Booking not found", 404));
      }

      // Handle both populated and non-populated user fields
      const getIdString = (field: any): string | null => {
        if (!field) return null;
        if (typeof field === "string") return field;
        if (field._id) return field._id.toString();
        if (field.toString) return field.toString();
        return null;
      };

      const sessionUserId = getIdString(session.userId);
      const sessionCounselorId = getIdString(session.counselorId);
      const requestUserId = req.user._id.toString();

      const isClient = sessionUserId && sessionUserId === requestUserId;
      const isCounselor =
        sessionCounselorId && sessionCounselorId === requestUserId;

      if (!isClient && !isCounselor) {
        return next(
          createError("Unauthorized to reschedule this booking", 403)
        );
      }

      const rescheduledSession = await BookingService.rescheduleBooking(
        sessionId,
        new Date(newScheduledAt),
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: "Booking rescheduled successfully",
        data: { session: rescheduledSession },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Approve booking (counselor only)
   */
  static async approveBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      if (req.user.role !== "counselor") {
        return next(createError("Only counselors can approve bookings", 403));
      }

      const { sessionId } = req.params;

      // Get counselor ID
      const { CounselorService } = await import("@/services/counselorService");
      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      const approvedSession = await BookingService.approveBooking(
        sessionId,
        counselor._id.toString()
      );

      res.json({
        success: true,
        message: "Booking approved successfully",
        data: { session: approvedSession },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reject booking (counselor only)
   */
  static async rejectBooking(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      if (req.user.role !== "counselor") {
        return next(createError("Only counselors can reject bookings", 403));
      }

      const { sessionId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError("Rejection reason is required", 400));
      }

      // Get counselor ID
      const { CounselorService } = await import("@/services/counselorService");
      const counselor = await CounselorService.getCounselorByUserId(
        req.user._id.toString()
      );

      const rejectedSession = await BookingService.rejectBooking(
        sessionId,
        counselor._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: "Booking rejected successfully",
        data: { session: rejectedSession },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Calculate pricing for a session
   */
  static async calculatePricing(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { counselorId, duration, isUrgent } = req.query;

      if (!counselorId || !duration) {
        return next(createError("Counselor ID and duration are required", 400));
      }

      // Validate counselorId format
      if (!mongoose.Types.ObjectId.isValid(counselorId as string)) {
        return next(createError("Invalid counselor ID format", 400));
      }

      const pricing = await BookingService.calculatePricing(
        counselorId as string,
        parseInt(duration as string),
        isUrgent === "true"
      );

      res.json({
        success: true,
        data: { pricing },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Handle payment webhook
   */
  static async handlePaymentWebhook(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const signature = req.headers["x-paystack-signature"] as string;
      const payload = JSON.stringify(req.body);

      // Validate webhook signature
      if (!PaymentService.validateWebhookSignature(payload, signature)) {
        return next(createError("Invalid webhook signature", 400));
      }

      const event = req.body;

      switch (event.event) {
        case "charge.success":
          await BookingService.verifyPayment(event.data.reference);
          logger.info(`Payment webhook processed: ${event.data.reference}`);
          break;

        case "charge.failed":
          // Handle failed payment
          logger.info(`Payment failed webhook: ${event.data.reference}`);
          break;

        default:
          logger.info(`Unhandled webhook event: ${event.event}`);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("Payment webhook error:", error);
      res.status(200).json({ success: false }); // Always return 200 to Paystack
    }
  }

  /**
   * Get supported banks
   */
  static async getSupportedBanks(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { country = "nigeria" } = req.query;
      const banks = await PaymentService.getSupportedBanks(country as string);

      res.json({
        success: true,
        data: { banks },
      });
    } catch (error) {
      next(error);
    }
  }
}

import { IUser } from "../models/User";

// Socket.io type definitions
export interface ServerToClientEvents {
  // Chat events
  messageReceived: (data: {
    messageId: string;
    roomId: string;
    senderId: string;
    senderName: string;
    content: string;
    timestamp: string;
    messageType: 'text' | 'image' | 'file';
  }) => void;
  
  userJoined: (data: {
    userId: string;
    userName: string;
    roomId: string;
    timestamp: string;
  }) => void;
  
  userLeft: (data: {
    userId: string;
    userName: string;
    roomId: string;
    timestamp: string;
  }) => void;
  
  roomUpdated: (data: {
    roomId: string;
    updates: any;
  }) => void;
  
  // Session events
  sessionStarted: (data: {
    sessionId: string;
    roomUrl: string;
    participants: string[];
  }) => void;
  
  sessionEnded: (data: {
    sessionId: string;
    duration: number;
    endedBy: string;
  }) => void;
  
  sessionReminder: (data: {
    sessionId: string;
    scheduledAt: string;
    counselorName: string;
    minutesUntil: number;
  }) => void;
  
  // Notification events
  notification: (data: {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: string;
    actionUrl?: string;
  }) => void;
  
  // Presence events
  userOnline: (data: {
    userId: string;
    status: 'online' | 'away' | 'busy';
  }) => void;
  
  userOffline: (data: {
    userId: string;
  }) => void;
}

export interface ClientToServerEvents {
  // Chat events
  joinRoom: (data: {
    roomId: string;
    userId?: string;
    guestId?: string;
  }) => void;
  
  leaveRoom: (data: {
    roomId: string;
    userId?: string;
    guestId?: string;
  }) => void;
  
  sendMessage: (data: {
    roomId: string;
    content: string;
    messageType: 'text' | 'image' | 'file';
    replyTo?: string;
  }) => void;
  
  typing: (data: {
    roomId: string;
    isTyping: boolean;
  }) => void;
  
  // Session events
  joinSession: (data: {
    sessionId: string;
  }) => void;
  
  leaveSession: (data: {
    sessionId: string;
  }) => void;
  
  // Presence events
  updateStatus: (data: {
    status: 'online' | 'away' | 'busy';
  }) => void;
}

export interface InterServerEvents {
  ping: () => void;
}

export interface SocketData {
  user?: IUser;
  userId?: string;
  guestId?: string;
  rooms: string[];
  status: 'online' | 'away' | 'busy';
}

// Socket middleware types
export interface AuthenticatedSocket {
  user?: IUser;
  userId?: string;
  guestId?: string;
  isGuest: boolean;
}

// Chat room types
export interface ChatRoomParticipant {
  userId?: string;
  guestId?: string;
  displayName: string;
  joinedAt: Date;
  isActive: boolean;
  role: 'member' | 'moderator' | 'admin';
}

// Message types
export interface ChatMessage {
  _id: string;
  roomId: string;
  senderId?: string;
  guestId?: string;
  senderName: string;
  content: string;
  messageType: 'text' | 'image' | 'file' | 'system';
  replyTo?: string;
  isEdited: boolean;
  isDeleted: boolean;
  timestamp: Date;
  reactions: {
    emoji: string;
    users: string[];
    count: number;
  }[];
}

// Session types for socket events
export interface SessionSocketData {
  sessionId: string;
  counselorId: string;
  clientId: string;
  roomUrl?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  startedAt?: Date;
  endedAt?: Date;
}

// Notification types
export interface SocketNotification {
  id: string;
  userId: string;
  type: 'session_reminder' | 'session_started' | 'message_received' | 'booking_confirmed' | 'payment_completed';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

// Error types for socket events
export interface SocketError {
  code: string;
  message: string;
  details?: any;
}

// Room management types
export interface RoomManagement {
  roomId: string;
  action: 'mute' | 'unmute' | 'kick' | 'ban' | 'promote' | 'demote';
  targetUserId: string;
  moderatorId: string;
  reason?: string;
  duration?: number; // for temporary actions
}

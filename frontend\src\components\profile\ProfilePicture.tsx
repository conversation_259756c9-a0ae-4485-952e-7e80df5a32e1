"use client";

import { useState, useRef } from "react";
import { User } from "@/types/auth";
import { useAuthStore } from "@/store/authStore";
import {
  CameraIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

interface ProfilePictureProps {
  user: User;
}

export default function ProfilePicture({ user }: ProfilePictureProps) {
  const { checkAuth, tokens } = useAuthStore();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const showMessage = (type: "success" | "error", text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      showMessage("error", "Please select an image file (JPG, PNG, GIF)");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      showMessage("error", "File size must be less than 5MB");
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload file
    uploadProfilePicture(file);
  };

  const uploadProfilePicture = async (file: File) => {
    setIsUploading(true);
    setMessage(null);

    try {
      const formData = new FormData();
      formData.append("profilePicture", file);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/profile/picture`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokens?.accessToken}`,
          },
          body: formData,
        }
      );

      const data = await response.json();

      if (response.ok) {
        // Refresh user data
        await checkAuth(true);
        setPreviewUrl(null); // Clear preview since we now have the actual image
        showMessage("success", "Profile picture updated successfully!");
      } else {
        showMessage(
          "error",
          data.message || "Failed to upload profile picture"
        );
        setPreviewUrl(null);
      }
    } catch (error) {
      console.error("Upload error:", error);
      showMessage(
        "error",
        "Network error. Please check your connection and try again."
      );
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const deleteProfilePicture = async () => {
    const confirmed = window.confirm(
      "Are you sure you want to delete your profile picture?"
    );
    if (!confirmed) return;

    setIsUploading(true);
    setMessage(null);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/profile/picture`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${tokens?.accessToken}`,
          },
        }
      );

      const data = await response.json();

      if (response.ok) {
        // Refresh user data
        await checkAuth(true);
        showMessage("success", "Profile picture removed successfully!");
      } else {
        showMessage(
          "error",
          data.message || "Failed to delete profile picture"
        );
      }
    } catch (error) {
      console.error("Delete error:", error);
      showMessage(
        "error",
        "Network error. Please check your connection and try again."
      );
    } finally {
      setIsUploading(false);
    }
  };

  const currentImageUrl = previewUrl || user.profilePicture;

  return (
    <div className="flex flex-col items-center space-y-6">
      {/* Profile Picture Display */}
      <div className="relative group">
        <div className="w-32 h-32 rounded-full overflow-hidden bg-gradient-to-br from-purple-400 to-indigo-500 flex items-center justify-center shadow-lg">
          {currentImageUrl ? (
            <img
              src={currentImageUrl}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-white text-3xl font-bold">
              {user.firstName?.charAt(0) || ""}
              {user.lastName?.charAt(0) || ""}
            </div>
          )}
        </div>

        {/* Loading Overlay */}
        {isUploading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
            <div className="flex flex-col items-center space-y-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
              <span className="text-white text-xs">Uploading...</span>
            </div>
          </div>
        )}

        {/* Hover Effect */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-full transition-all duration-200 flex items-center justify-center">
          <CameraIcon className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div
          className={`w-full max-w-sm p-3 rounded-lg border flex items-start space-x-2 ${
            message.type === "success"
              ? "bg-green-50 text-green-800 border-green-200"
              : "bg-red-50 text-red-800 border-red-200"
          }`}
        >
          {message.type === "success" ? (
            <CheckCircleIcon className="h-5 w-5 flex-shrink-0 mt-0.5" />
          ) : (
            <ExclamationCircleIcon className="h-5 w-5 flex-shrink-0 mt-0.5" />
          )}
          <p className="text-sm">{message.text}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col space-y-3 w-full max-w-sm">
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <CameraIcon className="h-5 w-5 mr-2" />
          {currentImageUrl ? "Change Photo" : "Upload Photo"}
        </button>

        {currentImageUrl && !previewUrl && (
          <button
            onClick={deleteProfilePicture}
            disabled={isUploading}
            className="inline-flex items-center justify-center px-4 py-3 border border-red-300 shadow-sm text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <TrashIcon className="h-5 w-5 mr-2" />
            Remove Photo
          </button>
        )}
      </div>

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        aria-label="Upload profile picture"
      />

      {/* Help Text */}
      <div className="text-center">
        <p className="text-sm text-gray-500">JPG, PNG or GIF up to 5MB</p>
        <p className="text-xs text-gray-400 mt-1">
          Recommended: Square images work best
        </p>
      </div>
    </div>
  );
}

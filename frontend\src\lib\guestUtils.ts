// Guest utilities for handling guest user functionality
export const getGuestDisplayName = (): string | null => {
  if (typeof window === "undefined") return null;
  return sessionStorage.getItem("guestDisplayName");
};

export const setGuestDisplayName = (name: string): void => {
  if (typeof window === "undefined") return;
  sessionStorage.setItem("guestDisplayName", name);
};

export const clearGuestDisplayName = (): void => {
  if (typeof window === "undefined") return;
  sessionStorage.removeItem("guestDisplayName");
};

export const generateRandomGuestName = (): string => {
  const adjectives = [
    "Calm",
    "Thoughtful",
    "Peaceful",
    "Hopeful",
    "Bright",
    "Gentle",
    "Kind",
    "Wise",
    "Strong",
    "Serene",
    "Curious",
    "Brave",
    "Creative",
    "Happy",
    "Mindful",
  ];

  const animals = [
    "<PERSON>",
    "<PERSON>",
    "Dolphin",
    "<PERSON>",
    "Lion",
    "<PERSON>wl",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "Pan<PERSON>",
    "<PERSON>",
    "Swan",
    "Hummingbird",
  ];

  const randomAdjective =
    adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomAnimal = animals[Math.floor(Math.random() * animals.length)];
  const randomNumber = Math.floor(Math.random() * 99) + 1;

  return `${randomAdjective}${randomAnimal}${randomNumber}`;
};

"use client";

import { useState } from "react";

interface BankDetails {
  accountName: string;
  accountNumber: string;
  bankName: string;
  bankCode: string;
  currency: "NGN" | "USD";
}

interface CounselorBankDetailsFormProps {
  bankDetails?: BankDetails;
  onSave: (data: { bankDetails: BankDetails }) => Promise<void>;
  isLoading?: boolean;
}

export default function CounselorBankDetailsForm({
  bankDetails,
  onSave,
  isLoading,
}: CounselorBankDetailsFormProps) {
  const [formData, setFormData] = useState<BankDetails>({
    accountName: bankDetails?.accountName || "",
    accountNumber: bankDetails?.accountNumber || "",
    bankName: bankDetails?.bankName || "",
    bankCode: bankDetails?.bankCode || "",
    currency: bankDetails?.currency || "NGN",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave({ bankDetails: formData });
  };

  const nigerianBanks = [
    { name: "Access Bank", code: "044" },
    { name: "First Bank of Nigeria", code: "011" },
    { name: "Guaranty Trust Bank", code: "058" },
    { name: "United Bank for Africa", code: "033" },
    { name: "Zenith Bank", code: "057" },
    { name: "Fidelity Bank", code: "070" },
    { name: "FCMB", code: "214" },
    { name: "Sterling Bank", code: "232" },
    { name: "Union Bank", code: "032" },
    { name: "Wema Bank", code: "035" },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Currency
        </label>
        <select
          value={formData.currency}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              currency: e.target.value as "NGN" | "USD",
            }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value="NGN">Nigerian Naira (NGN)</option>
          <option value="USD">US Dollar (USD)</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Account Holder Name
        </label>
        <input
          type="text"
          value={formData.accountName}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, accountName: e.target.value }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="Enter account holder name"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Account Number
        </label>
        <input
          type="text"
          value={formData.accountNumber}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, accountNumber: e.target.value }))
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="Enter account number"
          required
        />
      </div>

      {formData.currency === "NGN" ? (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Bank
          </label>
          <select
            value={formData.bankCode}
            onChange={(e) => {
              const selectedBank = nigerianBanks.find(
                (bank) => bank.code === e.target.value
              );
              setFormData((prev) => ({
                ...prev,
                bankCode: e.target.value,
                bankName: selectedBank?.name || "",
              }));
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            required
          >
            <option value="">Select a bank</option>
            {nigerianBanks.map((bank) => (
              <option key={bank.code} value={bank.code}>
                {bank.name}
              </option>
            ))}
          </select>
        </div>
      ) : (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bank Name
            </label>
            <input
              type="text"
              value={formData.bankName}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, bankName: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Enter bank name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bank Code / Routing Number
            </label>
            <input
              type="text"
              value={formData.bankCode}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, bankCode: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Enter bank code or routing number"
              required
            />
          </div>
        </>
      )}

      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-yellow-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Important Note
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Your bank details will be used for payouts. Please ensure all
                information is accurate. Changes to bank details may require
                re-verification.
              </p>
            </div>
          </div>
        </div>
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
      >
        {isLoading ? "Saving..." : "Save Bank Details"}
      </button>
    </form>
  );
}

const mongoose = require("mongoose");

const MONGODB_URI = "mongodb://theramea-mongodb:27017/theramea_dev";

const resourceSchema = new mongoose.Schema({}, { strict: false });
const Resource = mongoose.model("Resource", resourceSchema);

async function clearTestData() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Count current resources
    const count = await Resource.countDocuments();
    console.log(`📊 Found ${count} resources in database`);

    if (count === 0) {
      console.log("🎉 Database is already empty");
      return;
    }

    // List all resources before deletion
    const resources = await Resource.find({}, "_id title").lean();
    console.log("\n📚 Resources to be deleted:");
    resources.forEach((doc, i) => {
      console.log(`${i + 1}. ID: ${doc._id} | Title: ${doc.title}`);
    });

    // Delete all resources
    const result = await Resource.deleteMany({});
    console.log(`\n🗑️ Deleted ${result.deletedCount} resources`);

    // Verify deletion
    const remainingCount = await Resource.countDocuments();
    console.log(`✅ Database now contains ${remainingCount} resources`);
  } catch (error) {
    console.error("❌ Error clearing data:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

clearTestData();

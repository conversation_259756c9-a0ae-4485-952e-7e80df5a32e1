const mongoose = require("mongoose");

// MongoDB connection URI from environment variables or default
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "****************************************************************";

async function testConnection() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB successfully");

    // Check if we have counselors
    const counselors = await mongoose.connection.db
      .collection("counselors")
      .find({})
      .toArray();
    console.log(`Found ${counselors.length} counselors in database`);

    // Check availability data for each counselor
    for (const counselor of counselors) {
      console.log(
        `\n=== ${counselor.basicInfo?.firstName || "Unknown"} ${
          counselor.basicInfo?.lastName || ""
        } ===`
      );
      console.log(`ID: ${counselor._id}`);
      console.log(
        `Verification: ${counselor.verification?.status || "Not set"}`
      );
      console.log(
        `Accepting Clients: ${
          counselor.settings?.acceptingNewClients || "Not set"
        }`
      );

      if (counselor.availability) {
        console.log("Has availability config: YES");
        if (counselor.availability.schedule) {
          console.log("Has schedule config: YES");
          const schedule = counselor.availability.schedule;
          let hasActiveSchedule = false;

          for (const [day, config] of Object.entries(schedule)) {
            if (
              config.isAvailable &&
              config.timeSlots &&
              config.timeSlots.length > 0
            ) {
              hasActiveSchedule = true;
              console.log(
                `  ${day}: Available (${config.timeSlots.length} slots)`
              );
            }
          }

          if (!hasActiveSchedule) {
            console.log("  ⚠️  No active schedule found");
          }
        } else {
          console.log("Has schedule config: NO");
        }
      } else {
        console.log("Has availability config: NO");
      }
    }
  } catch (error) {
    console.error("❌ Database connection error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

testConnection();

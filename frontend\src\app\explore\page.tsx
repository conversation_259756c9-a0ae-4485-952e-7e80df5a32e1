"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import { getGuestDisplayName } from "@/lib/guestUtils";
import { useGuestPrompts } from "@/hooks/useGuestPrompts";
import GuestMoodModal from "@/components/guest/GuestMoodModal";
import GuestUpgradeModal from "@/components/guest/GuestUpgradeModal";
import Header from "@/components/layout/Header";
import {
  MessageCircle,
  BookOpen,
  UserCheck,
  Calendar,
  Lock,
  Star,
  ChevronRight,
  Heart,
  Users,
} from "lucide-react";

export default function ExplorePage() {
  const router = useRouter();
  const { isGuest, isAuthenticated, checkAuth, isLoading } = useAuthStore();
  const [guestDisplayName, setGuestDisplayName] = useState<string | null>(null);

  const {
    showMoodModal,
    showUpgradeModal,
    incrementActivity,
    triggerUpgradePrompt,
    completeMoodCheckIn,
    closeMoodModal,
    closeUpgradeModal,
  } = useGuestPrompts();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (isGuest) {
      const displayName = getGuestDisplayName();
      setGuestDisplayName(displayName);
      // Track page visit as activity
      incrementActivity();
    }
  }, [isGuest, incrementActivity]); // Now safe with useCallback

  useEffect(() => {
    if (isAuthenticated && !isGuest && !isLoading) {
      router.push("/dashboard");
    }
  }, [isAuthenticated, isGuest, isLoading, router]);

  useEffect(() => {
    // Only redirect if we're not loading and definitely not authenticated or guest
    if (!isLoading && !isGuest && !isAuthenticated) {
      router.push("/auth/login");
    }
  }, [isGuest, isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  if (!isGuest && !isAuthenticated) {
    return null; // Will redirect
  }

  const guestFeatures = [
    {
      icon: MessageCircle,
      title: "View Chatrooms",
      description:
        "Browse active community discussions and see what others are talking about",
      link: "/chat",
      available: true,
      note: "Read-only access",
    },
    {
      icon: BookOpen,
      title: "Resource Library",
      description:
        "Access self-help articles, blogs, videos, and mental health resources",
      link: "/resources",
      available: true,
      note: "Full access",
    },
    {
      icon: UserCheck,
      title: "Counselor Profiles",
      description:
        "Browse and view detailed profiles of our licensed counselors",
      link: "/counselors",
      available: true,
      note: "View profiles only",
    },
  ];

  const premiumFeatures = [
    {
      icon: Calendar,
      title: "Book 1-on-1 Sessions",
      description:
        "Schedule private counseling sessions with licensed professionals",
      restricted: true,
    },
    {
      icon: MessageCircle,
      title: "Join Chat Discussions",
      description:
        "Actively participate in community chatrooms and connect with others",
      restricted: true,
    },
    {
      icon: Star,
      title: "Reviews & Ratings",
      description:
        "Leave reviews and ratings for counselors to help the community",
      restricted: true,
    },
    {
      icon: Heart,
      title: "Save Preferences",
      description: "Save your favorite counselors, resources, and chat history",
      restricted: true,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Banner */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg text-white p-8 mb-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl font-bold mb-4">
              Welcome to Theramea
              {guestDisplayName ? `, ${guestDisplayName}` : ", Guest"}! 👋
            </h1>
            <p className="text-lg text-purple-100 mb-6">
              Explore our platform and discover mental health resources. Create
              an account anytime to unlock the full experience.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/auth/signup"
                className="bg-white text-purple-700 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors inline-flex items-center justify-center"
              >
                Create Free Account
                <ChevronRight className="ml-2 h-5 w-5" />
              </Link>
              <Link
                href="/auth/login"
                className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-700 transition-colors inline-flex items-center justify-center"
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Available Features */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Users className="h-6 w-6 text-green-600 mr-2" />
              Available Now
            </h2>
            <div className="space-y-4">
              {guestFeatures.map((feature, index) => (
                <Link
                  key={index}
                  href={feature.link}
                  className="block bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow border border-gray-200"
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <feature.icon className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-2">
                        {feature.description}
                      </p>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {feature.note}
                      </span>
                    </div>
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Premium Features */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Lock className="h-6 w-6 text-purple-600 mr-2" />
              Unlock with Account
            </h2>
            <div className="space-y-4">
              {premiumFeatures.map((feature, index) => (
                <button
                  key={index}
                  onClick={triggerUpgradePrompt}
                  className="w-full bg-white rounded-lg p-6 shadow-sm border border-gray-200 relative hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start opacity-75 text-left">
                    <div className="flex-shrink-0">
                      <feature.icon className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-lg font-semibold text-gray-700 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-500 mb-2">
                        {feature.description}
                      </p>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Requires account
                      </span>
                    </div>
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-6 bg-purple-50 border border-purple-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-purple-900 mb-2">
                Ready to get started?
              </h3>
              <p className="text-purple-700 mb-4">
                Create your free account to access all platform features and
                start your mental health journey.
              </p>
              <Link
                href="/auth/signup"
                className="bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors inline-flex items-center"
              >
                Sign Up Free
                <ChevronRight className="ml-2 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>

        {/* Quick Access Grid */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Start Exploring
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <Link
              href="/chat"
              className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center hover:bg-blue-100 transition-colors"
            >
              <MessageCircle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Browse Chatrooms
              </h3>
              <p className="text-blue-700 text-sm">See community discussions</p>
            </Link>

            <Link
              href="/resources"
              className="bg-green-50 border border-green-200 rounded-lg p-6 text-center hover:bg-green-100 transition-colors"
            >
              <BookOpen className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                Resource Library
              </h3>
              <p className="text-green-700 text-sm">
                Self-help articles & videos
              </p>
            </Link>

            <Link
              href="/counselors"
              className="bg-purple-50 border border-purple-200 rounded-lg p-6 text-center hover:bg-purple-100 transition-colors"
            >
              <UserCheck className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-purple-900 mb-2">
                Meet Counselors
              </h3>
              <p className="text-purple-700 text-sm">
                Browse professional profiles
              </p>
            </Link>
          </div>
        </div>
      </div>

      {/* Guest Modals */}
      <GuestMoodModal
        isOpen={showMoodModal}
        onClose={closeMoodModal}
        onComplete={completeMoodCheckIn}
      />
      <GuestUpgradeModal
        isOpen={showUpgradeModal}
        onClose={closeUpgradeModal}
      />
    </div>
  );
}

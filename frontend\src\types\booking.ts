// Booking and session types

export interface Session {
  _id: string;
  userId: string;
  counselorId: string;
  scheduledAt: string;
  duration: number;
  status:
    | "scheduled"
    | "completed"
    | "cancelled"
    | "in-progress"
    | "no-show"
    | "rescheduled";
  type: "individual" | "group" | "couples" | "family";
  pricing: {
    baseAmount: number;
    platformFee: number;
    totalAmount: number;
    currency: string;
  };
  notes?: {
    counselorNotes?: string;
    sessionSummary?: string;
    nextSteps?: string;
    isPrivate: boolean;
  };
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    profilePicture?: string;
  };
  counselor?: {
    _id: string;
    user?: {
      firstName: string;
      lastName: string;
      email: string;
    };
    bio: string;
    specializations: string[];
  };
  meetingLink?: string;
  feedback?: SessionFeedback;
  rescheduledFrom?: string;
  completedAt?: string;
  cancellationReason?: string;
  isUrgent?: boolean;
  sessionType?: string;
  totalAmount?: number;
  currency?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SessionFeedback {
  rating: number;
  comment?: string;
  categories?: {
    communication: number;
    helpfulness: number;
    professionalism: number;
    wouldRecommend: boolean;
  };
  isAnonymous: boolean;
  submittedAt: string;
}

export interface BookingRequest {
  counselorId: string;
  scheduledAt: string;
  duration: number;
  sessionType: "individual" | "group" | "couples" | "family";
  isUrgent?: boolean;
  notes?: string;
  preferredLanguage?: string;
  timezone?: string;
}

export interface BookingResponse {
  success: boolean;
  message: string;
  data: {
    session: Session;
    paymentRequired: boolean;
    paymentUrl?: string;
  };
}

export interface AvailabilitySlot {
  date: string;
  time: string;
  available: boolean;
  counselorId: string;
}

export interface BookingFilters {
  status?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  counselorId?: string;
  search?: string;
}

export interface PaginatedBookings {
  sessions: Session[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export type SessionStatus = Session["status"];
export type SessionType = Session["type"];

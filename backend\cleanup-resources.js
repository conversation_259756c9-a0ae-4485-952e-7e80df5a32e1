const mongoose = require("mongoose");

const MONGODB_URI = "mongodb://theramea-mongodb:27017/theramea_dev";

const resourceSchema = new mongoose.Schema({}, { strict: false });
const Resource = mongoose.model("Resource", resourceSchema);

async function cleanup() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Remove the duplicate resource with slug "5"
    const result = await Resource.deleteOne({_id: '689cbfd3725c25de81c0bbbf'});
    console.log(`🗑️ Removed ${result.deletedCount} duplicate resource(s)`);

    // List remaining resources
    const resources = await Resource.find({}, "_id title seo.slug seoData.slug").lean();
    console.log(`📚 Remaining ${resources.length} resources:`);
    resources.forEach((doc, i) => {
      console.log(`${i+1}. ID: ${doc._id} | Title: ${doc.title}`);
    });

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("👋 Disconnected from MongoDB");
  }
}

cleanup();

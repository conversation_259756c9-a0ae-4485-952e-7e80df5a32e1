import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import {
  Phone,
  MessageCircle,
  AlertTriangle,
  Heart,
  Clock,
  MapPin,
} from "lucide-react";
import Link from "next/link";

const crisisResources = [
  {
    name: "National Suicide Prevention Lifeline",
    phone: "988",
    description: "24/7 free and confidential support for people in distress",
  },
  {
    name: "Crisis Text Line",
    phone: "Text HOME to 741741",
    description: "Free, 24/7 crisis support via text message",
  },
  {
    name: "National Domestic Violence Hotline",
    phone: "**************",
    description: "24/7 confidential support for domestic violence survivors",
  },
  {
    name: "SAMHSA National Helpline",
    phone: "**************",
    description: "Treatment referral and information service",
  },
];

const warningSignsPersonal = [
  "Thoughts of self-harm or suicide",
  "Feeling hopeless or trapped",
  "Severe mood swings",
  "Withdrawal from friends and activities",
  "Changes in eating or sleeping patterns",
  "Increased use of alcohol or drugs",
];

const warningSignsOthers = [
  "Talking about wanting to die or hurt themselves",
  "Expressing feelings of hopelessness",
  "Talking about being trapped or in unbearable pain",
  "Talking about being a burden to others",
  "Increasing use of alcohol or drugs",
  "Acting anxious or agitated",
];

export default function CrisisPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="bg-red-600 text-white py-4">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center gap-2">
            <AlertTriangle className="h-6 w-6" />
            <span className="font-semibold text-lg">
              If you're in immediate danger, call 911 or go to your nearest
              emergency room
            </span>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <Heart className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            Crisis Support
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            You are not alone. If you're experiencing a mental health crisis,
            immediate help is available 24/7.
          </p>
        </div>
      </section>

      {/* Immediate Help Section */}
      <section className="py-12 bg-blue-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            Immediate Crisis Resources
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto">
            {crisisResources.map((resource, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-bold mb-3 text-gray-800">
                  {resource.name}
                </h3>
                <div className="flex items-center gap-2 mb-3">
                  <Phone className="h-5 w-5 text-blue-600" />
                  <span className="text-2xl font-bold text-blue-600">
                    {resource.phone}
                  </span>
                </div>
                <p className="text-gray-600">{resource.description}</p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <div className="bg-white p-8 rounded-lg shadow-md max-w-2xl mx-auto">
              <MessageCircle className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-4 text-gray-800">
                Theramea Crisis Support
              </h3>
              <p className="text-gray-600 mb-6">
                Our trained crisis counselors are available 24/7 for immediate
                support
              </p>
              <div className="space-y-4">
                <div className="flex items-center justify-center gap-4">
                  <Phone className="h-5 w-5 text-purple-600" />
                  <span className="text-xl font-bold text-purple-600">
                    1-800-CRISIS-T (**************)
                  </span>
                </div>
                <button className="bg-purple-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-purple-700 transition-colors">
                  Start Crisis Chat
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Warning Signs Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            Warning Signs to Watch For
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <div className="bg-gray-50 p-8 rounded-lg">
              <h3 className="text-2xl font-bold mb-6 text-gray-800">
                Warning Signs in Yourself
              </h3>
              <ul className="space-y-3">
                {warningSignsPersonal.map((sign, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-700">{sign}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-gray-50 p-8 rounded-lg">
              <h3 className="text-2xl font-bold mb-6 text-gray-800">
                Warning Signs in Others
              </h3>
              <ul className="space-y-3">
                {warningSignsOthers.map((sign, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-700">{sign}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Safety Planning Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
              Creating a Safety Plan
            </h2>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <p className="text-lg text-gray-600 mb-8 text-center">
                A safety plan is a prioritized written list of coping strategies
                and sources of support.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-bold mb-4 text-gray-800">
                    Include in Your Safety Plan:
                  </h3>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Warning signs that crisis may be approaching</li>
                    <li>• Coping strategies you can use alone</li>
                    <li>
                      • People and social settings that provide distraction
                    </li>
                    <li>• People you can reach out to for help</li>
                    <li>• Professional contacts and agencies</li>
                    <li>• How to make your environment safe</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-4 text-gray-800">
                    Emergency Contacts:
                  </h3>
                  <div className="space-y-3 text-gray-700">
                    <div>
                      <strong>Emergency Services:</strong> 911
                    </div>
                    <div>
                      <strong>National Suicide Prevention:</strong> 988
                    </div>
                    <div>
                      <strong>Crisis Text Line:</strong> Text HOME to 741741
                    </div>
                    <div>
                      <strong>Theramea Crisis Line:</strong> 1-800-CRISIS-T
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-center mt-8">
                <button className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors">
                  Download Safety Plan Template
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Resources Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8 text-gray-800">
            Additional Resources
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Link href="/counselors" className="group">
              <div className="bg-purple-50 p-6 rounded-lg hover:bg-purple-100 transition-colors">
                <Clock className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 group-hover:text-purple-600">
                  Find a Counselor
                </h3>
                <p className="text-gray-600">
                  Connect with licensed mental health professionals
                </p>
              </div>
            </Link>

            <Link href="/resources" className="group">
              <div className="bg-green-50 p-6 rounded-lg hover:bg-green-100 transition-colors">
                <Heart className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600">
                  Mental Health Resources
                </h3>
                <p className="text-gray-600">
                  Educational materials and self-help tools
                </p>
              </div>
            </Link>

            <Link href="/chatrooms" className="group">
              <div className="bg-blue-50 p-6 rounded-lg hover:bg-blue-100 transition-colors">
                <MessageCircle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 group-hover:text-blue-600">
                  Support Communities
                </h3>
                <p className="text-gray-600">
                  Connect with others in moderated chat rooms
                </p>
              </div>
            </Link>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
}

"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuthStore } from "@/store/authStore";

export function useGuestPrompts() {
  const { isGuest, isAuthenticated } = useAuthStore();
  const [showMoodModal, setShowMoodModal] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [activityCount, setActivityCount] = useState(0);

  useEffect(() => {
    if (!isGuest || !isAuthenticated) return;

    // Track activity from localStorage
    const stored = localStorage.getItem("guest_activity_count");
    const count = stored ? parseInt(stored) : 0;
    setActivityCount(count);

    // Show mood check-in after significant engagement (e.g., 5 activities)
    if (count >= 5 && !localStorage.getItem("guest_mood_completed")) {
      setShowMoodModal(true);
    }
  }, [isGuest, isAuthenticated]);

  const incrementActivity = useCallback(() => {
    if (!isGuest) return;

    setActivityCount((prevCount) => {
      const newCount = prevCount + 1;
      localStorage.setItem("guest_activity_count", newCount.toString());

      // Trigger mood check after 5 activities
      if (newCount >= 5 && !localStorage.getItem("guest_mood_completed")) {
        setShowMoodModal(true);
      }

      return newCount;
    });
  }, [isGuest]);

  const triggerUpgradePrompt = useCallback(() => {
    if (!isGuest) return;
    setShowUpgradeModal(true);
  }, [isGuest]);

  const completeMoodCheckIn = useCallback((mood: number, note?: string) => {
    const moodData = {
      mood,
      note,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem("guest_mood", JSON.stringify(moodData));
    localStorage.setItem("guest_mood_completed", "true");
    setShowMoodModal(false);
  }, []);

  const closeMoodModal = useCallback(() => {
    setShowMoodModal(false);
    // Mark as dismissed for this session
    localStorage.setItem("guest_mood_dismissed", "true");
  }, []);

  const closeUpgradeModal = useCallback(() => {
    setShowUpgradeModal(false);
  }, []);

  return {
    showMoodModal,
    showUpgradeModal,
    incrementActivity,
    triggerUpgradePrompt,
    completeMoodCheckIn,
    closeMoodModal,
    closeUpgradeModal,
    activityCount,
  };
}
